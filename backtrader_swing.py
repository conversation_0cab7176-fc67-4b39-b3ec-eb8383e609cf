import backtrader as bt
import ccxt
import pandas as pd
import os
from datetime import datetime, timedelta
import talib
import numpy as np
from scipy import stats

class RSI_WMA_ADX_Strategy(bt.Strategy):
    params = (
        ('rsi_period', 14),
        ('wma_period', 50),
        ('adx_period', 14),
        ('atr_period', 14),
        ('stop_loss_atr', 2),
        ('take_profit_atr', 3),
        ('risk_per_trade', 0.10),  # 10% do capital
        ('capital', 500),
    )
    
    def __init__(self):
        # Indicadores
        self.rsi = bt.indicators.RSI(self.data.close, period=self.params.rsi_period)
        self.wma = bt.indicators.WeightedMovingAverage(self.data.close, period=self.params.wma_period)
        self.adx = bt.indicators.AverageDirectionalMovementIndex(period=self.params.adx_period)
        self.atr = bt.indicators.ATR(period=self.params.atr_period)
        
        # Variáveis de controle
        self.order = None
        self.stop_loss_price = None
        self.take_profit_price = None
        self.trade_results = []  # Para armazenar resultados dos trades
        
    def next(self):
        # Se já temos uma ordem pendente, não fazer nada
        if self.order:
            return
            
        # Se não temos posição aberta
        if not self.position:
            # Condições de entrada
            if (self.rsi[0] > 50 and 
                self.data.close[0] > self.wma[0] and
                self.adx[0] > 25):  # ADX > 25 confirma tendência forte
                
                # Calcular tamanho da posição baseado no ATR
                risk_amount = self.params.capital * self.params.risk_per_trade
                atr_value = self.atr[0]
                stop_distance = self.params.stop_loss_atr * atr_value
                
                # Tamanho da posição = Risk Amount / Stop Distance
                position_size = risk_amount / stop_distance
                
                # Converter para número de ações/contratos
                shares = int(position_size / self.data.close[0])
                
                if shares > 0:
                    # Executar ordem de compra
                    self.order = self.buy(size=shares)
                    
                    # Calcular preços de stop loss e take profit
                    entry_price = self.data.close[0]
                    self.stop_loss_price = entry_price - stop_distance
                    self.take_profit_price = entry_price + (self.params.take_profit_atr * atr_value)
                    
                    print(f"COMPRA: {shares} ações a {entry_price:.2f}")
                    print(f"Stop Loss: {self.stop_loss_price:.2f}")
                    print(f"Take Profit: {self.take_profit_price:.2f}")
        
        # Se temos posição aberta, verificar condições de saída
        elif self.position:
            current_price = self.data.close[0]
            
            # Stop Loss
            if current_price <= self.stop_loss_price:
                self.order = self.sell(size=self.position.size)
                print(f"STOP LOSS ativado a {current_price:.2f}")
                
            # Take Profit
            elif current_price >= self.take_profit_price:
                self.order = self.sell(size=self.position.size)
                print(f"TAKE PROFIT ativado a {current_price:.2f}")
    
    def notify_order(self, order):
        if order.status in [order.Submitted, order.Accepted]:
            return
            
        if order.status in [order.Completed]:
            if order.isbuy():
                print(f"COMPRA EXECUTADA: {order.executed.size} a {order.executed.price:.2f}")
            elif order.issell():
                print(f"VENDA EXECUTADA: {order.executed.size} a {order.executed.price:.2f}")
                
        self.order = None
    
    def notify_trade(self, trade):
        if trade.isclosed:
            # Armazenar resultado do trade para métricas avançadas
            self.trade_results.append(trade.pnl)
            print(f"TRADE FECHADO: PnL = {trade.pnl:.2f}")

def get_okx_data(symbol='BTC/USDC', timeframe='4h', limit=300):
    """
    Busca dados da OKX usando ccxt
    """
    # Carregar credenciais do arquivo .env
    from dotenv import load_dotenv
    load_dotenv()
    
    # Configurar exchange
    exchange = ccxt.myokx({
        'apiKey': os.getenv('live_okx_apiKey'),
        'secret': os.getenv('live_okx_secret'),
        'password': os.getenv('live_okx_password'),
        'sandbox': False,  # False para live
        'enableRateLimit': True,
    })
    
    # Buscar dados OHLCV
    ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
    
    # Converter para DataFrame
    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
    df.set_index('timestamp', inplace=True)
    
    return df

class PandasData(bt.feeds.PandasData):
    """
    Classe para usar dados do pandas com Backtrader
    """
    lines = ('close', 'volume',)
    params = (
        ('datetime', None),
        ('open', 'open'),
        ('high', 'high'),
        ('low', 'low'),
        ('close', 'close'),
        ('volume', 'volume'),
        ('openinterest', None),
    )

def run_backtest():
    # Criar engine do Backtrader
    cerebro = bt.Cerebro()
    
    # Buscar dados da OKX
    print("Buscando dados da OKX...")
    df = get_okx_data('BTC/USDC', '4h', 300)
    
    # Criar data feed
    data = PandasData(dataname=df)
    cerebro.adddata(data)
    
    # Adicionar estratégia
    cerebro.addstrategy(RSI_WMA_ADX_Strategy)
    
    # Configurar capital inicial
    cerebro.broker.setcash(500.0)
    
    # Configurar comissões OKX - Maker/Taker fees
    cerebro.broker.setcommission(
        commission=0.0008,  # 0.08% maker fee (default)
        commtype=bt.CommInfoBase.COMM_PERC,
        stocklike=True
    )
    
    # Para fees diferenciadas maker/taker, você pode usar:
    # cerebro.broker.setcommission(commission=0.0001)  # 0.01% taker fee para ordens market
    
    # Adicionar analisadores
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
    cerebro.addanalyzer(bt.analyzers.SQN, _name='sqn')  # System Quality Number
    cerebro.addanalyzer(bt.analyzers.VWR, _name='vwr')  # Variability-Weighted Return
    cerebro.addanalyzer(bt.analyzers.Calmar, _name='calmar')  # Calmar Ratio
    cerebro.addanalyzer(bt.analyzers.TimeReturn, _name='time_return')
    cerebro.addanalyzer(bt.analyzers.PositionsValue, _name='positions')
    cerebro.addanalyzer(bt.analyzers.GrossLeverage, _name='leverage')
    
    print(f"Capital Inicial: {cerebro.broker.getvalue():.2f}€")
    
    # Executar backtest
    results = cerebro.run()
    
    # Análise completa com comentários
    strat = results[0]
    analyze_results(strat, cerebro, 500)
    
def calculate_advanced_metrics(trade_results, returns, initial_capital=500):
    """
    Calcula métricas avançadas: Kelly Criterion, Omega Ratio, VaR, Tail Ratio
    """
    if not trade_results or len(trade_results) < 2:
        return {
            'kelly_criterion': 'N/A',
            'omega_ratio': 'N/A',
            'var_95': 'N/A',
            'var_99': 'N/A',
            'tail_ratio': 'N/A'
        }
    
    trade_results = np.array(trade_results)
    
    # Kelly Criterion
    wins = trade_results[trade_results > 0]
    losses = trade_results[trade_results < 0]
    
    if len(wins) > 0 and len(losses) > 0:
        win_rate = len(wins) / len(trade_results)
        avg_win = np.mean(wins)
        avg_loss = abs(np.mean(losses))
        
        # Kelly = (bp - q) / b
        # onde b = avg_win/avg_loss, p = win_rate, q = 1-win_rate
        b = avg_win / avg_loss
        p = win_rate
        q = 1 - win_rate
        kelly = (b * p - q) / b
        kelly_pct = kelly * 100
    else:
        kelly_pct = 'N/A'
    
    # Omega Ratio (usando threshold = 0)
    threshold = 0
    gains = trade_results[trade_results > threshold] - threshold
    losses = threshold - trade_results[trade_results < threshold]
    
    if len(gains) > 0 and len(losses) > 0:
        omega_ratio = np.sum(gains) / np.sum(losses)
    else:
        omega_ratio = 'N/A'
    
    # Value at Risk (VaR)
    if len(trade_results) > 0:
        var_95 = np.percentile(trade_results, 5)
        var_99 = np.percentile(trade_results, 1)
    else:
        var_95 = 'N/A'
        var_99 = 'N/A'
    
    # Tail Ratio
    if len(trade_results) > 0:
        percentile_95 = np.percentile(trade_results, 95)
        percentile_5 = np.percentile(trade_results, 5)
        tail_ratio = percentile_95 / abs(percentile_5) if percentile_5 != 0 else 'N/A'
    else:
        tail_ratio = 'N/A'
    
    return {
        'kelly_criterion': kelly_pct,
        'omega_ratio': omega_ratio,
        'var_95': var_95,
        'var_99': var_99,
        'tail_ratio': tail_ratio
    }

def analyze_results(strat, cerebro, initial_capital=500):
    """
    Analisa e imprime todos os resultados com comentários
    """
    print("\n" + "="*60)
    print("ANÁLISE COMPLETA DA ESTRATÉGIA")
    print("="*60)
    
    # Obter resultados dos trades
    trade_results = getattr(strat, 'trade_results', [])
    
    # Calcular métricas avançadas
    advanced_metrics = calculate_advanced_metrics(trade_results, None, initial_capital)
    
    # Métricas básicas
    trades = strat.analyzers.trades.get_analysis()
    drawdown = strat.analyzers.drawdown.get_analysis()
    sharpe = strat.analyzers.sharpe.get_analysis()
    
    total_trades = trades.get('total', {}).get('total', 0)
    won_trades = trades.get('won', {}).get('total', 0)
    lost_trades = trades.get('lost', {}).get('total', 0)
    profit_factor = 'N/A'
    win_rate = 0
    
    final_value = cerebro.broker.getvalue()
    total_return_pct = ((final_value / initial_capital) - 1) * 100
    
    print(f"💰 PERFORMANCE FINANCEIRA")
    print(f"   Capital Inicial: {initial_capital:.2f}€")
    print(f"   Capital Final: {final_value:.2f}€")
    print(f"   Retorno Total: {total_return_pct:.2f}%")
    
    # Comentários sobre performance
    if total_return_pct > 20:
        print("   ✅ EXCELENTE: Retorno superior a 20%")
    elif total_return_pct > 10:
        print("   ✅ BOM: Retorno superior a 10%")
    elif total_return_pct > 0:
        print("   ⚠️  MODERADO: Retorno positivo mas baixo")
    else:
        print("   ❌ NEGATIVO: Estratégia com prejuízo")
    
    print(f"\n📊 ANÁLISE DE TRADES")
    print(f"   Total de Trades: {total_trades}")
    print(f"   Trades Ganhos: {won_trades}")
    print(f"   Trades Perdidos: {lost_trades}")
    
    if total_trades > 0:
        win_rate = (won_trades / total_trades) * 100
        print(f"   Win Rate: {win_rate:.2f}%")
        
        # Comentários sobre win rate
        if win_rate >= 60:
            print("   ✅ EXCELENTE: Win rate muito alto (>60%)")
        elif win_rate >= 50:
            print("   ✅ BOM: Win rate acima de 50%")
        elif win_rate >= 40:
            print("   ⚠️  ACEITÁVEL: Win rate moderado (40-50%)")
        else:
            print("   ❌ BAIXO: Win rate abaixo de 40%")
        
        # Profit Factor
        total_won = trades.get('won', {}).get('pnl', {}).get('total', 0)
        total_lost = abs(trades.get('lost', {}).get('pnl', {}).get('total', 0))
        profit_factor = total_won / total_lost if total_lost > 0 else 'N/A'
        print(f"   Profit Factor: {profit_factor:.2f}")
        
        # Comentários sobre profit factor
        if isinstance(profit_factor, (int, float)):
            if profit_factor >= 2.0:
                print("   ✅ EXCELENTE: Profit Factor muito alto (>2.0)")
            elif profit_factor >= 1.5:
                print("   ✅ BOM: Profit Factor sólido (1.5-2.0)")
            elif profit_factor >= 1.0:
                print("   ⚠️  ACEITÁVEL: Profit Factor positivo mas baixo")
            else:
                print("   ❌ NEGATIVO: Profit Factor abaixo de 1.0")
    
    print(f"\n🎯 MÉTRICAS AVANÇADAS")
    
    # Kelly Criterion
    kelly = advanced_metrics['kelly_criterion']
    print(f"   Kelly Criterion: {kelly}%")
    if isinstance(kelly, (int, float)):
        if kelly > 25:
            print("   ⚠️  ATENÇÃO: Kelly muito alto (>25%), reduzir posição")
        elif kelly > 10:
            print("   ✅ BOM: Kelly saudável (10-25%)")
        elif kelly > 0:
            print("   ✅ POSITIVO: Kelly baixo mas positivo")
        else:
            print("   ❌ NEGATIVO: Kelly negativo, estratégia não rentável")
    
    # Omega Ratio
    omega = advanced_metrics['omega_ratio']
    if isinstance(omega, (int, float)):
        print(f"   Omega Ratio: {omega:.2f}")
        if omega >= 1.5:
            print("   ✅ EXCELENTE: Omega Ratio muito bom (>1.5)")
        elif omega >= 1.0:
            print("   ✅ BOM: Omega Ratio positivo")
        else:
            print("   ❌ NEGATIVO: Omega Ratio abaixo de 1.0")
    else:
        print(f"   Omega Ratio: {omega}")
    
    # Value at Risk
    var_95 = advanced_metrics['var_95']
    var_99 = advanced_metrics['var_99']
    if isinstance(var_95, (int, float)):
        print(f"   VaR 95%: {var_95:.2f}€")
    else:
        print(f"   VaR 95%: {var_95}")
    if isinstance(var_99, (int, float)):
        print(f"   VaR 99%: {var_99:.2f}€")
    else:
        print(f"   VaR 99%: {var_99}")
    
    if isinstance(var_95, (int, float)):
        var_95_pct = (var_95 / initial_capital) * 100
        if var_95_pct > -2:
            print("   ✅ BAIXO RISCO: VaR 95% inferior a 2% do capital")
        elif var_95_pct > -5:
            print("   ⚠️  RISCO MODERADO: VaR 95% entre 2-5% do capital")
        else:
            print("   ❌ ALTO RISCO: VaR 95% superior a 5% do capital")
    
    # Tail Ratio
    tail_ratio = advanced_metrics['tail_ratio']
    if isinstance(tail_ratio, (int, float)):
        print(f"   Tail Ratio: {tail_ratio:.2f}")
        if tail_ratio >= 2.0:
            print("   ✅ EXCELENTE: Tail Ratio muito bom (>2.0)")
        elif tail_ratio >= 1.0:
            print("   ✅ BOM: Tail Ratio positivo")
        else:
            print("   ❌ NEGATIVO: Tail Ratio abaixo de 1.0")
    else:
        print(f"   Tail Ratio: {tail_ratio}")
    
    print(f"\n⚡ ANÁLISE DE RISCO")
    
    # Sharpe Ratio
    sharpe_ratio = sharpe.get('sharperatio', 'N/A')
    print(f"   Sharpe Ratio: {sharpe_ratio}")
    if isinstance(sharpe_ratio, (int, float)):
        if sharpe_ratio >= 2.0:
            print("   ✅ EXCELENTE: Sharpe Ratio muito alto (>2.0)")
        elif sharpe_ratio >= 1.0:
            print("   ✅ BOM: Sharpe Ratio sólido (1.0-2.0)")
        elif sharpe_ratio >= 0.5:
            print("   ⚠️  MODERADO: Sharpe Ratio aceitável (0.5-1.0)")
        else:
            print("   ❌ BAIXO: Sharpe Ratio abaixo de 0.5")
    
    # Max Drawdown
    max_dd = drawdown.get('max', {}).get('drawdown', 'N/A')
    print(f"   Max Drawdown: {max_dd:.2f}%")
    if isinstance(max_dd, (int, float)):
        if max_dd <= 5:
            print("   ✅ EXCELENTE: Drawdown muito baixo (<5%)")
        elif max_dd <= 10:
            print("   ✅ BOM: Drawdown aceitável (5-10%)")
        elif max_dd <= 20:
            print("   ⚠️  MODERADO: Drawdown preocupante (10-20%)")
        else:
            print("   ❌ ALTO: Drawdown muito alto (>20%)")
    
    print(f"\n💡 RECOMENDAÇÕES")
    
    # Recomendações baseadas nos resultados
    recommendations = []
    
    if total_trades < 10:
        recommendations.append("⚠️  Poucos trades para conclusões definitivas")
    
    if isinstance(kelly, (int, float)) and kelly > 25:
        recommendations.append("🔧 Reduzir tamanho da posição (Kelly muito alto)")
    
    if isinstance(profit_factor, (int, float)) and profit_factor < 1.2:
        recommendations.append("🔧 Melhorar critérios de entrada/saída")
    
    if isinstance(max_dd, (int, float)) and max_dd > 15:
        recommendations.append("🔧 Implementar melhor gestão de risco")
    
    if win_rate < 40:
        recommendations.append("🔧 Revisar sinais de entrada")
    
    if total_return_pct < 10:
        recommendations.append("🔧 Otimizar parâmetros da estratégia")
    
    if not recommendations:
        recommendations.append("✅ Estratégia apresenta métricas saudáveis")
    
    for rec in recommendations:
        print(f"   {rec}")
    
    print(f"\n🎯 CONCLUSÃO GERAL")
    
    # Pontuação geral
    score = 0
    max_score = 7
    
    if total_return_pct > 10: score += 1
    if isinstance(win_rate, (int, float)) and win_rate >= 50: score += 1
    if isinstance(profit_factor, (int, float)) and profit_factor >= 1.5: score += 1
    if isinstance(sharpe_ratio, (int, float)) and sharpe_ratio >= 1.0: score += 1
    if isinstance(max_dd, (int, float)) and max_dd <= 10: score += 1
    if isinstance(kelly, (int, float)) and 0 < kelly <= 25: score += 1
    if isinstance(omega, (int, float)) and omega >= 1.0: score += 1
    
    percentage_score = (score / max_score) * 100
    
    print(f"   Pontuação: {score}/{max_score} ({percentage_score:.1f}%)")
    
    if percentage_score >= 80:
        print("   ✅ ESTRATÉGIA EXCELENTE - Recomendada para live trading")
    elif percentage_score >= 60:
        print("   ✅ ESTRATÉGIA BOA - Pode ser usada com cautela")
    elif percentage_score >= 40:
        print("   ⚠️  ESTRATÉGIA MODERADA - Precisa de otimizações")
    else:
        print("   ❌ ESTRATÉGIA FRACA - Não recomendada")
    
    print("="*60)
    
    # Plot dos resultados (opcional)
    cerebro.plot()

if __name__ == '__main__':
    run_backtest()
