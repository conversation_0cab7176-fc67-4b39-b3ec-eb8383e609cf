// K-means Clustering

// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © Bluetuga

//@version=6
indicator("»» Magic Pro A 2025 - K-means Clustering", "Pro A AI", overlay=true)

// ───── INPUT PARAMETERS ────────────────────────────────────────────────────────────────
// Indicator Parameters
wma_length      = input.int(50,  "WMA Length",       minval=1)   // Comprimento da média móvel ponderada
sma_length      = input.int(50,  "SMA Length",       minval=1)   // Comprimento da média móvel simples curta
sma200_length   = input.int(200, "SMA 200 Length",   minval=1)   // Comprimento da média móvel simples de 200 períodos
rsi_length      = input.int(30,  "RSI Length",       minval=1)   // Comprimento do RSI
rsi_threshold   = input.float(50,"RSI Threshold",    minval=1, maxval=100) // Limiar do RSI

// K-means Clustering Parameters (Precomputed externally, e.g., in Python)
// These are placeholder values for cluster centroids or thresholds derived from historical data analysis
// Cluster 1 represents a bullish pattern, Cluster 2 a bearish pattern, Cluster 3 a neutral/range-bound pattern
use_cluster_filter = input.bool(false, "Use K-means Cluster Filter", tooltip="Filter signals based on precomputed K-means clusters")
cluster1_rsi = input.float(70, "Cluster 1 RSI Centroid", minval=0, maxval=100, tooltip="RSI centroid for bullish cluster")
cluster2_rsi = input.float(30, "Cluster 2 RSI Centroid", minval=0, maxval=100, tooltip="RSI centroid for bearish cluster")
cluster3_rsi = input.float(50, "Cluster 3 RSI Centroid", minval=0, maxval=100, tooltip="RSI centroid for neutral cluster")
cluster_threshold = input.float(10, "Cluster Threshold", minval=0, tooltip="Threshold for determining cluster membership based on RSI distance")

// Opções de filtros
use_sma200_filter = input.bool(true, "Use SMA200 Filter", tooltip="Adiciona confirmação de tendência com SMA200")
use_volume_filter = input.bool(false, "Use Volume Filter", tooltip="Confirma sinais com volume acima da média")
volume_length     = input.int(20, "Volume MA Length", minval=1)

// Configurações de alertas
enable_alerts = input.bool(true, "Enable Alerts")

// ───── INDICATOR CALCULATIONS ──────────────────────────────────────────────────────────
// Médias móveis
wma_value       = ta.wma(close, wma_length)
sma_value       = ta.sma(close, sma_length)
sma200_value    = ta.sma(close, sma200_length)

// RSI
rsi_value       = ta.rsi(close, rsi_length)

// VWAP nativo (mais eficiente)
vwap_value      = ta.vwap(hlc3)

// Volume filter
volume_ma       = ta.sma(volume, volume_length)
volume_condition = not use_volume_filter or volume > volume_ma

// ───── TREND LOGIC & CONDITIONS ────────────────────────────────────────────────────────
// Condições base
base_bullish = close > wma_value and rsi_value > rsi_threshold
base_bearish = close < wma_value and rsi_value < rsi_threshold

// K-means Cluster Logic
// Determine which cluster the current data point belongs to based on RSI distance to centroids
// This is a simplified approach; in practice, clusters would be determined by multiple features in external analysis
cluster1_distance = math.abs(rsi_value - cluster1_rsi)
cluster2_distance = math.abs(rsi_value - cluster2_rsi)
cluster3_distance = math.abs(rsi_value - cluster3_rsi)

// Find the closest cluster
is_cluster1 = cluster1_distance < cluster_threshold and cluster1_distance < cluster2_distance and cluster1_distance < cluster3_distance
is_cluster2 = cluster2_distance < cluster_threshold and cluster2_distance < cluster1_distance and cluster2_distance < cluster3_distance
is_cluster3 = cluster3_distance < cluster_threshold and cluster3_distance < cluster1_distance and cluster3_distance < cluster2_distance

// Cluster-based signal adjustment (example logic)
cluster_bullish = is_cluster1 and base_bullish
cluster_bearish = is_cluster2 and base_bearish
cluster_neutral = is_cluster3

// Filtros adicionais
sma200_bullish_filter = not use_sma200_filter or close > sma200_value
sma200_bearish_filter = not use_sma200_filter or close < sma200_value

// Condições finais com filtros
bullish_condition = base_bullish and sma200_bullish_filter and volume_condition and (not use_cluster_filter or cluster_bullish)
bearish_condition = base_bearish and sma200_bearish_filter and volume_condition and (not use_cluster_filter or cluster_bearish)

// ───── PLOT INDICATORS ─────────────────────────────────────────────────────────────────
plot(wma_value,     title="WMA",     color=#abbad3, linewidth=1)
plot(sma_value,     title="SMA",     color=#8895aa, linewidth=1)
plot(sma200_value,  title="SMA 200", color=#ff9800, linewidth=2)
plot(vwap_value,    title="VWAP",    color=color.blue, linewidth=2)

// ───── BACKGROUND COLOR ────────────────────────────────────────────────────────────────
bgcolor(bullish_condition ? color.new(color.green, 80) : bearish_condition ? color.new(color.red, 80) : na, title="Trend Background")

// ───── SIGNALS ─────────────────────────────────────────────────────────────────────────
bullish_start = bullish_condition and not bullish_condition[1]
bullish_end   = not bullish_condition and bullish_condition[1]
bearish_start = bearish_condition and not bearish_condition[1]
bearish_end   = not bearish_condition and bearish_condition[1]

// Plot signals
plotshape(bullish_start, title="Bullish Start", location=location.belowbar, 
          color=color.green, style=shape.triangleup, size=size.tiny)

plotshape(bullish_end, title="Bullish End", location=location.abovebar, 
          color=color.red, style=shape.triangledown, size=size.tiny)

plotshape(bearish_start, title="Bearish Start", location=location.abovebar, 
          color=color.red, style=shape.triangledown, size=size.tiny)

plotshape(bearish_end, title="Bearish End", location=location.belowbar, 
          color=color.green, style=shape.triangleup, size=size.tiny)

// ───── ALERTS ──────────────────────────────────────────────────────────────────────────
if enable_alerts
    if bullish_start
        alert("Magic Pro A: BULLISH trend started! Price: " + str.tostring(close, "#.##") + 
              " | RSI: " + str.tostring(rsi_value, "#.#") + 
              (use_cluster_filter and is_cluster1 ? " | Cluster: Bullish" : ""), alert.freq_once_per_bar)
    
    if bearish_start
        alert("Magic Pro A: BEARISH trend started! Price: " + str.tostring(close, "#.##") + 
              " | RSI: " + str.tostring(rsi_value, "#.#") + 
              (use_cluster_filter and is_cluster2 ? " | Cluster: Bearish" : ""), alert.freq_once_per_bar)
    
    if bullish_end
        alert("Magic Pro A: Bullish trend ENDED! Price: " + str.tostring(close, "#.##"), 
              alert.freq_once_per_bar)
    
    if bearish_end
        alert("Magic Pro A: Bearish trend ENDED! Price: " + str.tostring(close, "#.##"), 
              alert.freq_once_per_bar)
