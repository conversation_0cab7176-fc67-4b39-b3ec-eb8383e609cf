//@version=5
indicator("WMA Signals + RSI Filter", shorttitle="WMA-RSI", overlay=true)

// === PARÂMETROS DE ENTRADA ===
wma_length = input.int(21, title="WMA Length", minval=1)
wma_source = input.source(close, title="WMA Source")

rsi_length = input.int(14, title="RSI Length", minval=1)
rsi_sma_length = input.int(14, title="RSI SMA Length", minval=1)

// === CÁLCULOS DOS INDICADORES ===
// WMA (Weighted Moving Average)
wma_value = ta.wma(wma_source, wma_length)

// RSI e RSI SMA
rsi_value = ta.rsi(close, rsi_length)
rsi_sma = ta.sma(rsi_value, rsi_sma_length)

// === SINAIS BASEADOS NA WMA ===
// Sinal de compra: preço cruza acima da WMA
wma_buy_signal = ta.crossover(close, wma_value)

// Sinal de venda: preço cruza abaixo da WMA
wma_sell_signal = ta.crossunder(close, wma_value)

// === FILTRO RSI ===
// RSI acima da sua média móvel indica momentum de alta
rsi_bullish_filter = rsi_value > rsi_sma

// RSI abaixo da sua média móvel indica momentum de baixa
rsi_bearish_filter = rsi_value < rsi_sma

// === SINAIS FINAIS (WMA + FILTRO RSI) ===
// Compra: WMA dá sinal de compra E RSI está acima da sua SMA
buy_condition = wma_buy_signal and rsi_bullish_filter

// Venda: WMA dá sinal de venda E RSI está abaixo da sua SMA
sell_condition = wma_sell_signal and rsi_bearish_filter

// === PLOTAGEM ===
// Linha WMA com largura 1
plot(wma_value, title="WMA", color=color.blue, linewidth=1)

// Sinais de compra e venda filtrados
plotshape(buy_condition, title="Buy Signal", location=location.belowbar, 
          style=shape.triangleup, size=size.small, color=color.green)

plotshape(sell_condition, title="Sell Signal", location=location.abovebar, 
          style=shape.triangledown, size=size.small, color=color.red)

// Sinais da WMA (sem filtro) em cor mais fraca para referência
plotshape(wma_buy_signal and not rsi_bullish_filter, title="WMA Buy (Filtered Out)", 
          location=location.belowbar, style=shape.triangleup, size=size.tiny, color=color.new(color.green, 70))

plotshape(wma_sell_signal and not rsi_bearish_filter, title="WMA Sell (Filtered Out)", 
          location=location.abovebar, style=shape.triangledown, size=size.tiny, color=color.new(color.red, 70))

// === PAINEL SEPARADO PARA RSI ===
// Plotagem do RSI em painel separado
bgcolor(na, title="RSI Panel")

// === ALERTAS ===
alertcondition(buy_condition, title="Filtered Buy Signal", 
               message="Buy Signal: Price crossed above WMA and RSI > RSI SMA")

alertcondition(sell_condition, title="Filtered Sell Signal", 
               message="Sell Signal: Price crossed below WMA and RSI < RSI SMA")

alertcondition(wma_buy_signal, title="WMA Buy Signal", 
               message="WMA Buy: Price crossed above WMA")

alertcondition(wma_sell_signal, title="WMA Sell Signal", 
               message="WMA Sell: Price crossed below WMA")

// === TABELA INFORMATIVA ===
if barstate.islast
    var table info_table = table.new(position.top_right, 2, 6, bgcolor=color.white, border_width=1)
    
    table.cell(info_table, 0, 0, "WMA", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 0, str.tostring(math.round(wma_value, 2)), text_color=color.black)
    
    table.cell(info_table, 0, 1, "Price", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 1, str.tostring(math.round(close, 2)), text_color=color.black)
    
    table.cell(info_table, 0, 2, "RSI", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 2, str.tostring(math.round(rsi_value, 2)), text_color=color.black)
    
    table.cell(info_table, 0, 3, "RSI SMA", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 3, str.tostring(math.round(rsi_sma, 2)), text_color=color.black)
    
    // Status do filtro RSI
    rsi_filter_text = rsi_bullish_filter ? "BULL" : "BEAR"
    rsi_filter_color = rsi_bullish_filter ? color.green : color.red
    table.cell(info_table, 0, 4, "RSI Filter", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 4, rsi_filter_text, text_color=color.white, bgcolor=rsi_filter_color)
    
    // Status do sinal final
    signal_text = buy_condition ? "BUY" : sell_condition ? "SELL" : "WAIT"
    signal_color = buy_condition ? color.green : sell_condition ? color.red : color.yellow
    table.cell(info_table, 0, 5, "Signal", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 5, signal_text, text_color=color.white, bgcolor=signal_color)
