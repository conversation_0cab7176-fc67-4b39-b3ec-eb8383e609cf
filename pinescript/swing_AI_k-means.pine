// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © Bluetuga

//@version=6
indicator('Swing Base - AI', overlay = true)

// - - Inputs
show_swing = input.bool(true, title = '► Swing signal', group = 'Signals')
swing_bars = input.int(3, title = 'Swing Lookback - Resistance', group = 'Input Data')
swing_bars2 = input.int(3, title = 'Swing2 Lookback - Support', group = 'Input Data')
cluster_bars = input.int(20, title = 'Cluster Lookback Period', group = 'Clustering', minval=5)
num_clusters = input.int(3, title = 'Number of Clusters', group = 'Clustering', minval=2, maxval=5)
show_clusters = input.bool(false, title = 'Show Cluster Zones', group = 'Clustering')

// - - Calculation
// Swing Highs and Lows
res = ta.highest(high, swing_bars)
sup = ta.lowest(low, swing_bars)
iff_1 = close < sup[swing_bars2] ? -1 : 0
avd = close > res[swing_bars2] ? 1 : iff_1
avn = ta.valuewhen(avd != 0, avd, 0)
tsl = avn == 1 ? sup : res

// Simplified K-means Clustering Approximation
var float cluster_high = na
var float cluster_low = na
var float cluster_step = na
var int current_cluster = 0

// Calculate price range over lookback period for clustering
price_high = ta.highest(high, cluster_bars)
price_low = ta.lowest(low, cluster_bars)
price_range = price_high - price_low
cluster_step := price_range / num_clusters

// Define cluster boundaries
cluster_high := price_high
cluster_low := price_low

// Assign current price to a cluster (0 to num_clusters-1)
if price_range > 0
    current_cluster := math.floor((close - price_low) / cluster_step)
    if current_cluster >= num_clusters
        current_cluster := num_clusters - 1

// Adjust swing levels based on cluster (simple adjustment for demonstration)
cluster_adjust = cluster_step * 0.5
tsl_adjusted = avn == 1 ? sup + cluster_adjust * (current_cluster / num_clusters) : res - cluster_adjust * (current_cluster / num_clusters)

// Plot cluster zones if enabled
if show_clusters
    // Note: hline cannot be used with dynamic series in Pine Script. Cluster levels change over time, so they cannot be plotted as static horizontal lines.
    // As a workaround, cluster levels can be observed in the Data Window or plotted manually if needed.
    for i = 0 to num_clusters - 1
        cluster_level = price_low + (i * cluster_step)
        // Placeholder for potential future plotting method if Pine Script supports dynamic horizontal lines.

// - - Conditions
swing_longX = ta.crossover(close, tsl_adjusted)
swing_shortX = ta.crossunder(close, tsl_adjusted)
swing_long = close > tsl_adjusted
swing_short = close < tsl_adjusted

// - - Plot
plotshape(show_swing ? swing_longX : false, title = 'Swing ▲', style = shape.labelup, location = location.belowbar, color = color.new(color.silver, 99), textcolor = color.new(color.green, 20), text = '▲', size = size.tiny)
plotshape(show_swing ? swing_shortX : false, title = 'Swing ▼', style = shape.labeldown, location = location.abovebar, color = color.new(color.gray, 99), textcolor = color.new(#3179f5, 20), text = '▼', size = size.tiny)
