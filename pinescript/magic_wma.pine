//@version=6
indicator('Magic WMA com K-means', overlay = true)

// Inputs para configuração
wmaLength = input(34, title = 'Período da WMA')
kmeansLength = input(20, title = 'Período para K-means')
numClusters = input(3, title = 'Número de Clusters K-means')
adaptiveSmoothing = input(true, title = 'Suavização Adaptativa')

// Função K-means simplificada para Pine Script
kmeans(src, length, clusters) =>
    var float[] centroids = array.new<float>(clusters, 0.0)
    var int[] assignments = array.new<int>(length, 0)
    
    // Inicializar centroids com valores distribuídos
    if barstate.isfirst
        minVal = ta.lowest(src, length)
        maxVal = ta.highest(src, length)
        range_lenght = maxVal - minVal
        
        for i = 0 to clusters - 1
            centroid = minVal + (range_lenght * i / (clusters - 1))
            array.set(centroids, i, centroid)
    
    // Coletar dados recentes
    prices = array.new<float>()
    for i = 0 to length - 1
        if bar_index >= i
            array.push(prices, src[i])
    
    // Executar iterações K-means (simplificado)
    if array.size(prices) >= clusters
        // Atribuir pontos aos clusters mais próximos
        for i = 0 to array.size(prices) - 1
            price = array.get(prices, i)
            minDist = math.abs(price - array.get(centroids, 0))
            bestCluster = 0
            
            for j = 1 to clusters - 1
                dist = math.abs(price - array.get(centroids, j))
                if dist < minDist
                    minDist := dist
                    bestCluster := j
            
            if i < array.size(assignments)
                array.set(assignments, i, bestCluster)
        
        // Atualizar centroids
        for j = 0 to clusters - 1
            sum = 0.0
            count = 0
            
            for i = 0 to math.min(array.size(prices), array.size(assignments)) - 1
                if array.get(assignments, i) == j
                    sum += array.get(prices, i)
                    count += 1
            
            if count > 0
                array.set(centroids, j, sum / count)
    
    // Retornar centroid mais próximo do preço atual
    currentPrice = src
    minDist = math.abs(currentPrice - array.get(centroids, 0))
    bestCentroid = array.get(centroids, 0)
    
    for i = 1 to clusters - 1
        dist = math.abs(currentPrice - array.get(centroids, i))
        if dist < minDist
            minDist := dist
            bestCentroid := array.get(centroids, i)
    
    bestCentroid

// Cálculo da WMA tradicional
wmaPrice = ta.wma(close, wmaLength)

// Cálculo do K-means para suavização
kmeansPrice = kmeans(close, kmeansLength, numClusters)

// WMA melhorada com K-means
wmaKmeans = adaptiveSmoothing ? ta.wma(kmeansPrice, wmaLength) : ta.wma((close + kmeansPrice) / 2, wmaLength)

// Cálculo da força do sinal baseado na distância do K-means
priceDeviation = math.abs(close - kmeansPrice) / close * 100
signalStrength = priceDeviation < 1 ? 3 : priceDeviation < 2 ? 2 : 1

// Condições de entrada baseadas na WMA melhorada
bullishSignal = close > wmaKmeans and close[1] <= wmaKmeans[1]
bearishSignal = close < wmaKmeans and close[1] >= wmaKmeans[1]

// Variáveis persistentes para controle de sinais
var int lastSignal = 0

// Detectar novos sinais
newBullish = bullishSignal and lastSignal != 1
newBearish = bearishSignal and lastSignal != -1

// Atualizar último sinal
if newBullish
    lastSignal := 1
if newBearish
    lastSignal := -1

// Cores dinâmicas baseadas na força do sinal
wmaColor = close > wmaKmeans ? (signalStrength == 3 ? color.green : signalStrength == 2 ? color.lime : color.yellow) :     (signalStrength == 3 ? color.red : 
     signalStrength == 2 ? color.orange : color.gray)

// Plotar as linhas
plot(wmaPrice, title = 'WMA Tradicional', color = color.new(color.blue, 70), linewidth = 1)
plot(wmaKmeans, title = 'WMA + K-means', color = wmaColor, linewidth = 2)
plot(kmeansPrice, title = 'K-means', color = color.new(color.purple, 50), linewidth = 1)

// Sinais de entrada
plotshape(newBullish and signalStrength == 3, location = location.belowbar, color = color.green, style = shape.triangleup, size = size.tiny, title = 'Sinal de Compra Forte')
plotshape(newBullish and signalStrength == 2, location = location.belowbar, color = color.lime, style = shape.triangleup, size = size.tiny, title = 'Sinal de Compra Moderado')
plotshape(newBullish and signalStrength == 1, location = location.belowbar, color = color.yellow, style = shape.triangleup, size = size.tiny, title = 'Sinal de Compra Fraco')

plotshape(newBearish and signalStrength == 3, location = location.abovebar, color = color.red, style = shape.triangledown, size = size.tiny, title = 'Sinal de Venda Forte')
plotshape(newBearish and signalStrength == 2, location = location.abovebar, color = color.orange, style = shape.triangledown, size = size.tiny, title = 'Sinal de Venda Moderado')
plotshape(newBearish and signalStrength == 1, location = location.abovebar, color = color.gray, style = shape.triangledown, size = size.tiny, title = 'Sinal de Venda Fraco')
>>>>>>> REPLACE

// Alertas
alertcondition(newBullish, title = 'Compra WMA+K-means', message = 'Sinal de compra detectado - WMA com K-means')
alertcondition(newBearish, title = 'Venda WMA+K-means', message = 'Sinal de venda detectado - WMA com K-means')
