// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © Bluetuga

//@version=6
indicator('Swing Base', overlay = true)

// - - Inputs
show_swing = input.bool(true, title = '► Swing signal', group = 'Signals')
swing_bars = input.int(3, title = 'Swing Loockback - Resistence', group = 'Input Data')
swing_bars2 = input.int(3, title = 'Swing2 Loockback - Support', group = 'Input Data')

// - - Calculation
res = ta.highest(high, swing_bars)
sup = ta.lowest(low, swing_bars)
iff_1 = close < sup[swing_bars2] ? -1 : 0
avd = close > res[swing_bars2] ? 1 : iff_1
avn = ta.valuewhen(avd != 0, avd, 0)
tsl = avn == 1 ? sup : res

// - - Conditions
swing_longX = ta.crossover(close, tsl)
swing_shortX = ta.crossunder(close, tsl)
swing_long = close > tsl
swing_short = close < tsl

// - - Plot
plotshape(show_swing ? swing_longX : false, title = 'Swing ▲', style = shape.labelup, location = location.belowbar, color = color.new(color.silver, 75), textcolor = color.new(color.green, 20), text = '▲', size = size.tiny)
plotshape(show_swing ? swing_shortX : false, title = 'Swing ▼', style = shape.labeldown, location = location.abovebar, color = color.new(color.gray, 75), textcolor = color.new(#3179f5, 20), text = '▼', size = size.tiny)
