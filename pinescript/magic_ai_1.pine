// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © Enhanced Magic 001 with K-means Clustering AI Optimization

//@version=5
indicator("Magic 001 AI Enhanced (K-means Clustering)", "Magic AI 1", overlay=true, max_labels_count = 500)

//──────────────────────────────────────────────────────────────────────────────────────
// CONFIGURAÇÕES DE ENTRADA
//──────────────────────────────────────────────────────────────────────────────────────

// Parâmetros principais
wma_base = input.int(50, "WMA Base Length", minval=10, maxval=200, group="Parâmetros Base")
rsi_base = input.int(50, "RSI Base Length", minval=10, maxval=100, group="Parâmetros Base")
adx_base = input.int(14, "ADX Base Length", minval=5, maxval=50, group="Parâmetros Base")

// Configurações de clustering
enable_clustering = input.bool(true, "Ativar Clustering K-means", group="AI Clustering")
perfMemory = input.float(10, "Performance Memory", minval=2, maxval=50, group="AI Clustering")
fromCluster = input.string("Best", "Usar Cluster", options=["Best", "Average", "Worst"], group="AI Clustering")

// Otimização
maxIterations = input.int(500, "Máximo de Iterações", minval=100, maxval=2000, group="Otimização")
maxHistData = input.int(5000, "Barras Históricas", minval=1000, maxval=10000, group="Otimização")

// Filtros
use_rsi_filter = input.bool(true, "Usar Filtro RSI", group="Filtros")
use_adx_filter = input.bool(true, "Usar Filtro ADX", group="Filtros")
use_swing_filter = input.bool(true, "Usar Filtro Swing", group="Filtros")

// Swing TSL
swing_bars = input.int(3, "Swing Lookback - Resistência", minval=1, maxval=10, group="Swing TSL")
swing_bars2 = input.int(3, "Swing Lookback - Suporte", minval=1, maxval=10, group="Swing TSL")

// Alertas
enable_alerts = input.bool(true, "Ativar Alertas", group="Alertas")

// Estilo
bullColor = input.color(color.new(color.green, 0), "Cor Alta", group="Estilo")
bearColor = input.color(color.new(color.red, 0), "Cor Baixa", group="Estilo")
showGradient = input.bool(true, "Colorir Candles", group="Estilo")
showSignals = input.bool(true, "Mostrar Sinais", group="Estilo")

// Dashboard
showDashboard = input.bool(true, "Mostrar Dashboard", group="Dashboard")
dashLocation = input.string("Top Right", "Localização", options=["Top Right", "Bottom Right", "Bottom Left"], group="Dashboard")

//──────────────────────────────────────────────────────────────────────────────────────
// TIPOS DEFINIDOS PELO USUÁRIO (UDT)
//──────────────────────────────────────────────────────────────────────────────────────

type magic_config
    int wma_length
    int rsi_length
    float rsi_threshold
    int adx_length
    float adx_threshold
    int swing_bars
    int swing_bars2
    float performance = 0
    bool is_bullish = false
    bool is_bearish = false

type cluster_vector
    array<float> values
    array<int> indices

//──────────────────────────────────────────────────────────────────────────────────────
// VARIÁVEIS GLOBAIS E ARRAYS
//──────────────────────────────────────────────────────────────────────────────────────

var array<magic_config> configs = array.new<magic_config>(0)
var array<float> performances = array.new<float>(0)
var array<cluster_vector> clusters = array.new<cluster_vector>(0)
var array<float> centroids = array.new<float>(0)

var float active_wma_length = wma_base
var float active_rsi_length = rsi_base
var float active_rsi_threshold = 50
var float active_adx_length = adx_base
var float active_adx_threshold = 25
var int active_swing_bars = swing_bars
var int active_swing_bars2 = swing_bars2

var float performance_index = 0
var string active_cluster_name = "Base"
var color active_cluster_color = color.gray

//──────────────────────────────────────────────────────────────────────────────────────
// INICIALIZAÇÃO DAS CONFIGURAÇÕES
//──────────────────────────────────────────────────────────────────────────────────────

if barstate.isfirst and enable_clustering
    // Criar múltiplas configurações para teste
    wma_range = array.from(20, 30, 40, 50, 60, 70)
    rsi_range = array.from(30, 40, 50, 60, 70)
    rsi_threshold_range = array.from(40, 45, 50, 55, 60)
    adx_range = array.from(10, 14, 18, 22)
    adx_threshold_range = array.from(20, 25, 30, 35)
    swing_range = array.from(2, 3, 4, 5)
    
    // Gerar combinações de configurações
    for wma_len in wma_range
        for rsi_len in rsi_range
            for rsi_thresh in rsi_threshold_range
                for adx_len in adx_range
                    for adx_thresh in adx_threshold_range
                        for swing_bar in swing_range
                            if configs.size() < 50 // Limitar número de configurações
                                new_config = magic_config.new(
                                  wma_len, rsi_len, rsi_thresh, 
                                  adx_len, adx_thresh, swing_bar, swing_bar)
                                configs.push(new_config)
                                performances.push(0.0)

//──────────────────────────────────────────────────────────────────────────────────────
// FUNÇÕES AUXILIARES
//──────────────────────────────────────────────────────────────────────────────────────

// Função para calcular ADX
calculate_adx(length) =>
    up = ta.change(high)
    down = -ta.change(low)
    plusDM = na(up) ? na : (up > down and up > 0 ? up : 0)
    minusDM = na(down) ? na : (down > up and down > 0 ? down : 0)
    truerange = ta.rma(ta.tr, length)
    plus = fixnan(100 * ta.rma(plusDM, length) / truerange)
    minus = fixnan(100 * ta.rma(minusDM, length) / truerange)
    sum = plus + minus
    100 * ta.rma(math.abs(plus - minus) / (sum == 0 ? 1 : sum), length)

// Função para calcular Swing TSL
calculate_swing_tsl(bars1, bars2) =>
    res = ta.highest(high, bars1)
    sup = ta.lowest(low, bars1)
    iff_1 = close < sup[bars2] ? -1 : 0
    avd = close > res[bars2] ? 1 : iff_1
    avn = ta.valuewhen(avd != 0, avd, 0)
    tsl = avn == 1 ? sup : res
    [tsl, close > tsl, close < tsl]

// Função para avaliar performance de uma configuração
evaluate_config(config) =>
    wma_val = ta.wma(close, config.wma_length)
    rsi_val = ta.rsi(close, rsi_base)
    adx_val = calculate_adx(adx_base)
    [tsl, swing_long, swing_short] = calculate_swing_tsl(config.swing_bars, config.swing_bars2)
    
    // Condições
    price_above_wma = close > wma_val
    price_below_wma = close < wma_val
    rsi_bullish = rsi_val > config.rsi_threshold
    rsi_bearish = rsi_val < config.rsi_threshold
    adx_strong = adx_val > config.adx_threshold
    
    // Sinais
    bullish = price_above_wma and rsi_bullish and adx_strong and swing_long
    bearish = price_below_wma and rsi_bearish and adx_strong and swing_short
    
    // Calcular performance baseada na precisão dos sinais
    signal_accuracy = 0.0
    if bullish and close > close[1]
        signal_accuracy := 1.0
    else if bearish and close < close[1]
        signal_accuracy := 1.0
    else if bullish or bearish
        signal_accuracy := -0.5
    
    [bullish, bearish, signal_accuracy]

//──────────────────────────────────────────────────────────────────────────────────────
// CLUSTERING K-MEANS
//──────────────────────────────────────────────────────────────────────────────────────

if enable_clustering and last_bar_index - bar_index <= maxHistData and configs.size() > 0
    // Avaliar todas as configurações
    for i = 0 to configs.size() - 1
        config = configs.get(i)
        [bullish, bearish, accuracy] = evaluate_config(config)
        
        // Atualizar performance usando EMA
        current_perf = performances.get(i)
        new_perf = current_perf + 2/(perfMemory+1) * (accuracy - current_perf)
        performances.set(i, new_perf)
        
        // Atualizar flags da configuração
        config.is_bullish := bullish
        config.is_bearish := bearish
    
    // Executar clustering K-means apenas a cada 100 barras para otimização
    if bar_index % 100 == 0
        // Inicializar centroides usando quartis
        sorted_perfs = performances.copy()
        sorted_perfs.sort()
        
        if sorted_perfs.size() >= 3
            centroids.clear()
            centroids.push(sorted_perfs.get(math.floor(sorted_perfs.size() * 0.25)))
            centroids.push(sorted_perfs.get(math.floor(sorted_perfs.size() * 0.50)))
            centroids.push(sorted_perfs.get(math.floor(sorted_perfs.size() * 0.75)))
            
            // Algoritmo K-means
            for iteration = 0 to maxIterations
                // Inicializar clusters
                clusters.clear()
                for j = 0 to 2
                    new_cluster = cluster_vector.new(array.new<float>(0), array.new<int>(0))
                    clusters.push(new_cluster)
                
                // Atribuir cada performance ao cluster mais próximo
                for i = 0 to performances.size() - 1
                    perf = performances.get(i)
                    min_dist = math.abs(perf - centroids.get(0))
                    cluster_idx = 0
                    
                    for j = 1 to 2
                        dist = math.abs(perf - centroids.get(j))
                        if dist < min_dist
                            min_dist := dist
                            cluster_idx := j
                    
                    target_cluster = clusters.get(cluster_idx)
                    target_cluster.values.push(perf)
                    target_cluster.indices.push(i)
                
                // Atualizar centroides
                new_centroids = array.new<float>(0)
                converged = true
                
                for j = 0 to 2
                    cluster = clusters.get(j)
                    if cluster.values.size() > 0
                        new_centroid = cluster.values.avg()
                        new_centroids.push(new_centroid)
                        if math.abs(new_centroid - centroids.get(j)) > 0.001
                            converged := false
                    else
                        new_centroids.push(centroids.get(j))
                
                centroids := new_centroids
                
                if converged
                    break
            
            // Selecionar cluster ativo
            cluster_index = fromCluster == "Best" ? 2 : fromCluster == "Average" ? 1 : 0
            
            if clusters.size() > cluster_index
                active_cluster = clusters.get(cluster_index)
                if active_cluster.indices.size() > 0
                    // Usar configuração média do cluster selecionado
                    best_config_idx = active_cluster.indices.get(0)
                    best_config = configs.get(best_config_idx)
                    
                    active_wma_length := best_config.wma_length
                    active_rsi_length := best_config.rsi_length
                    active_rsi_threshold := best_config.rsi_threshold
                    active_adx_length := best_config.adx_length
                    active_adx_threshold := best_config.adx_threshold
                    active_swing_bars := best_config.swing_bars
                    active_swing_bars2 := best_config.swing_bars2
                    
                    performance_index := performances.get(best_config_idx)
                    active_cluster_name := fromCluster
                    active_cluster_color := fromCluster == "Best" ? color.green : fromCluster == "Average" ? color.yellow : color.red

//──────────────────────────────────────────────────────────────────────────────────────
// CÁLCULOS DOS INDICADORES PRINCIPAIS
//──────────────────────────────────────────────────────────────────────────────────────

// Usar parâmetros otimizados ou base
wma_length_final = enable_clustering ? int(active_wma_length) : wma_base
rsi_length_final = enable_clustering ? int(active_rsi_length) : rsi_base
rsi_threshold_final = enable_clustering ? active_rsi_threshold : 50
adx_length_final = enable_clustering ? int(active_adx_length) : adx_base
adx_threshold_final = enable_clustering ? active_adx_threshold : 25
swing_bars_final = enable_clustering ? active_swing_bars : swing_bars
swing_bars2_final = enable_clustering ? active_swing_bars2 : swing_bars2

// Calcular indicadores
wma_value = ta.wma(close, wma_length_final)
rsi_value = ta.rsi(close, rsi_base)
adx_value = calculate_adx(adx_base)
[tsl, swing_long, swing_short] = calculate_swing_tsl(swing_bars_final, swing_bars2_final)

// Condições de filtro
rsi_filter_bull = not use_rsi_filter or rsi_value > rsi_threshold_final
rsi_filter_bear = not use_rsi_filter or rsi_value < rsi_threshold_final
adx_filter_pass = not use_adx_filter or adx_value > adx_threshold_final
swing_filter_bull = not use_swing_filter or swing_long
swing_filter_bear = not use_swing_filter or swing_short

// Condições principais
price_above_wma = close > wma_value
price_below_wma = close < wma_value

// Sinais finais
bullish_condition = price_above_wma and rsi_filter_bull and adx_filter_pass and swing_filter_bull
bearish_condition = price_below_wma and rsi_filter_bear and adx_filter_pass and swing_filter_bear

// Detecção de mudanças de sinal
bullish_start = bullish_condition and not bullish_condition[1]
bearish_start = bearish_condition and not bearish_condition[1]
bullish_end = not bullish_condition and bullish_condition[1]
bearish_end = not bearish_condition and bearish_condition[1]

//──────────────────────────────────────────────────────────────────────────────────────
// PLOTAGEM DOS INDICADORES
//──────────────────────────────────────────────────────────────────────────────────────

// WMA
plot(wma_value, title="WMA Otimizada", color=color.new(color.blue, 0), linewidth=2)

// TSL
plot(tsl, title="Trailing Stop Line", color=color.new(color.purple, 0), linewidth=1)

// Background
bg_color = bullish_condition ? color.new(bullColor, 90) : bearish_condition ? color.new(bearColor, 90) : na
bgcolor(bg_color, title="Fundo de Tendência")

// Coloração de candles baseada na performance
gradient_color = (showGradient and enable_clustering) ?  color.from_gradient(performance_index, -1, 1, color.new(bearColor, 70), color.new(bullColor, 70)) : na
barcolor(gradient_color)

// SINAIS (corrigidos - âmbito global)
show_bullish_signal = showSignals and bullish_start
show_bearish_signal = showSignals and bearish_start
show_bullish_end = showSignals and bullish_end
show_bearish_end = showSignals and bearish_end

plotshape(show_bullish_signal, title="Sinal de Alta", location=location.belowbar, color=bullColor, style=shape.triangleup, size=size.small)
plotshape(show_bearish_signal, title="Sinal de Baixa", location=location.abovebar, color=bearColor, style=shape.triangledown, size=size.small)
plotshape(show_bullish_end, title="Fim da Alta", location=location.abovebar, color=color.orange, style=shape.xcross, size=size.tiny)
plotshape(show_bearish_end, title="Fim da Baixa", location=location.belowbar, color=color.orange, style=shape.xcross, size=size.tiny)


// Labels com performance
if showSignals and enable_clustering
    if bullish_start
        label.new(bar_index, low, str.tostring(math.round(performance_index * 100)) + "%", 
                  color=bullColor, style=label.style_label_up, textcolor=color.white, size=size.tiny)
    if bearish_start
        label.new(bar_index, high, str.tostring(math.round(performance_index * 100)) + "%", 
                  color=bearColor, style=label.style_label_down, textcolor=color.white, size=size.tiny)

//──────────────────────────────────────────────────────────────────────────────────────
// ALERTAS
//──────────────────────────────────────────────────────────────────────────────────────

if enable_alerts
    if bullish_start
        cluster_info = enable_clustering ? (" | Cluster: " + active_cluster_name + " | Perf: " + str.tostring(performance_index * 100, "#.#") + "%") : ""
        alert("📈 ALTA (AI): Preço: " + str.tostring(close, "#.##") + cluster_info, alert.freq_once_per_bar)
    
    if bearish_start
        cluster_info = enable_clustering ? (" | Cluster: " + active_cluster_name + " | Perf: " + str.tostring(performance_index * 100, "#.#") + "%") : ""
        alert("📉 BAIXA (AI): Preço: " + str.tostring(close, "#.##") + cluster_info, alert.freq_once_per_bar)

//──────────────────────────────────────────────────────────────────────────────────────
// DASHBOARD
//──────────────────────────────────────────────────────────────────────────────────────

var table_position = dashLocation == "Bottom Left" ? position.bottom_left : dashLocation == "Top Right" ? position.top_right : position.bottom_right

var info_table = table.new(table_position, 2, 12, bgcolor=color.white, border_width=1, border_color=color.gray)

if showDashboard and barstate.islast
    // Cabeçalho
    table.cell(info_table, 0, 0, "Magic AI Pro", text_color=color.white, text_size=size.small, bgcolor=color.navy)
    table.cell(info_table, 1, 0, "Valor", text_color=color.white, text_size=size.small, bgcolor=color.navy)
    
    // Status do clustering
    if enable_clustering
        table.cell(info_table, 0, 1, "Clustering AI", text_color=color.black, text_size=size.tiny)
        table.cell(info_table, 1, 1, "ATIVO", text_color=color.green, text_size=size.tiny)
        
        table.cell(info_table, 0, 2, "Cluster Ativo", text_color=color.black, text_size=size.tiny)
        table.cell(info_table, 1, 2, active_cluster_name, text_color=active_cluster_color, text_size=size.tiny)
        
        table.cell(info_table, 0, 3, "Performance", text_color=color.black, text_size=size.tiny)
        perf_color = performance_index > 0 ? color.green : performance_index < 0 ? color.red : color.gray
        table.cell(info_table, 1, 3, str.tostring(performance_index * 100, "#.#") + "%", text_color=perf_color, text_size=size.tiny)
    else
        table.cell(info_table, 0, 1, "Clustering AI", text_color=color.black, text_size=size.tiny)
        table.cell(info_table, 1, 1, "DESATIVO", text_color=color.red, text_size=size.tiny)
    
    // Parâmetros atuais
    row = enable_clustering ? 4 : 2
    table.cell(info_table, 0, row, "WMA Length", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, row, str.tostring(wma_length_final), text_color=color.blue, text_size=size.tiny)
    
    table.cell(info_table, 0, row+1, "RSI (" + str.tostring(rsi_length_final) + ")", text_color=color.black, text_size=size.tiny)
    rsi_color = rsi_value > rsi_threshold_final ? color.green : color.red
    table.cell(info_table, 1, row+1, str.tostring(rsi_value, "#.#"), text_color=rsi_color, text_size=size.tiny)
    
    table.cell(info_table, 0, row+2, "ADX (" + str.tostring(adx_length_final) + ")", text_color=color.black, text_size=size.tiny)
    adx_color = adx_value > adx_threshold_final ? color.green : color.red
    table.cell(info_table, 1, row+2, str.tostring(adx_value, "#.#"), text_color=adx_color, text_size=size.tiny)
    
    table.cell(info_table, 0, row+3, "Preço vs WMA", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, row+3, price_above_wma ? "ACIMA" : "ABAIXO", text_color=price_above_wma ? color.green : color.red, text_size=size.tiny)
    
    table.cell(info_table, 0, row+4, "Swing", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, row+4, swing_long ? "ALTA" : "BAIXA", text_color=swing_long ? color.green : color.red, text_size=size.tiny)
    
    table.cell(info_table, 0, row+5, "TSL", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, row+5, str.tostring(tsl, "#.##"), text_color=color.purple, text_size=size.tiny)
    
    table.cell(info_table, 0, row+6, "Sinal Principal", text_color=color.black, text_size=size.tiny)
    signal_text = bullish_condition ? "ALTA" : bearish_condition ? "BAIXA" : "NEUTRO"
    signal_color = bullish_condition ? color.green : bearish_condition ? color.red : color.gray
    table.cell(info_table, 1, row+6, signal_text, text_color=signal_color, text_size=size.tiny)
