"""
Script de análise independente para métricas de trading na OKX Exchange.
Este script combina as funcionalidades de análise de '777_trail_base.py' e '777_oco_base.py',
incluindo Métricas Avançadas, Resumo Geral, Valores Investidos e Lucros Realizados.
"""

import asyncio
import os
import numpy as np
import pandas as pd
import json
from dataclasses import dataclass
from typing import Any, Dict, List, Optional
from dotenv import load_dotenv
from utils.logger import TradingLogger
from utils.database import TradingDatabase
from core.okx_client import OKXClient

load_dotenv()


@dataclass
class BotConfig:
    """Configurações centralizadas para o script de análise."""

    SANDBOX_MODE: bool = False
    TRADING_SYMBOLS = ["BTC/USDC", "ETH/USDC", "SOL/USDC", "CRO/USDC"]
    # TODO: Adicionar outros símbolos aqui, vindos do paramenters.json general.TRADING_SYMBOLS
    ENABLE_TELEGRAM_NOTIFICATIONS: bool = False
    ENABLE_SOUND_NOTIFICATIONS: bool = False

    def __post_init__(self):
        self._initialize_defaults()

    def _initialize_defaults(self):
        """Inicializa valores padrão de OKXClient para atributos não definidos."""
        from core.okx_client import OKXClient

        default_config = OKXClient().config
        default_attrs = [attr for attr in dir(default_config) if attr.isupper()]
        for attr in default_attrs:
            if (
                not hasattr(self, attr)
                or getattr(self, attr) is None
                or (
                    isinstance(getattr(self, attr), (int, float))
                    and getattr(self, attr) == 0
                )
            ):
                default_val = getattr(default_config, attr, None)
                if default_val is not None:
                    setattr(self, attr, default_val)

    @classmethod
    def load_config(cls, file_path: str = "config.json") -> "BotConfig":
        """Carrega a configuração de um arquivo JSON."""
        try:
            with open(file_path, "r") as f:
                config_data = json.load(f)
            return cls(**config_data)
        except Exception as e:
            print(f"Falha ao carregar configuração: {e}")
            return cls()


class AnalysisDashboard:
    """Classe principal para análise de métricas de trading."""

    def __init__(self, config: Optional[BotConfig] = None):
        self.config = config or BotConfig()
        self.logger = TradingLogger.get_logger(__name__)
        self.db = self._initialize_database()
        self.client = OKXClient(self.config)

    def _initialize_database(self) -> TradingDatabase:
        """Inicializa o banco de dados para leitura de ordens."""
        db_name = "sandbox_trades.db" if self.config.SANDBOX_MODE else "live_trades.db"
        db_path = os.path.join("trades", db_name)
        try:
            return TradingDatabase(db_path)
        except Exception as exc:
            self.logger.error("Falha ao inicializar banco de dados: %s", str(exc))
            raise

    def calculate_advanced_metrics(self) -> Dict[str, Any]:
        """Calcula métricas avançadas como Sharpe Ratio, Máximo Drawdown, Win Rate e Profit Factor."""
        metrics = {}
        for symbol in self.config.TRADING_SYMBOLS:
            try:
                all_orders = self.db.get_orders(limit=1000)
                symbol_orders = [
                    o
                    for o in all_orders
                    if o.get("symbol") == symbol
                    and o.get("status", "")
                    and o.get("status", "").lower() == "closed"
                ]
                if not symbol_orders:
                    metrics[symbol] = {
                        "sharpe_ratio": 0.0,
                        "max_drawdown": 0.0,
                        "win_rate": 0.0,
                        "profit_factor": 0.0,
                    }
                    continue

                buy_orders = [
                    o
                    for o in symbol_orders
                    if o.get("side", "") and o.get("side", "").lower() == "buy"
                ]
                sell_orders = [
                    o
                    for o in symbol_orders
                    if o.get("side", "") and o.get("side", "").lower() == "sell"
                ]
                returns = []
                equity_curve = []
                total_equity = 0.0
                for sell_order in sorted(
                    sell_orders, key=lambda x: x.get("timestamp", 0)
                ):
                    sell_price = float(sell_order.get("price", 0.0))
                    sell_amount = float(sell_order.get("amount", 0.0))
                    buy_amount_total = 0.0
                    buy_cost_total = 0.0
                    for buy_order in sorted(
                        buy_orders, key=lambda x: x.get("timestamp", 0)
                    ):
                        if (
                            buy_order.get("timestamp", 0)
                            < sell_order.get("timestamp", 0)
                            and buy_amount_total < sell_amount
                        ):
                            buy_amount = float(buy_order.get("amount", 0.0))
                            buy_price = float(buy_order.get("price", 0.0))
                            amount_to_use = min(
                                buy_amount, sell_amount - buy_amount_total
                            )
                            buy_amount_total += amount_to_use
                            buy_cost_total += amount_to_use * buy_price
                    if buy_amount_total > 0:
                        avg_buy_price = buy_cost_total / buy_amount_total
                        profit = (sell_price - avg_buy_price) * sell_amount
                        total_equity += profit
                        equity_curve.append(total_equity)
                        if len(equity_curve) > 1:
                            returns.append(
                                (equity_curve[-1] - equity_curve[-2]) / equity_curve[-2]
                                if equity_curve[-2] > 0
                                else 0.0
                            )

                if returns:
                    mean_return = np.mean(returns)
                    std_return = np.std(returns) if len(returns) > 1 else 0.0
                    sharpe_ratio = (
                        mean_return / std_return * np.sqrt(252)
                        if std_return > 0
                        else 0.0
                    )
                else:
                    sharpe_ratio = 0.0

                max_drawdown = 0.0
                peak = equity_curve[0] if equity_curve else 0.0
                for value in equity_curve:
                    if value > peak:
                        peak = value
                    drawdown = (peak - value) / peak if peak > 0 else 0.0
                    if drawdown > max_drawdown:
                        max_drawdown = drawdown

                wins = sum(
                    1
                    for sell_order in sell_orders
                    for buy_order in buy_orders
                    if buy_order.get("timestamp", 0) < sell_order.get("timestamp", 0)
                    and float(sell_order.get("price", 0.0))
                    > float(buy_order.get("price", 0.0))
                )
                total_trades = len(sell_orders)
                win_rate = wins / total_trades * 100 if total_trades > 0 else 0.0

                gross_profit = sum(
                    (
                        float(sell_order.get("price", 0.0))
                        - float(buy_order.get("price", 0.0))
                    )
                    * float(sell_order.get("amount", 0.0))
                    for sell_order in sell_orders
                    for buy_order in buy_orders
                    if buy_order.get("timestamp", 0) < sell_order.get("timestamp", 0)
                    and float(sell_order.get("price", 0.0))
                    > float(buy_order.get("price", 0.0))
                )
                gross_loss = abs(
                    sum(
                        (
                            float(sell_order.get("price", 0.0))
                            - float(buy_order.get("price", 0.0))
                        )
                        * float(sell_order.get("amount", 0.0))
                        for sell_order in sell_orders
                        for buy_order in buy_orders
                        if buy_order.get("timestamp", 0)
                        < sell_order.get("timestamp", 0)
                        and float(sell_order.get("price", 0.0))
                        <= float(buy_order.get("price", 0.0))
                    )
                )
                profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0.0

                metrics[symbol] = {
                    "sharpe_ratio": sharpe_ratio,
                    "max_drawdown": max_drawdown * 100,
                    "win_rate": win_rate,
                    "profit_factor": profit_factor,
                }

                self.logger.info(f"📊 Métricas Avançadas para {symbol}:")
                self.logger.info(f"• Sharpe Ratio: {sharpe_ratio:.2f}")
                self.logger.info(f"• Máximo Drawdown: {max_drawdown * 100:.2f}%")
                self.logger.info(f"• Win Rate: {win_rate:.2f}%")
                self.logger.info(f"• Profit Factor: {profit_factor:.2f}")
            except Exception as exc:
                self.logger.error(
                    f"Erro ao calcular métricas avançadas para {symbol}: {str(exc)}"
                )
                metrics[symbol] = {
                    "sharpe_ratio": 0.0,
                    "max_drawdown": 0.0,
                    "win_rate": 0.0,
                    "profit_factor": 0.0,
                }
        return metrics

    def display_invested_and_profits(self) -> None:
        """Exibe valores investidos, lucros realizados e não realizados por símbolo."""
        print("\n💸 Valores Investidos e Lucros Realizados:")
        print("=" * 50)
        total_invested = 0
        total_realized_pnl = 0
        total_unrealized_pnl = 0

        for symbol in self.config.TRADING_SYMBOLS:
            all_orders = self.db.get_orders(limit=1000)
            symbol_orders = [o for o in all_orders if o.get("symbol") == symbol]

            buy_orders = [
                o
                for o in symbol_orders
                if o.get("side", "") and o.get("side", "").lower() == "buy"
            ]
            invested_amount = sum(
                float(o.get("amount", 0)) * float(o.get("price", 0))
                for o in buy_orders
                if o.get("amount") is not None and o.get("price") is not None
            )

            sell_orders = [
                o
                for o in symbol_orders
                if o.get("side", "")
                and o.get("side", "").lower() == "sell"
                and o.get("status", "")
                and o.get("status", "").lower() == "closed"
            ]
            realized_pnl = 0
            used_buy_amounts = {o.get("id", "N/A"): 0 for o in buy_orders}
            for sell_order in sorted(sell_orders, key=lambda x: x.get("timestamp", 0)):
                sell_amount = (
                    float(sell_order.get("amount", 0))
                    if sell_order.get("amount") is not None
                    else 0.0
                )
                sell_price = (
                    float(sell_order.get("price", 0))
                    if sell_order.get("price") is not None
                    else 0.0
                )
                sell_timestamp = sell_order.get("timestamp", 0)
                buy_amount_total = 0
                buy_cost_total = 0
                for buy_order in sorted(
                    buy_orders, key=lambda x: x.get("timestamp", 0)
                ):
                    buy_id = buy_order.get("id", "N/A")
                    buy_amount = (
                        float(buy_order.get("amount", 0))
                        if buy_order.get("amount") is not None
                        else 0.0
                    )
                    buy_price = (
                        float(buy_order.get("price", 0))
                        if buy_order.get("price") is not None
                        else 0.0
                    )
                    buy_timestamp = buy_order.get("timestamp", 0)
                    if (
                        buy_timestamp < sell_timestamp
                        and used_buy_amounts[buy_id] < buy_amount
                    ):
                        available_amount = buy_amount - used_buy_amounts[buy_id]
                        amount_to_use = min(
                            available_amount, sell_amount - buy_amount_total
                        )
                        if amount_to_use > 0:
                            buy_amount_total += amount_to_use
                            buy_cost_total += amount_to_use * buy_price
                            used_buy_amounts[buy_id] += amount_to_use
                            if buy_amount_total >= sell_amount:
                                break
                if buy_amount_total > 0:
                    avg_buy_price = buy_cost_total / buy_amount_total
                    realized_pnl += (sell_price - avg_buy_price) * sell_amount

            open_buy_orders = [
                o
                for o in buy_orders
                if o.get("status", "") and o.get("status", "").lower() != "closed"
            ]
            open_amount = sum(
                float(o.get("amount", 0))
                for o in open_buy_orders
                if o.get("amount") is not None
            )
            unrealized_pnl = 0
            if open_amount > 0:
                ticker = self.client.get_ticker(symbol)
                current_price = (
                    float(ticker.get("last", 0))
                    if ticker and "last" in ticker and ticker.get("last") is not None
                    else 0.0
                )
                open_cost = sum(
                    float(o.get("amount", 0)) * float(o.get("price", 0))
                    for o in open_buy_orders
                    if o.get("amount") is not None and o.get("price") is not None
                )
                avg_open_price = open_cost / open_amount if open_amount > 0 else 0
                unrealized_pnl = (current_price - avg_open_price) * open_amount

            total_invested += invested_amount
            total_realized_pnl += realized_pnl
            total_unrealized_pnl += unrealized_pnl

            roi = (realized_pnl / invested_amount * 100) if invested_amount > 0 else 0.0
            print(f" • {symbol}:")
            print(f"   - Investido: ${invested_amount:.2f}")
            print(f"   - Lucro Realizado: ${realized_pnl:.2f}")
            print(f"   - Lucro Não Realizado: ${unrealized_pnl:.2f}")
            print(f"   - ROI Realizado: {roi:.2f}%")

        print("\n📈 Resumo Geral:")
        print("=" * 50)
        total_roi = (
            (total_realized_pnl / total_invested * 100) if total_invested > 0 else 0.0
        )
        print(f" • Total Investido: ${total_invested:.2f}")
        print(f" • Lucro/Prejuízo Realizado Total: ${total_realized_pnl:.2f}")
        print(f" • Lucro/Prejuízo Não Realizado Total: ${total_unrealized_pnl:.2f}")
        print(
            f" • Lucro/Prejuízo Total: ${(total_realized_pnl + total_unrealized_pnl):.2f}"
        )
        print(f" • ROI Realizado Total: {total_roi:.2f}%")

        metrics = self.calculate_advanced_metrics()
        print("\n📊 Métricas Avançadas:")
        print("=" * 50)
        for symbol in self.config.TRADING_SYMBOLS:
            metric = metrics.get(symbol, {})
            print(f" • {symbol}:")
            print(f"   - Sharpe Ratio: {metric.get('sharpe_ratio', 0.0):.2f}")
            print(f"   - Máximo Drawdown: {metric.get('max_drawdown', 0.0):.2f}%")
            print(f"   - Win Rate: {metric.get('win_rate', 0.0):.2f}%")
            print(f"   - Profit Factor: {metric.get('profit_factor', 0.0):.2f}")

    async def run_analysis(self):
        """Executa a análise completa de métricas e resumos."""
        print(
            f"\n🔄 Análise de Métricas - {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )
        self.display_invested_and_profits()


async def main():
    """Função principal para executar a análise de métricas."""
    logger = TradingLogger.get_logger(__name__)
    try:
        config = BotConfig()
        dashboard = AnalysisDashboard(config)
        await dashboard.run_analysis()
    except Exception as exc:
        logger.error("Erro durante a execução da análise: %s", str(exc))
        print(f"❌ Erro durante a análise: {exc}")


if __name__ == "__main__":
    asyncio.run(main())
