"""
Bot de trading automatizado para OKX Exchange.
"""

import warnings

warnings.filterwarnings("ignore", message="pkg_resources is deprecated as an API")

import asyncio
import os
import shutil
import json
import ccxt
import pandas as pd
import signal
import uuid
import time
import sqlite3
from dataclasses import dataclass
from decimal import Decimal, ROUND_HALF_UP
from dotenv import load_dotenv
from indicators.indicators import TechnicalIndicator
from service.service_alerts import SoundNotifications
from service.service_telegram import TelegramNotifier
from typing import Any, Dict, List, Optional, Tuple
from utils.logger import TradingLogger
from utils.database import TradingDatabase
from utils.exceptions import ConfigurationError, ExchangeConnectionError
from utils.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from utils.order_validator import OrderValidator
from core.okx_client import OKXClient
import time
from typing import Dict, List, Optional


@dataclass
class BotConfig:
    """Configurações centralizadas para o bot de trading."""

    SANDBOX_MODE: bool = True
    GRID_SYMBOLS = ["SOL/USDC"]
    SCRIPT_NAME: str = "Grid Bot"  # Add this missing attribute

    RSI_MIN = 30
    RSI_PERIOD: int = 30
    WMA_PERIOD: int = 84
    OHLCV_LIMIT: int = 100
    GRID_COUNT: int = 10
    RANGE_MULTIPLIER: float = 1.5
    MIN_ORDER_MULTIPLIER: int = 40
    SL_MULTIPLIER = 1.25
    ATR_MULTIPLIER: float = 1.55
    STABLECOINS: List[str] = None
    FIAT_DECIMAL_PLACES: int = 6
    CRYPTO_DECIMAL_PLACES: int = 8
    ENABLE_SOUND_NOTIFICATIONS: bool = True
    ENABLE_TELEGRAM_NOTIFICATIONS: bool = True
    RSI_PERIOD: int = 30
    WMA_PERIDOD: int = 30
    OHLCV_LIMIT: int = 100

    def __post_init__(self):
        """Inicializa valores padrão para símbolos de trading e outras configurações."""
        if self.GRID_SYMBOLS is None:
            self.GRID_SYMBOLS = ["SOL/USDC"]
        self._initialize_defaults()

    @classmethod
    def load_config(cls, file_path: str = "config.json") -> "BotConfig":
        """Carrega a configuração de um arquivo JSON."""
        try:
            with open(file_path, "r") as f:
                config_data = json.load(f)
                return cls(**config_data)
        except Exception as e:
            print(f"Falha ao carregar configuração: {e}")
            return cls()

    def _initialize_defaults(self):
        """Inicializa valores padrão de OKXClient para atributos não definidos."""
        from core.okx_client import OKXClient

        default_config = OKXClient().config
        # Obtém todos os atributos disponíveis no DefaultConfig
        default_attrs = [attr for attr in dir(default_config) if attr.isupper()]
        for attr in default_attrs:
            if (
                not hasattr(self, attr)
                or getattr(self, attr) is None
                or (
                    isinstance(getattr(self, attr), (int, float))
                    and getattr(self, attr) == 0
                )
            ):
                default_val = getattr(default_config, attr, None)
                if default_val is not None:
                    setattr(self, attr, default_val)
                    # print(f"Atributo {attr} inicializado com valor padrão: {default_val}")
        # Comentado o código de cache de ordens para evitar erro de 'cache_duration' não definido
        """
        Inicializa o cache de ordens.
        
        Args:
            cache_duration: Duração em segundos antes de atualizar o cache.
        """
        # self.cache_duration = cache_duration
        # self.open_orders: Dict[str, List[Dict]] = {}
        # self.closed_orders: Dict[str, List[Dict]] = {}
        # self.last_updated: Dict[str, float] = {}


class OrderCache:
    """Cache local para ordens, reduzindo chamadas à API da exchange."""

    def __init__(self, cache_duration: int = 30):
        """
        Inicializa o cache de ordens.

        Args:
            cache_duration: Duração em segundos antes de atualizar o cache.
        """
        self.cache_duration = cache_duration
        self.open_orders: Dict[str, List[Dict]] = {}
        self.closed_orders: Dict[str, List[Dict]] = {}
        self.last_updated: Dict[str, float] = {}

    def get_open_orders(self, symbol: str) -> Optional[List[Dict]]:
        """
        Obtém ordens abertas do cache se estiverem atualizadas.

        Args:
            symbol: Símbolo do par de trading.

        Returns:
            Lista de ordens abertas ou None se o cache estiver desatualizado.
        """
        if symbol in self.open_orders and symbol in self.last_updated:
            if time.time() - self.last_updated[symbol] < self.cache_duration:
                return self.open_orders[symbol]
        return None

    def get_closed_orders(self, symbol: str) -> Optional[List[Dict]]:
        """
        Obtém ordens fechadas do cache se estiverem atualizadas.

        Args:
            symbol: Símbolo do par de trading.

        Returns:
            Lista de ordens fechadas ou None se o cache estiver desatualizado.
        """
        if symbol in self.closed_orders and symbol in self.last_updated:
            if time.time() - self.last_updated[symbol] < self.cache_duration:
                return self.closed_orders[symbol]
        return None

    def update_orders(
        self, symbol: str, open_orders: List[Dict], closed_orders: List[Dict]
    ) -> None:
        """
        Atualiza o cache com novas ordens.

        Args:
            symbol: Símbolo do par de trading.
            open_orders: Lista de ordens abertas.
            closed_orders: Lista de ordens fechadas.
        """
        self.open_orders[symbol] = open_orders
        self.closed_orders[symbol] = closed_orders
        self.last_updated[symbol] = time.time()


load_dotenv()
FIAT_CURRENCIES = ["EUR", "USD"]


class OKXOrder:
    _instance = None
    _lock = asyncio.Lock()

    def __new__(cls, client: OKXClient = None):
        """Garante uma instância singleton de OKXOrder."""
        # Como __new__ é síncrono, não podemos usar 'await' diretamente aqui.
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, client: OKXClient = None):
        if getattr(self, "_initialized", False):
            return
        self._initialized = True
        self.client = client or OKXClient()
        self.logger = TradingLogger.get_logger(__name__)
        self.validator = OrderValidator(self.client)

    def validate_order_conditions(self, symbol: str) -> bool:
        """
        Valida se as condições estão adequadas para colocar uma ordem.

        Args:
            symbol: Par de trading

        Returns:
            True se condições estão OK, False caso contrário
        """
        return self.validator.validate_order_conditions(symbol, strategy_type="grid")

    def _generate_client_order_id(self) -> str:
        """Gera um ID de ordem de cliente único."""
        return uuid.uuid4().hex[:32]

    def has_active_grid(self, symbol: str) -> bool:
        """Verifica se há ordens de grid ativas para um símbolo."""
        try:
            open_orders = self.client.exchange.fetch_open_orders(symbol)
            if open_orders:
                self.logger.info("Ordens de grid ativas detectadas para %s", symbol)
                return True
            return False
        except Exception as exc:
            self.logger.error(
                "Erro ao verificar ordens ativas para %s: %s", symbol, str(exc)
            )
            return False

    async def wait_for_order_execution(
        self, order_id: str, symbol: str, timeout: int = 60, max_retries: int = 3
    ) -> Optional[Dict]:
        """Aguarda a execução ou cancelamento de uma ordem."""
        start_time = time.time()
        retries = 0
        while time.time() - start_time < timeout:
            try:
                order = self.client.exchange.fetch_order(order_id, symbol)
                if order["status"] == "closed":
                    self.logger.info(f"Ordem {order_id} executada com sucesso.")
                    return order
                elif order["status"] == "canceled":
                    self.logger.warning(f"Ordem {order_id} foi cancelada.")
                    return None
                await asyncio.sleep(2)
            except Exception as exc:
                retries += 1
                self.logger.error(
                    f"Erro ao verificar status da ordem {order_id} (Tentativa {retries}/{max_retries}): {exc}"
                )
                if retries >= max_retries:
                    self.logger.error(
                        f"Limite de tentativas atingido para a ordem {order_id}. Abortando."
                    )
                    return None
                await asyncio.sleep(3)
        self.logger.warning(
            f"Tempo esgotado ao esperar pela execução da ordem {order_id}."
        )
        return None

    async def place_grid_orders(
        self, symbol: str, indicator: TechnicalIndicator, timeframe: str = "15m"
    ) -> Optional[List[Dict]]:
        """Coloca ordens de grid para um símbolo com base no preço atual e na configuração."""
        try:
            best_bid = self.client.get_best_bid(symbol)
            if not best_bid:
                self.logger.error("Não foi possível obter melhor bid para %s", symbol)
                return None
            current_price = best_bid
            self.logger.debug("Preço atual obtido para %s: %s", symbol, current_price)
            balance = self.client.get_balance()
            quote_currency = symbol.split("/")[1]
            base_currency = symbol.split("/")[0]
            available_quote_balance = balance["total"].get(quote_currency, 0)
            available_base_balance = balance["total"].get(base_currency, 0)
            if available_quote_balance <= 0:
                self.logger.error(
                    "Saldo insuficiente em %s para %s", quote_currency, symbol
                )
                return None
            # Check if there is balance in the base currency for sell orders
            if available_base_balance <= 0:
                self.logger.warning(
                    "Sem saldo em %s para ordens de venda em %s. Colocando ordem de mercado para comprar metade do valor a investir.",
                    base_currency,
                    symbol,
                )
                # Calculate 50% of the 10% allocated balance for trading
                ten_percent_balance = available_quote_balance * 0.1
                market_buy_amount_in_quote = ten_percent_balance * 0.5
                market_buy_amount = market_buy_amount_in_quote / current_price
                formatted_buy_amount = self.client.format_amount_with_precision(
                    symbol, market_buy_amount
                )
                try:
                    market_order = self.client.exchange.create_order(
                        symbol=symbol,
                        type="market",
                        side="buy",
                        amount=float(formatted_buy_amount),
                        params={
                            "tdMode": "cash",
                            "clOrdId": self._generate_client_order_id(),
                        },
                    )
                    self.logger.debug(
                        "Ordem de mercado de compra enviada para %s: Quantidade %s a preço de mercado",
                        symbol,
                        formatted_buy_amount,
                    )
                    # Wait for order confirmation
                    order_id = market_order.get("id", None)
                    if order_id:
                        confirmed_order = await self.wait_for_order_execution(
                            order_id, symbol
                        )
                        if confirmed_order and confirmed_order["status"] == "closed":
                            self.logger.info(
                                "Ordem de mercado de compra confirmada para %s: ID %s",
                                symbol,
                                order_id,
                            )
                            # Atualizar saldo após compra
                            balance = self.client.get_balance()
                            available_base_balance = balance["total"].get(
                                base_currency, 0
                            )
                        else:
                            self.logger.error(
                                "Ordem de mercado de compra não confirmada para %s: ID %s",
                                symbol,
                                order_id,
                            )
                            return None
                    else:
                        self.logger.error(
                            "ID da ordem de mercado não disponível para %s", symbol
                        )
                        return None
                except Exception as market_exc:
                    self.logger.error(
                        "Erro ao executar ordem de mercado para %s: %s",
                        symbol,
                        str(market_exc),
                    )
                    return None
            else:
                self.logger.debug(
                    "Saldo disponível em %s: %s. Prosseguindo com ordens de grid.",
                    base_currency,
                    available_base_balance,
                )
            ten_percent_balance = available_quote_balance * 0.1
            total_investment = ten_percent_balance
            grid_count = self.client.config.GRID_COUNT
            range_multiplier = self.client.config.RANGE_MULTIPLIER
            range_size = current_price * (range_multiplier / 100)
            min_price = current_price - range_size
            max_price = current_price + range_size
            grid_spacing = (max_price - min_price) / grid_count
            amount_per_grid = total_investment / grid_count / current_price

            formatted_amount = self.client.format_amount_with_precision(
                symbol, amount_per_grid
            )
            if float(formatted_amount) <= 0:
                self.logger.error(
                    "Quantidade calculada insuficiente para %s: %s",
                    symbol,
                    formatted_amount,
                )
                return None

            # Check for minimum order size required by the exchange
            try:
                markets = self.client.exchange.load_markets()
                min_amount = (
                    markets[symbol]["limits"]["amount"]["min"]
                    if symbol in markets
                    else 0
                )
                adjusted_amount = float(formatted_amount)
                if min_amount and adjusted_amount < min_amount:
                    # Apply multiplier to ensure amount meets minimum requirement
                    multiplier = self.client.config.MIN_ORDER_MULTIPLIER
                    adjusted_amount = min_amount * multiplier
                    formatted_amount = self.client.format_amount_with_precision(
                        symbol, adjusted_amount
                    )
                    self.logger.info(
                        "Quantidade por grid ajustada para %s usando multiplicador %d para atender ao mínimo %s para %s",
                        formatted_amount,
                        multiplier,
                        min_amount,
                        symbol,
                    )
            except Exception as exc:
                self.logger.warning(
                    "Não foi possível verificar o tamanho mínimo do pedido para %s: %s",
                    symbol,
                    str(exc),
                )

            grid_orders = []
            print(f"Configuração de Grid para {symbol}:")
            print(f"  - Total de Grids: {grid_count + 1}")
            print(f"  - Multiplicador de Range: {range_multiplier}")
            print(f"  - Preço Mínimo: {min_price}")
            print(f"  - Preço Máximo: {max_price}")
            print(f"  - Espaçamento de Grid: {grid_spacing}")
            print(f"  - Quantidade por Grid: {amount_per_grid}")
            for i in range(grid_count + 1):
                price = min_price + (i * grid_spacing)
                formatted_price = self.client.format_price_with_precision(symbol, price)
                # Determine side based on whether the price is below or above current price
                side = "buy" if price < current_price else "sell"
                order = self.client.exchange.create_order(
                    symbol=symbol,
                    type="limit",
                    side=side,
                    amount=float(formatted_amount),
                    price=float(formatted_price),
                    params={
                        "tdMode": "cash",
                        "clOrdId": self._generate_client_order_id(),
                    },
                )
                if "amount" not in order or order["amount"] is None:
                    order["amount"] = float(formatted_amount)
                if "price" not in order or order["price"] is None:
                    order["price"] = float(formatted_price)
                self.logger.info(
                    "Ordem de grid criada com sucesso: %s, Lado: %s, Preço: %s",
                    order.get("id", "N/A"),
                    side,
                    formatted_price,
                )
                grid_orders.append(order)

            return grid_orders

        except Exception as exc:
            self.logger.error(
                "Erro ao colocar ordens de grid para %s: %s", symbol, str(exc)
            )
            return None

    def display_order_status_summary(self) -> None:
        """
        Exibe um resumo consolidado do status das ordens para todos os símbolos de trading.
        """
        print("\n📊 Resumo do Status das Ordens:")
        print("=" * 50)
        for symbol in self.client.config.GRID_SYMBOLS:
            try:
                regular_count = len(check_regular_orders(self.client, symbol))
                oco_count = len(self.check_oco_orders(symbol))
                total_orders = regular_count + oco_count

                if total_orders > 0:
                    status_emoji = "🟡"
                    print(f" {status_emoji} {symbol}:")
                    print(f"• Ordens Regulares: {regular_count}")
                    print(f"• Ordens OCO: {oco_count}")
                    print(f"• Total: {total_orders}")
                else:
                    print(f" 🟢 {symbol}: Sem ordens ativas")
            except Exception as exc:
                print(f" ❌ {symbol}: Erro ao verificar status - {exc}")
        print()

    def log_order_prevention_details(self, symbol: str) -> None:
        """Registra detalhes de prevenção de ordem devido a ordens de grid ativas - comentado conforme solicitação do usuário."""
        pass


class Sinais:
    def __init__(self, indicator: TechnicalIndicator, client: OKXClient):
        """Inicializa a classe Sinais com indicador técnico e cliente."""
        self.indicator = indicator
        self.client = client
        self.logger = TradingLogger.get_logger(__name__)

    def check_entry_signal(self, symbol: str, timeframe: str = None) -> bool:
        """Verifica se há um sinal de entrada com base nos indicadores RSI e WMA."""
        if timeframe is None:
            timeframe = self.client.config.TIMEFRAME
        try:
            ticker = self.client.get_ticker(symbol)
            if not ticker or "last" not in ticker:
                self.logger.error(f"Não há dados de ticker para {symbol}")
                return False
            price = ticker["last"]
            rsi = self.indicator.calculate_rsi(
                symbol, timeframe, period=self.client.config.RSI_PERIOD
            )
            wma = self.indicator.calculate_wma(
                symbol, timeframe, period=self.client.config.WMA_PERIOD
            )
            if rsi is None or wma is None:
                self.logger.debug(f"Dados do indicador em falta para {symbol}")
                return False
            if rsi > self.client.config.RSI_MIN and price > wma:
                self.logger.info(
                    f"\033[92mSinal de entrada detetado para {symbol}\033[0m"
                )
                return True
            return False
        except Exception as exc:
            self.logger.error(f"Erro ao verificar sinal para {symbol}: {exc}")
            return False

    def print_signal(self, symbol: str, timeframe: str = None) -> None:
        """Imprime o sinal de trading para um símbolo com base nos indicadores atuais."""
        if timeframe is None:
            timeframe = self.client.config.TIMEFRAME
        try:
            ticker = self.client.get_ticker(symbol)
            if not ticker or "last" not in ticker:
                self.logger.warning(f" • {symbol}: Dados de preço indisponíveis")
                return
            price = ticker["last"]
            rsi = self.indicator.calculate_rsi(
                symbol, timeframe, period=self.client.config.RSI_PERIOD
            )
            wma = self.indicator.calculate_wma(
                symbol, timeframe, period=self.client.config.WMA_PERIOD
            )
            if rsi is None or wma is None:
                self.logger.warning(f" • {symbol}: Indicadores indisponíveis")
                return
            if rsi > self.client.config.RSI_MIN and price > wma:
                signal = "BUY"
                color = "\033[92m"
            else:
                signal = "HOLD"
                color = "\033[93m"
            self.logger.info(f" • GRID |  {symbol}: SINAL = {signal}")
            self.logger.info(f"   Preço: ${price:.2f}")
        except Exception as exc:
            self.logger.error(f"Erro ao imprimir sinal para {symbol}: {exc}")


class DataManager:
    def __init__(self, config: Optional[BotConfig] = None):
        """Inicializa o DataManager com configuração e banco de dados."""
        self.config = config or BotConfig()
        self.logger = TradingLogger.get_logger(__name__)
        self.db = self._initialize_database()
        self.order_cache = OrderCache(cache_duration=30)

    def _initialize_database(self) -> TradingDatabase:
        """Inicializa o banco de dados para armazenar dados de trading."""
        db_name = "sandbox_trades.db" if self.config.SANDBOX_MODE else "live_trades.db"
        db_path = os.path.join("trades", db_name)
        try:
            return TradingDatabase(db_path)
        except Exception as exc:
            self.logger.error("Falha ao inicializar banco de dados: %s", str(exc))
            raise

    def save_order_to_db(self, order: Dict) -> None:
        """Salva uma ordem no banco de dados."""
        try:
            self.db.save_order(order)
            self.logger.info(
                "Ordem salva no banco de dados: %s", order.get("id", "N/A")
            )
        except Exception as exc:
            self.logger.error("Erro ao salvar ordem no banco de dados: %s", str(exc))

    async def sync_and_calculate_metrics(self, client):
        """
        Sincroniza ordens abertas e fechadas da exchange para o DB local
        e, em seguida, executa o cálculo de métricas. Usa cache para minimizar chamadas à API.
        Otimiza a sincronização agrupando requisições e recalcula métricas apenas se houver mudanças.
        Comentado o uso de OrderCache para evitar erro de 'OrderCache' não definido.
        """
        # if not hasattr(self, 'order_cache'):
        #     self.order_cache = OrderCache(cache_duration=30)

        if not hasattr(self, "last_order_counts"):
            self.last_order_counts = {}

        symbols_to_update = []
        for symbol in client.config.GRID_SYMBOLS:
            # Tentar obter ordens do cache - comentado
            # open_orders = self.order_cache.get_open_orders(symbol)
            # closed_orders = self.order_cache.get_closed_orders(symbol)

            # if open_orders is None or closed_orders is None:
            symbols_to_update.append(symbol)
            # else:
                # self.logger.debug("Usando ordens do cache para %s", symbol)

        if symbols_to_update:
            # Agrupar requisições para múltiplos símbolos, se possível
            for symbol in symbols_to_update:
                max_retries = 3
                open_orders = []
                closed_orders = []
                for attempt in range(max_retries):
                    try:
                        open_orders = client.exchange.fetch_open_orders(symbol)
                        break
                    except Exception as exc:
                        self.logger.error(
                            "Erro ao obter ordens abertas para %s (tentativa %d/%d): %s",
                            symbol,
                            attempt + 1,
                            max_retries,
                            str(exc),
                        )
                        if attempt == max_retries - 1:
                            self.logger.error(
                                "Falha ao obter ordens abertas para %s após %d tentativas",
                                symbol,
                                max_retries,
                            )
                        time.sleep(2)
                for attempt in range(max_retries):
                    try:
                        closed_orders = client.exchange.fetch_closed_orders(symbol)
                        break
                    except Exception as exc:
                        self.logger.error(
                            "Erro ao obter ordens fechadas para %s (tentativa %d/%d): %s",
                            symbol,
                            attempt + 1,
                            max_retries,
                            str(exc),
                        )
                        if attempt == max_retries - 1:
                            self.logger.error(
                                "Falha ao obter ordens fechadas para %s após %d tentativas",
                                symbol,
                                max_retries,
                            )
                        time.sleep(2)
                # Atualizar cache - comentado
                self.order_cache.update_orders(symbol, open_orders, closed_orders)
                self.logger.debug("Cache de ordens atualizado para %s", symbol)

                all_orders = open_orders + closed_orders
                for order in all_orders:
                    if not self.db.get_order_by_id(order["id"]):
                        self.db.save_order(order)

        # Verificar se houve mudanças nas ordens para decidir se recalcula métricas
        should_recalculate = False
        for symbol in client.config.GRID_SYMBOLS:
            # open_orders = self.order_cache.get_open_orders(symbol) or []
            # closed_orders = self.order_cache.get_closed_orders(symbol) or []
            total_orders = len(open_orders) + len(closed_orders)

            if (
                symbol not in self.last_order_counts
                or self.last_order_counts[symbol] != total_orders
            ):
                should_recalculate = True
                self.last_order_counts[symbol] = total_orders

        if should_recalculate:
            self.logger.info("Mudanças detectadas nas ordens, recalculando métricas.")
            self.calculate_investment_metrics(client)
        else:
            self.logger.info(
                "Nenhuma mudança nas ordens, pulando recálculo de métricas."
            )

    def calculate_investment_metrics(self, client: OKXClient) -> None:
        """
        Calcula e exibe métricas de investimento por símbolo, incluindo valores investidos e lucros reais.
        """
        print("\n📊 Métricas de Investimento por Símbolo:")
        print("=" * 50)
        try:
            all_orders = self.db.get_orders(limit=1000)
            if not all_orders:
                print(" Nenhuma ordem encontrada no banco de dados.")
                return

            orders_by_symbol = {}
            for order in all_orders:
                symbol = order.get("symbol", "N/A")
                if symbol not in orders_by_symbol:
                    orders_by_symbol[symbol] = []
                orders_by_symbol[symbol].append(order)

            total_invested = 0
            total_realized_pnl = 0
            total_unrealized_pnl = 0

            for symbol, orders in orders_by_symbol.items():
                if symbol == "N/A":
                    continue

                buy_orders = [
                    o
                    for o in orders
                    if o.get("side", "") and o.get("side", "").lower() == "buy"
                ]
                invested_amount = 0.0
                for o in buy_orders:
                    amount = o.get("amount")
                    price = o.get("price")
                    if amount is not None and price is not None:
                        try:
                            invested_amount += float(amount) * float(price)
                        except (ValueError, TypeError):
                            continue

                sell_orders = [
                    o
                    for o in orders
                    if o.get("side", "")
                    and o.get("side", "").lower() == "sell"
                    and o.get("status", "")
                    and o.get("status", "").lower() == "closed"
                ]
                realized_pnl = 0.0
                for sell_order in sell_orders:
                    sell_amount = sell_order.get("amount")
                    sell_price = sell_order.get("price")
                    if sell_amount is None or sell_price is None:
                        continue
                    try:
                        sell_amount = float(sell_amount)
                        sell_price = float(sell_price)
                    except (ValueError, TypeError):
                        continue
                    buy_amount_total = 0.0
                    buy_cost_total = 0.0
                    for buy_order in buy_orders:
                        buy_amount = buy_order.get("amount")
                        buy_price = buy_order.get("price")
                        if buy_amount is None or buy_price is None:
                            continue
                        try:
                            buy_amount = float(buy_amount)
                            buy_price = float(buy_price)
                        except (ValueError, TypeError):
                            continue
                        if buy_amount_total + buy_amount <= sell_amount:
                            buy_amount_total += buy_amount
                            buy_cost_total += buy_amount * buy_price
                        else:
                            remaining = sell_amount - buy_amount_total
                            buy_amount_total += remaining
                            buy_cost_total += remaining * buy_price
                            break
                    if buy_amount_total > 0:
                        avg_buy_price = buy_cost_total / buy_amount_total
                        realized_pnl += (sell_price - avg_buy_price) * sell_amount

                open_buy_orders = [
                    o
                    for o in buy_orders
                    if o.get("status", "") and o.get("status", "").lower() != "closed"
                ]
                open_amount = 0.0
                for o in open_buy_orders:
                    amount = o.get("amount")
                    if amount is not None:
                        try:
                            open_amount += float(amount)
                        except (ValueError, TypeError):
                            continue
                if open_amount > 0:
                    ticker = client.get_ticker(symbol)
                    current_price = 0.0
                    if ticker and "last" in ticker and ticker.get("last") is not None:
                        try:
                            current_price = float(ticker.get("last", 0))
                        except (ValueError, TypeError):
                            pass
                    open_cost = 0.0
                    for o in open_buy_orders:
                        amount = o.get("amount")
                        price = o.get("price")
                        if amount is not None and price is not None:
                            try:
                                open_cost += float(amount) * float(price)
                            except (ValueError, TypeError):
                                continue
                    avg_open_price = open_cost / open_amount if open_amount > 0 else 0
                    unrealized_pnl = (current_price - avg_open_price) * open_amount
                else:
                    unrealized_pnl = 0.0

                total_invested += invested_amount
                total_realized_pnl += realized_pnl
                total_unrealized_pnl += unrealized_pnl

                self.logger.info(f" 🪙 {symbol}:")
                self.logger.info(f"• Valor Investido: ${invested_amount:.2f}")
                self.logger.info(f"• Lucro/Prejuízo Realizado: ${realized_pnl:.2f}")
                self.logger.info(
                    f"• Lucro/Prejuízo Não Realizado: ${unrealized_pnl:.2f}"
                )
                self.logger.info(" " + "-" * 30)

            print("\n📈 Resumo Geral:")
            print("=" * 50)
            self.logger.info(f" • Total Investido: ${total_invested:.2f}")
            self.logger.info(
                f" • Lucro/Prejuízo Realizado Total: ${total_realized_pnl:.2f}"
            )
            self.logger.info(
                f" • Lucro/Prejuízo Não Realizado Total: ${total_unrealized_pnl:.2f}"
            )
            self.logger.info(
                f" • Lucro/Prejuízo Total: ${(total_realized_pnl + total_unrealized_pnl):.2f}"
            )

        except Exception as exc:
            self.logger.error("Erro ao calcular métricas de investimento: %s", str(exc))
            print(f" ❌ Erro ao calcular métricas: {exc}")

    async def sync_orders(self, client: OKXClient, symbol: str):
        """Sincroniza ordens da exchange para o banco de dados local."""
        exchange_orders = await client.exchange.fetch_open_orders(symbol)
        local_orders = self.db.get_open_orders(symbol)
        for order in exchange_orders:
            if order["id"] not in [o["id"] for o in local_orders]:
                self.db.add_order(order)
        self.logger.debug("Ordens sincronizadas para %s", symbol)


class NotificationManager:
    def __init__(self, config: Optional[BotConfig] = None):
        """Inicializa o NotificationManager com configuração."""
        self.config = config or BotConfig()
        self.logger = TradingLogger.get_logger(__name__)
        self.sound_alert = self._initialize_sound_notifications()
        self.notifier = self._initialize_telegram_notifier()

    def _initialize_sound_notifications(self) -> Optional[SoundNotifications]:
        """Inicializa notificações sonoras se habilitado na configuração."""
        if self.config.ENABLE_SOUND_NOTIFICATIONS:
            try:
                return SoundNotifications()
            except Exception as exc:
                self.logger.warning(
                    "Falha ao inicializar notificações sonoras: %s", str(exc)
                )
        return None

    def _initialize_telegram_notifier(self) -> Optional[TelegramNotifier]:
        """Inicializa o notificador Telegram se habilitado na configuração."""
        if self.config.ENABLE_TELEGRAM_NOTIFICATIONS:
            try:
                return TelegramNotifier()
            except Exception as exc:
                self.logger.warning(
                    "Falha ao inicializar notificador Telegram: %s", str(exc)
                )
        return None

    async def play_alert(self, alert_type: str, volume: float = 0.5) -> None:
        """Reproduz um alerta sonoro se as notificações sonoras estiverem habilitadas."""
        if self.sound_alert:
            try:
                self.sound_alert.play_notification(alert_type, volume=volume)
            except Exception as exc:
                self.logger.warning("Falha ao reproduzir alerta: %s", str(exc))

    async def send_telegram_notification(self, message: str) -> None:
        """Envia uma notificação Telegram se habilitado."""
        if self.notifier:
            try:
                await self.notifier.send_message(message)
            except Exception as exc:
                self.logger.warning(
                    "Falha ao enviar notificação Telegram: %s", str(exc)
                )


class TradingBot:
    def __init__(self, config: Optional[BotConfig] = None):
        """Inicializa o TradingBot com configuração e componentes necessários."""
        self.config = config or BotConfig()
        self.logger = TradingLogger.get_logger(__name__)
        self.data_manager = DataManager(self.config)
        self.notification_manager = NotificationManager(self.config)
        self.created_orders = []

    async def display_orders(self) -> None:
        """Exibe ordens recentes - comentado conforme solicitação do usuário."""
        pass

    async def _calculate_all_indicators(
        self, symbol: str, indicator: TechnicalIndicator
    ) -> Dict[str, Any]:
        """Calcula todos os indicadores para um símbolo."""
        return {
            "rsi": indicator.calculate_rsi(symbol),
            "adx": indicator.calculate_adx(symbol),
            "wma": indicator.calculate_wma(symbol),
            "sma": indicator.calculate_sma(symbol),
            "atr": indicator.calculate_atr(symbol),
        }


async def initialize_bot_components() -> Tuple[OKXClient, OKXOrder, TradingBot]:
    """Inicializa os componentes do bot."""
    try:
        config = BotConfig()
        config.__post_init__()  # Garantir que os valores padrão sejam inicializados
        client = OKXClient(config)
        order_manager = OKXOrder(client)
        bot = TradingBot(config)
        return client, order_manager, bot
    except Exception as exc:
        logger = TradingLogger.get_logger(__name__)
        logger.error("Falha ao inicializar componentes do bot: %s", str(exc))
        raise ConfigurationError("Falha na inicialização do bot") from exc


async def send_startup_notification(client: OKXClient, bot: TradingBot) -> None:
    """Envia notificação de inicialização via Telegram se habilitado."""
    if not bot.config.ENABLE_TELEGRAM_NOTIFICATIONS:
        return
    try:
        balance = client.get_balance()
        message = await build_startup_message(balance, bot.config)
        await bot.notification_manager.send_telegram_notification(message)
    except Exception as exc:
        logger = TradingLogger.get_logger(__name__)
        logger.error("Erro ao enviar notificação de inicialização: %s", str(exc))


async def build_startup_message(balance: Optional[Dict], config: BotConfig) -> str:
    """Constrói mensagem de inicialização para notificação."""
    mode_text = "🟢 LIVE TRADING" if not config.SANDBOX_MODE else "🔴 SANDBOX MODE"
    message = f"""🚀 *Grid Spot Iniciado*
-----------------------------
• *Mode*: {mode_text}
• *Balance*:
💰 Saldo Disponível:
"""
    if balance and balance.get("total"):
        for asset, amount in balance["total"].items():
            if amount > 0:
                message += f" 🪙 {asset}: {amount:.8f}\n"
    return message


async def main() -> None:
    logger = TradingLogger.get_logger(__name__)

    def handle_sigstp(signum, frame):
        logger.info("Bot interrompido manualmente pelo usuário (SIGTSTP).")
        print("\n🛑 Bot interrompido manualmente (Ctrl+Z).")
        print("A finalizar o bot. Até breve!")
        exit(0)

    try:
        client, order_manager, bot = await initialize_bot_components()
        await send_startup_notification(client, bot)
        await bot.notification_manager.play_alert("start", volume=0.25)
        signal.signal(signal.SIGTSTP, handle_sigstp)
        indicator = TechnicalIndicator(client)
        for symbol in client.config.GRID_SYMBOLS:
            ohlcv_data = indicator.fetch_historical_data(
                symbol, client.config.TIMEFRAME
            )
        cycle_count = 0
        while True:
            cycle_count += 1
            if cycle_count == 1:
                client._print_mode_message()
            print(
                f"\n🔄 Ciclo #{cycle_count} - {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
            client.display_prices_with_precision()
            client.display_balance_with_precision()
            # await bot.display_orders()
            await bot.data_manager.sync_and_calculate_metrics(client)
            # Process closed orders and send notifications
            for symbol in client.config.GRID_SYMBOLS:
                try:
                    closed_orders = client.exchange.fetch_closed_orders(symbol, limit=5)
                    for order in closed_orders:
                        order_id = order.get("id", "N/A")
                        # Check if order has been notified to avoid duplicates
                        if not bot.data_manager.db.is_order_notified(order_id):
                            if (
                                order.get("side") == "sell"
                                and order.get("status") == "closed"
                            ):
                                buy_order = bot.data_manager.db.get_matching_buy_order(
                                    order_id, symbol
                                )
                                if buy_order:
                                    try:
                                        buy_price = float(
                                            buy_order.get("price", 0.0) or 0.0
                                        )
                                        sell_price = float(
                                            order.get("price", 0.0) or 0.0
                                        )
                                        amount = min(
                                            float(buy_order.get("amount", 0.0) or 0.0),
                                            float(order.get("amount", 0.0) or 0.0),
                                        )
                                        pnl = (sell_price - buy_price) * amount
                                        # Save PnL to database
                                        order_with_pnl = order.copy()
                                        order_with_pnl["pnl"] = pnl
                                        bot.data_manager.save_order_to_db(
                                            order_with_pnl
                                        )
                                        # Save to trade history
                                        trade_record = {
                                            "symbol": symbol,
                                            "buy_order_id": buy_order.get("id"),
                                            "sell_order_id": order_id,
                                            "buy_price": buy_price,
                                            "sell_price": sell_price,
                                            "amount": amount,
                                            "pnl": pnl,
                                            "open_timestamp": buy_order.get(
                                                "timestamp"
                                            ),
                                        }
                                        with sqlite3.connect(
                                            bot.data_manager.db.db_path
                                        ) as conn:
                                            cursor = conn.cursor()
                                            cursor.execute(
                                                """
                                                INSERT INTO trade_history 
                                                (symbol, buy_order_id, sell_order_id, buy_price, sell_price, amount, pnl, open_timestamp)
                                                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                                                """,
                                                (
                                                    trade_record["symbol"],
                                                    trade_record["buy_order_id"],
                                                    trade_record["sell_order_id"],
                                                    trade_record["buy_price"],
                                                    trade_record["sell_price"],
                                                    trade_record["amount"],
                                                    trade_record["pnl"],
                                                    trade_record["open_timestamp"],
                                                ),
                                            )
                                            conn.commit()
                                            logger.debug(
                                                "Trade history saved for sell order %s",
                                                order_id,
                                            )

                                        message = f"""📉 *GRID \\- POSIÇÃO FECHADA \\- {symbol}*
• *Bot*: {config.SCRIPT_NAME}
• *Tipo*: Venda
• *Quantidade*: {amount}
• *Preço de Compra*: ${buy_price:.2f}
• *Preço de Venda*: ${sell_price:.2f}
• *PnL*: ${pnl:.2f}"""
                                        await bot.notification_manager.send_telegram_notification(
                                            message
                                        )
                                        await bot.notification_manager.play_alert(
                                            (
                                                "win"
                                                if sell_price > buy_price
                                                else "loose"
                                            ),
                                            volume=0.7,
                                        )
                                        bot.data_manager.db.mark_order_as_notified(
                                            order_id
                                        )
                                    except (ValueError, TypeError) as e:
                                        logger.warning(
                                            "Error processing order %s for %s: %s",
                                            order_id,
                                            symbol,
                                            str(e),
                                        )
                                        message = f"""📉 *GRID \\- POSIÇÃO FECHADA \\- {symbol}*
• *Bot*: {config.SCRIPT_NAME}
• *Tipo*: Venda
• *Quantidade*: {order.get("amount", 0.0)}
• *Preço de Venda*: ${order.get("price", 0.0) if order.get("price") else 0.0:.2f}
• *Note*: Error processing data"""
                                        await bot.notification_manager.send_telegram_notification(
                                            message
                                        )
                                        bot.data_manager.db.mark_order_as_notified(
                                            order_id
                                        )
                                else:
                                    logger.warning(
                                        "Sell order without matching buy order for %s: ID=%s",
                                        symbol,
                                        order_id,
                                    )
                                    sell_price = float(order.get("price", 0.0) or 0.0)
                                    message = f"""📉 *GRID \\- POSIÇÃO FECHADA \\- {symbol}*
• *Bot*: {config.SCRIPT_NAME}
• *Tipo*: Venda
• *Quantidade*: {order.get("amount", 0.0)}
• *Preço de Venda*: ${sell_price:.2f}
• *PnL*: N/A (Sem ordem de compra correspondente)
• *ID da Ordem*: {order_id}"""
                                    await bot.notification_manager.send_telegram_notification(
                                        message
                                    )
                                    bot.data_manager.db.mark_order_as_notified(order_id)
                            elif (
                                order.get("status") == "closed"
                                and order.get("side") == "buy"
                            ):
                                message = f"""📈 *GRID \\- POSIÇÃO ABERTA \\- {symbol}*
• *Tipo*: Compra
• *Quantidade*: {order.get("amount", 0.0)}
• *Preço*: ${order.get("price", 0.0):.2f}"""
                                await bot.notification_manager.send_telegram_notification(
                                    message
                                )
                                bot.data_manager.db.mark_order_as_notified(order_id)
                except Exception as exc:
                    logger.error(
                        "Error checking closed orders for %s: %s",
                        symbol,
                        str(exc),
                    )
            # display_regular_orders(client)
            # display_oco_orders(client)

            print("\n🤖 Verificando Sinais de Trading:")
            print("=" * 50)
            checker = Sinais(indicator, client)
            for symbol in client.config.GRID_SYMBOLS:
                try:
                    has_buy_signal = checker.check_entry_signal(
                        symbol, checker.client.config.TIMEFRAME
                    )
                    checker.print_signal(symbol, checker.client.config.TIMEFRAME)
                    # Verificar se houve mudanças (vendas ou compras) desde o último ciclo
                    last_orders = bot.data_manager.db.get_orders(limit=10)
                    has_recent_change = False
                    recent_order_price = None
                    recent_order_side = None
                    for order in last_orders:
                        if (
                            order.get("symbol") == symbol
                            and order.get("status") == "closed"
                        ):
                            has_recent_change = True
                            recent_order_price = order.get("price")
                            recent_order_side = order.get("side")
                            break
                    if order_manager.has_active_grid(symbol):
                        print(
                            f"⏳ {symbol}: Grid ativo detectado. Aguardando fechamento..."
                        )
                        if (
                            has_recent_change
                            and recent_order_price
                            and recent_order_side
                        ):
                            logger.info(
                                f"🔄 Recalculando ordens para {symbol} devido a mudanças recentes."
                            )
                            # Ajustar grid com base na última ordem executada
                            try:
                                current_price = float(recent_order_price)
                                grid_count = client.config.GRID_COUNT
                                range_multiplier = client.config.RANGE_MULTIPLIER
                                range_size = current_price * (range_multiplier / 100)
                                grid_spacing = range_size * 2 / grid_count
                                # Atualizar saldo antes de criar ordens
                                balance = client.get_balance()
                                quote_currency = symbol.split("/")[1]
                                base_currency = symbol.split("/")[0]
                                available_quote_balance = balance["total"].get(
                                    quote_currency, 0
                                )
                                available_base_balance = balance["total"].get(
                                    base_currency, 0
                                )
                                if recent_order_side.lower() == "buy":
                                    # Após compra, colocar ordem de venda um grid acima
                                    sell_price = current_price + grid_spacing
                                    formatted_sell_price = (
                                        client.format_price_with_precision(
                                            symbol, sell_price
                                        )
                                    )
                                    formatted_amount = (
                                        client.format_amount_with_precision(
                                            symbol,
                                            available_base_balance / (grid_count / 2),
                                        )
                                    )
                                    sell_order = client.exchange.create_order(
                                        symbol=symbol,
                                        type="limit",
                                        side="sell",
                                        amount=float(formatted_amount),
                                        price=float(formatted_sell_price),
                                        params={
                                            "tdMode": "cash",
                                            "clOrdId": order_manager._generate_client_order_id(),
                                        },
                                    )
                                    if (
                                        "amount" not in sell_order
                                        or sell_order["amount"] is None
                                    ):
                                        sell_order["amount"] = float(formatted_amount)
                                    if (
                                        "price" not in sell_order
                                        or sell_order["price"] is None
                                    ):
                                        sell_order["price"] = float(
                                            formatted_sell_price
                                        )
                                    logger.info(
                                        f"Ordem de venda criada após compra: Preço: {formatted_sell_price}"
                                    )
                                    bot.data_manager.save_order_to_db(sell_order)
                                    bot.created_orders.append(sell_order)
                                    await bot.notification_manager.play_alert(
                                        "success", volume=0.7
                                    )
                                    pnl = (
                                        float(formatted_sell_price)
                                        - float(recent_order_price)
                                    ) * float(formatted_amount)
                                    message = f"""🎯 *Grid Base \\- ORDEM DE VENDA CRIADA*
• *Símbolo*: {symbol}
• *Tipo*: Limit Sell
• *Preço*: {formatted_sell_price}
• *PnL Estimado*: ${pnl:.2f}"""
                                    await bot.notification_manager.send_telegram_notification(
                                        message
                                    )
                                elif recent_order_side.lower() == "sell":
                                    # Após venda, colocar ordem de compra um grid abaixo
                                    buy_price = current_price - grid_spacing
                                    formatted_buy_price = (
                                        client.format_price_with_precision(
                                            symbol, buy_price
                                        )
                                    )
                                    buy_amount_in_quote = (
                                        available_quote_balance * 0.1 / (grid_count / 2)
                                    )
                                    buy_amount = buy_amount_in_quote / current_price
                                    formatted_buy_amount = (
                                        client.format_amount_with_precision(
                                            symbol, buy_amount
                                        )
                                    )
                                    buy_order = client.exchange.create_order(
                                        symbol=symbol,
                                        type="limit",
                                        side="buy",
                                        amount=float(formatted_buy_amount),
                                        price=float(formatted_buy_price),
                                        params={
                                            "tdMode": "cash",
                                            "clOrdId": order_manager._generate_client_order_id(),
                                        },
                                    )
                                    if (
                                        "amount" not in buy_order
                                        or buy_order["amount"] is None
                                    ):
                                        buy_order["amount"] = float(
                                            formatted_buy_amount
                                        )
                                    if (
                                        "price" not in buy_order
                                        or buy_order["price"] is None
                                    ):
                                        buy_order["price"] = float(formatted_buy_price)
                                    logger.info(
                                        f"Ordem de compra criada após venda: Preço: {formatted_buy_price}"
                                    )
                                    bot.data_manager.save_order_to_db(buy_order)
                                    bot.created_orders.append(buy_order)
                                    await bot.notification_manager.play_alert(
                                        "success", volume=0.7
                                    )
                                    pnl = (
                                        float(formatted_buy_price)
                                        - float(recent_order_price)
                                    ) * float(formatted_buy_amount)
                                    message = f"""🎯 *Grid Base \\- ORDEM DE COMPRA CRIADA*
• *Símbolo*: {symbol}
• *Tipo*: Limit Buy
• *Preço*: {formatted_buy_price}
• *PnL Estimado*: ${pnl:.2f}"""
                                    await bot.notification_manager.send_telegram_notification(
                                        message
                                    )
                            except Exception as exc:
                                logger.error(
                                    f"Erro ao criar ordem após mudança para {symbol}: {str(exc)}"
                                )
                                await bot.notification_manager.play_alert(
                                    "error", volume=0.5
                                )
                        continue
                    if has_buy_signal:
                        logger.info(f"\n🚨 SINAL DE COMPRA DETECTADO PARA {symbol}!")
                        if order_manager.validate_order_conditions(symbol):
                            logger.info(f"✅ Condições validadas para {symbol}")
                            order_results = await order_manager.place_grid_orders(
                                symbol=symbol, indicator=indicator
                            )
                            if order_results:
                                print(
                                    f"🎯 Grid Base \\- ORDENS EXECUTADAS COM SUCESSO!"
                                )
                                for order in order_results:
                                    print(f"• Símbolo: {symbol}")
                                    print(f"• Lado: {order['side']}")
                                    print(f"• Quantidade: {order['amount']}")
                                    print(f"• Preço: ${order['price']}")
                                    print(f"• ID da ordem: {order.get('id', 'N/A')}")
                                    bot.data_manager.save_order_to_db(order)
                                    bot.created_orders.append(order)
                                await bot.notification_manager.play_alert(
                                    "success", volume=0.7
                                )
                                message = f"""🎯 *Grid Base \\- ORDENS EXECUTADAS*
• *Símbolo*: {symbol}
• *Tipo*: Grid
• *Total Ordens*: {len(order_results)}
"""
                                await bot.notification_manager.send_telegram_notification(
                                    message
                                )
                            else:
                                logger.error(
                                    f"❌ Falha ao executar ordens para {symbol}"
                                )
                                await bot.notification_manager.play_alert(
                                    "error", volume=0.5
                                )
                        else:
                            logger.warning(f"⚠️ Condições não atendidas para {symbol}")
                            logger.debug(
                                f"    (Pode haver ordens ativas ou saldo insuficiente)"
                            )
                    else:
                        logger.info(f"   → Sem sinal de compra para {symbol}")
                        print()
                except Exception as exc:
                    logger.error("Erro ao processar %s: %s", symbol, str(exc))
                    logger.error(f"❌ Erro ao processar {symbol}: {exc}")
            print(f"\n⏰ Aguardando próximo ciclo (60 segundos)...")
            await asyncio.sleep(60)
    except KeyboardInterrupt:
        logger.info("Bot interrompido manualmente pelo usuário.")
        print("\n🛑 Bot interrompido manualmente.")
        if "bot" in locals():
            await bot.notification_manager.play_alert("exit", volume=0.9)
        print("A finalizar o bot. Até breve!")
        return
    except (ConfigurationError, ExchangeConnectionError) as exc:
        logger.error("Erro de configuração/conexão: %s", exc)
        logger.error(f"❌ Erro: {exc}")
        logger.error("Por favor, verifique as suas credenciais e configuração.")
    except Exception as exc:
        logger.error("Erro inesperado: %s", exc)
        logger.exception(exc)
        logger.error(f"❌ Erro inesperado: {exc}")
        logger.error("Verifique os logs para mais detalhes.")


if __name__ == "__main__":
    asyncio.run(main())
