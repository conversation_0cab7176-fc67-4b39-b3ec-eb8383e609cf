<style>
    :root {
        --dark-green: #006b32;
        --light-green: #00983b;
        --green-9: hsl(149, 66%, 13%);
        --green-8: hsl(148, 76%, 20%);
        --green-7: hsl(148, 100%, 21%);
        --green-6: hsl(143, 100%, 25%);
        --green-5: hsl(143, 70%, 42%);
        --green-4: hsl(143, 71%, 53%);
        --green-3: hsla(143, 71%, 53%, 0.462);
        --green-1: hsla(149, 94%, 35%, 0.158);
        --step--2: clamp(0.6944rem, 0.7943rem + -0.1289vw, 0.7686rem);
        --step--1: clamp(0.8201rem, 0.8154rem + 0.0231vw, 0.8333rem);
        --step-0: clamp(0.875rem, 0.8315rem + 0.2174vw, 1rem);
        --step-1: clamp(0.9336rem, 0.841rem + 0.4633vw, 1.2rem);
        --step-2: clamp(0.9962rem, 0.8418rem + 0.7719vw, 1.44rem);
        --step-3: clamp(1.0629rem, 0.8316rem + 1.1567vw, 1.728rem);
        --step-4: clamp(1.1341rem, 0.8074rem + 1.6338vw, 2.0736rem);
        --step-5: clamp(1.2101rem, 0.7655rem + 2.2229vw, 2.4883rem);
    }

    .section_categoria-grafico {
        display: flex;
        justify-content: center;
        align-items: center;
        overflow-x: hidden;
        margin-block-start: calc(var(--step-5) * 2);
    }

    .infographic {
        background: var(--green-1);
        padding: var(--step-3);
        border-radius: 15px;
        box-shadow: 0 0 20px var(--green-3);
        width: 450px;
        max-width: 90%;
        backdrop-filter: blur(10px);
        border: 1px solid var(--green-5);
    }

    .infographic h1 {
        text-align: center;
        color: var(--green-8);
        font-size: 28px;
        margin-bottom: 30px;
        letter-spacing: 2px;
        text-shadow: 0 0 10px var(--green-3);
    }

    .categoria {
        margin-block-start: var(--step-0);
        opacity: 0;
        animation: fadeIn 0.5s ease-in-out forwards;
    }

    .categoria:nth-child(1) {
        animation-delay: 0.2s;
    }

    .categoria:nth-child(2) {
        animation-delay: 0.4s;
    }

    .categoria:nth-child(3) {
        animation-delay: 0.6s;
    }

    .categoria h2 {
        margin: 0 0 8px 0;
        font-size: 20px;
        color: var(--green-8);
        letter-spacing: 1px;
    }

    .bar-container {
        background: var(--green-1);
        border-radius: 10px;
        height: 25px;
        overflow: hidden;
        border: 1px solid var(--green-3);
        transition: box-shadow 0.3s ease;
    }

    .bar-container:hover {
        box-shadow: 0 0 15px var(--green-3);
    }

    .bar {
        height: 100%;
        transition: width 1.5s ease-in-out;
        position: relative;
        overflow: hidden;
    }

    .bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        animation: shimmer 2s infinite;
    }

    .categoria-eventos .bar {
        width: 7.96%;
        background: linear-gradient(45deg, var(--green-4), var(--green-7));
    }

    .categoria-campanhas .bar {
        width: 22.35%;
        background: linear-gradient(45deg, var(--green-4), var(--green-7));
    }

    .categoria-donativos .bar {
        width: 0.29%;
        background: linear-gradient(45deg, var(--green-4), var(--green-7));
    }

    .percentage {
        text-align: right;
        font-size: 16px;
        color: var(--green-7);
        margin-top: 8px;
        text-shadow: 0 0 5px var(--green-3);
    }

    .section_campanha_autocarro {
        /* margin-block: 2rem 6rem; */
        /* margin-block-end: 5rem; */

        max-width: 1400px;
        margin-inline: auto;
        display: grid;
        grid-template-rows: auto 1fr;
    }

    .viagem_header {
        filter: drop-shadow(1px 1px 2px #02692d30);
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        flex-wrap: wrap;
        color: var(--dark-green);
        gap: 1rem;
    }

    .viagem_header h2 {
        font-size: var(--step-5);
    }

    .viagem_km {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .road-icon {
        width: clamp(4rem, 10vw, 5rem);
    }

    .road_distance {
        display: flex;
        flex-direction: column;
        align-items: start;
        font-size: var(--step--1);
        font-weight: lighter;
        color: var(--light-green);
    }

    .road_distance span {
        font-size: var(--step-2);
        font-weight: bold;
    }

    .viagem_content {
        margin: auto;
        margin-block: calc(var(--step-5) *1);

    }

    .d-grid {
        display: grid;
        grid-template-columns: 1fr;
        /* gap: 1rem; */
        margin: 0 1rem;
    }

    .mbway {
        font-size: var(--step-2);
        font-weight: bold;
        background: var(--dark-green);
        padding: var(--step-1) var(--step-0);
        border-radius: 8px;
        color: whitesmoke;
        display: inline-block;
        text-align: center;
        letter-spacing: 1px;
    }

    @keyframes shimmer {
        0% {
            transform: translateX(-100%);
        }

        100% {
            transform: translateX(100%);
        }
    }

    @keyframes fadeIn {
        0% {
            opacity: 0;
            transform: translateY(20px);
        }

        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @media (max-width: 600px) {
        .infographic {
            padding: 20px;
        }

        .infographic h1 {
            font-size: 24px;
        }

        .categoria h2 {
            font-size: 18px;
        }

        .bar-container {
            height: 20px;
        }

        .percentage {
            font-size: 14px;
        }
    }

    .section_slogan {
        display: grid;
        align-items: center;
        justify-content: center;
        background: linear-gradient(80deg, var(--green-7), var(--green-6));
        padding: 4rem;
        margin-block: var(--step-5);
    }

    .animate-100-green {
        margin-inline: auto;
        width: 100%;
        background-image: linear-gradient(225deg,
                #fff 0%,
                #c1c2c3 50%,
                #fff 100%);
        background-size: auto auto;
        background-clip: border-box;
        background-size: 200% auto;
        color: #fff;
        background-clip: text;
        text-fill-color: transparent;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: textclip 5s linear infinite;
        display: inline-block;
    }

    .section_artigo {
        display: grid;
        place-items: center;
        padding: 1rem;
        margin-inline: auto;
        max-width: 1400px;
        margin-block: var(--step-5);

    }

    .artigo-photo {
        display: flex;
        max-width: 100%;
        width: min(450px, 90vw);
        transition: transform 0.3s ease, max-height 0.3s ease;
        cursor: pointer;
    }

    .artigo-photo:hover {
        transform: scale(1.05);
    }

    .artigo-photo.expanded {
        max-height: 100vh;
        width: auto;
        max-width: 100%;
        object-fit: contain;
    }

    @media (max-width: 768px) {
        .section_artigo {
            padding: 1rem;
        }

        .artigo-photo {
            width: calc(100% - 2rem);
            margin: 1rem;
        }
    }

    .section_bus-timeline {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0;
        margin-block: calc(var(--step-5) *2);
    }

    .timeline-container {
        width: 100%;
        max-width: 1400px;
        padding: 1rem;
        border-radius: 12px;
        /* box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); */
        backdrop-filter: blur(4px);
        position: relative;
        /* Make timeline-container the positioning context for truck */
    }

    .road {
        position: relative;
        height: 24px;
        border-radius: 12px;
        overflow: hidden;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .progress {
        height: 100%;
        background: linear-gradient(90deg, hsla(149, 94%, 35%, 0.225), hsla(143, 71%, 53%, 0.553));
        transition: width 5s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .truck {
        position: absolute;
        top: calc(1rem + 12px);
        transform: translateY(-50%) scaleX(-1);
        transition: left 5s cubic-bezier(0.4, 0, 0.2, 1), transform .3s ease-out;
        z-index: 10;
    }

    .truck img {
        width: 70px;
        height: 70px;
        display: block;
    }

    @media (max-width: 768px) {
        .timeline-container {
            padding: 1rem;
            margin: 1rem;
        }

        .road {
            height: 20px;
        }

        .truck {
            top: calc(1rem + 10px);
            /* Adjust for smaller road height */
        }

        .truck img {
            width: 60px;
            height: 60px;
        }
    }

    .truck:hover {
        transform: translateY(-50%) scaleX(-1) scale(1.2);
    }

    .progress-text {
        padding-bottom: var(--step-3);
        margin-block-start: var(--step-3);
        text-align: center;
        font-size: var(--step-2);
        font-weight: 600;
        color: #1f2937;
        animation: fadeIn 0.5s ease-in;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>


<section class="section_campanha_autocarro">
    <header class="viagem_header">
        <h2>Junta-te à Viagem</h2>
        <div class="viagem_km">
            <img class="road-icon" src="https://cercifaf.org.pt/www/images/campanha_bus/road.svg" alt="Road icon">
            <div class="road_distance">Distância<span>170 000 km</span></div>
        </div>
    </header>
</section>

<section class="section_categoria-grafico">
    <div class="infographic">
        <h1>Km por Categoria</h1>
        <div class="categoria categoria-eventos" role="region" aria-label="categoria 1 with 7.96% value">
            <h2>Eventos</h2>
            <div class="bar-container">
                <div class="bar"></div>
            </div>
            <div class="percentage">7.96%</div>
        </div>
        <div class="categoria categoria-campanhas" role="region" aria-label="categoria 2 with 22.35% value">
            <h2>Campanhas</h2>
            <div class="bar-container">
                <div class="bar"></div>
            </div>
            <div class="percentage">22.35%</div>
        </div>
        <div class="categoria categoria-donativos" role="region" aria-label="categoria 3 with 0.29% value">
            <h2>Donativos</h2>
            <div class="bar-container">
                <div class="bar"></div>
            </div>
            <div class="percentage">0.29%</div>
        </div>
    </div>
</section>

<section class="section_bus-timeline">
    <div class="timeline-container">
        <div class="road">
            <div class="progress" style="width: 30%;"></div>
        </div>
        <div class="truck">
            <img src="https://cercifaf.org.pt/www/images/campanha_bus/bus_white.svg" alt="Camião">
        </div>
    </div>
</section>

<section class="section_slogan">
    <p class="animate-100-green">
        <span style="font-size: var(--step-5); font-weight:600;">Contribui de coração para a nossa missão...</span>
    </p>
</section>

<section class="section_campanha_autocarro">

    <div class="viagem_content">
        <div class="d-grid">
            <p class="mbway">(SPIN) 500 860 602</p>
            <p class="mbway">(MB WAY) 966 506 920</p>
            <p class="mbway">PT50.0036.0394.9910335873.33</p>
            <p class="mbway">Empresário Solidário <small>(Lei do Mecenato - majoração em 30%)</small></p>
        </div>
    </div>
</section>

<div class="viagem_header" style="font-size: var(--step-5);        margin-block: calc(var(--step-5) *1);
">Obrigado por viajar connosco!</div>

<section class="section_artigo">
    <div>
        <h2><span style="font-size:16px; letter-spacing:0.01rem;">A CERCIFAF, instituição de apoio a pessoas com
                deficiência
                em Fafe, lançou uma </span><strong style="font-size: 16px; letter-spacing: 0.01rem;">campanha de
                angariação de
                fundos para comprar um novo autocarro </strong><span style="font-size:16px; letter-spacing:0.01rem;">,
                essencial
                para garantir a mobilidade dos utentes, uma vez que o atual está obsoleto e com problemas
                frequentes.</span>
        </h2>
        <p>Paralelamente, com apoio parcial do PRR (Plano de Recuperação e Resiliência), a instituição irá adquirir uma
            carrinha elétrica, promovendo uma mobilidade mais sustentável.</p>
        <p>O presidente Luís Roque, há dois anos à frente da instituição, nestas funções, e um dos seus fundadores,
            destaca
            a
            importância deste projeto no contexto de uma estratégia mais ampla, que inclui:</p>
        <ul>
            <li>A renovação da sede da CERCIFAF (investimento de 650 mil euros),</li>
            <li>O reforço da resposta social do CACI (550 mil euros),</li>
            <li>E a prioridade futura: construção de uma nova residência para utentes sem apoio familiar, face ao fim
                dos
                apoios
                a fundo perdido.</li>
        </ul>
        <p>A CERCIFAF apela à solidariedade da comunidade, mas sem recorrer à caridade tradicional. A campanha já está
            em
            curso e permite ajudar de várias formas:</p>
        <ul>
            <li><a rel="" href="index.php?option=com_sppagebuilder&amp;view=page&amp;id=15&amp;Itemid=148">Consignação
                    do
                    IRS</a> (sem custo para o contribuinte),</li>
            <li><a rel="" href="index.php?option=com_sppagebuilder&amp;view=page&amp;id=18&amp;Itemid=149">Donativos de
                    empresas</a> (com benefícios fiscais),</li>
            <li>Participação em iniciativas solidárias, como eventos organizados localmente <em>(ex: 8.ª Marcha Noturna
                    -
                    Pirilampo Mágico, marcha dos Bombos de Estorãos)</em>...</li>
        </ul>
        <p>A aquisição do autocarro será um presente simbólico pelos <strong>47 anos da instituição</strong>, que
            continua a
            afirmar-se como uma referência na inclusão social.</p>
    </div>

</section>

<section class="section_artigo">
    <img class="artigo-photo" src="https://cercifaf.org.pt/www/images/campanha_bus/artigo.jpg" alt="">
</section>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        const progress = document.querySelector('.progress');
        const truck = document.querySelector('.truck');
        const progressPercentage = 30; // Set progress percentage

        // Set initial state
        progress.style.width = '0%';
        truck.style.left = '1rem'; // Align with road's left edge (padding)

        // Animate progress and truck position
        setTimeout(() => {
            progress.style.width = `${progressPercentage}%`;
            // Calculate truck's position: container padding (1rem) + progress width - half truck width
            truck.style.left = `calc(1rem + ${progressPercentage}% - 35px)`;
        }, 800);
    });

    document.querySelectorAll('.artigo-photo').forEach(photo => {
        photo.addEventListener('click', () => {
            photo.classList.toggle('expanded');
        });
    });
</script>