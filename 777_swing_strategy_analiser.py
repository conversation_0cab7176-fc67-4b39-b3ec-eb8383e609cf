import pandas as pd
import numpy as np
import talib
from dotenv import load_dotenv
import ccxt
from decimal import Decimal, ROUND_HALF_UP
import os
import json
import time
import shutil
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass


load_dotenv()
FIAT_CURRENCIES = ["EUR", "USD"]


@dataclass
class BotConfig:
    SANDBOX_MODE: bool = False
    PREFIX: str = "demo" if SANDBOX_MODE else "live"
    TRADING_SYMBOLS: List[str] = None
    SYMBOLS = ["BTC/USDC", "ETH/USDC", "SOL/USDC"]
    TIMEFRAME = "1h"

    MAKER_FEE: float = 0.0008
    TAKER_FEE: float = 0.001
    STABLECOINS: List[str] = None
    FIAT_DECIMAL_PLACES: int = 6
    CRYPTO_DECIMAL_PLACES: int = 8

    RSI_PERIOD: int = 22
    wma_length: int = 84
    OHLCV_LIMIT: int = 300

    swing_bars = 3
    swing_bars2 = 3
    cluster_bars = 20
    num_clusters = 3

    def __post_init__(self):
        """Initialize default values for trading symbols and stablecoins."""
        if self.TRADING_SYMBOLS is None:
            self.TRADING_SYMBOLS = BotConfig.SYMBOLS
        if self.STABLECOINS is None:
            self.STABLECOINS = ["USDC", "USDC", "BUSD", "FDUSD"]

    @classmethod
    def load_config(cls, file_path: str = "config.json") -> "BotConfig":
        """Load configuration from a JSON file."""
        try:
            with open(file_path, "r") as f:
                config_data = json.load(f)
                return cls(**config_data)
        except Exception as e:
            cls.logger.error("Falha ao carregar configuração: %s", e)
            return cls()


class OKXClient:
    """Cliente OKX otimizado com padrão Singleton para conexão com a exchange."""

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if getattr(self, "_initialized", False):
            return
        self._initialized = True
        self.logger = TradingLogger.get_logger(__name__)
        self.config = BotConfig()
        self._initialize_exchange()

    def _initialize_exchange(self) -> None:
        """Initialize the OKX exchange connection."""
        try:
            credentials = self._get_credentials(self.config.PREFIX)
            self.exchange = ccxt.myokx(
                {
                    "apiKey": credentials["api_key"],
                    "secret": credentials["secret"],
                    "password": credentials["password"],
                    "enableRateLimit": True,
                    "options": {"defaultType": "spot"},
                }
            )
            self.exchange.set_sandbox_mode(self.config.SANDBOX_MODE)
            self._log_initialization_status()
        except Exception as exc:
            self.logger.error("Erro ao inicializar a exchange: %s" % exc)
            raise ConfigurationError(f"Falha ao inicializar a exchange: {exc}") from exc

    def _get_credentials(self, prefix: str) -> Dict[str, str]:
        """Retrieve API credentials from environment variables."""
        credentials = {
            "api_key": os.getenv(f"{prefix}_okx_apiKey"),
            "secret": os.getenv(f"{prefix}_okx_secret"),
            "password": os.getenv(f"{prefix}_okx_password"),
        }
        if not all(credentials.values()):
            self.logger.error("Credenciais incompletas para o prefixo '%s'" % prefix)
            raise ConfigurationError(
                f"Credenciais incompletas para o prefixo '{prefix}'."
            )
        return credentials

    def _log_initialization_status(self) -> None:
        mode = "SANDBOX" if self.config.SANDBOX_MODE else "LIVE"
        self.logger.debug("Exchange initialized in %s mode" % mode)

    def _print_mode_message(self) -> None:
        """Print the trading mode message to the console."""
        try:
            columns = shutil.get_terminal_size().columns
            msg = (
                "🔴 DEMO TRADING 🔴"
                if self.config.SANDBOX_MODE
                else "🟢 LIVE TRADING 🟢"
            )
            color = "\033[1;37;43m" if self.config.SANDBOX_MODE else "\033[1;37;47m"
            reset = "\033[0;0m"
            self.logger.info(f"{color}{msg.center(columns)}{reset}")
        except Exception as exc:
            self.logger.warning("Falha ao imprimir mensagem de modo: %s" % str(exc))

    def format_price_with_precision(self, symbol: str, price: float) -> str:
        """Format price with exchange precision."""
        if price <= 0:
            raise ValueError("Price must be positive")
        try:
            decimal_price = Decimal(str(price))
            formatted_price = self.exchange.price_to_precision(
                symbol, float(decimal_price)
            )
            return str(formatted_price)
        except (ValueError, TypeError) as exc:
            self.logger.warning(
                "Falha ao formatar preço para %s: %s" % (symbol, str(exc))
            )
            return self._fallback_price_format(price)

    def format_amount_with_precision(self, symbol: str, amount: float) -> str:
        """Format amount with exchange precision."""
        if amount <= 0:
            raise ValueError("Amount must be positive")
        try:
            decimal_amount = Decimal(str(amount))
            formatted_amount = self.exchange.amount_to_precision(
                symbol, float(decimal_amount)
            )
            return str(formatted_amount)
        except (ValueError, TypeError) as exc:
            self.logger.warning(
                "Falha ao formatar quantidade para %s: %s" % (symbol, str(exc))
            )
            return self._fallback_amount_format(amount)

    def _fallback_price_format(self, price: float) -> str:
        """Format price using fallback method if precision fails."""
        decimal_price = Decimal(str(price))
        formatted = f"{decimal_price:.{self.config.CRYPTO_DECIMAL_PLACES}f}"
        return formatted.rstrip("0").rstrip(".")

    def _fallback_amount_format(self, amount: float) -> str:
        """Format amount using fallback method if precision fails."""
        decimal_amount = Decimal(str(amount))
        formatted = f"{decimal_amount:.{self.config.CRYPTO_DECIMAL_PLACES}f}"
        return formatted.rstrip("0").rstrip(".")

    def format_fiat_amount(self, amount: float, decimals: int = None) -> str:
        """Format fiat amount with specified decimal places."""
        try:
            decimals = decimals or self.config.FIAT_DECIMAL_PLACES
            decimal_amount = Decimal(str(amount))
            rounded = decimal_amount.quantize(
                Decimal("0." + "0" * decimals), rounding=ROUND_HALF_UP
            )
            formatted = f"{rounded:.{decimals}f}".rstrip("0").rstrip(".")
            return formatted if formatted else "0"
        except Exception as exc:
            self.logger.warning("Falha ao formatar quantidade fiat: %s" % str(exc))
            return f"{amount:.{decimals if decimals else 2}f}"

    def get_balance(self) -> Optional[Dict]:
        """Fetch account balance from the exchange."""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                balance = self.exchange.fetch_balance()
                self.logger.debug("Saldo obtido com sucesso")
                return balance
            except Exception as exc:
                self.logger.error(
                    "Erro ao obter saldo (tentativa %d/%d): %s"
                    % (attempt + 1, max_retries, str(exc))
                )
                if attempt == max_retries - 1:
                    raise ExchangeConnectionError(
                        f"Falha ao obter saldo após {max_retries} tentativas: {exc}"
                    ) from exc
                time.sleep(2)  # Aguarda antes de tentar novamente
        return None

    def get_ticker(self, symbol: str) -> Optional[Dict]:
        """Fetch ticker data for a given symbol."""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                ticker = self.exchange.fetch_ticker(symbol)
                self.logger.debug("Ticker obtido para %s", symbol)
                return ticker
            except Exception as exc:
                self.logger.error(
                    "Erro ao obter ticker para %s (tentativa %d/%d): %s"
                    % (symbol, attempt + 1, max_retries, str(exc))
                )
                if attempt == max_retries - 1:
                    raise ConfigurationError(
                        f"Falha ao obter ticker para {symbol} após {max_retries} tentativas"
                    ) from exc
                time.sleep(2)
        return None

    def get_best_bid(self, symbol: str) -> Optional[float]:
        """Fetch the best bid price for a symbol."""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                ticker = self.get_ticker(symbol)
                if ticker and "bid" in ticker and ticker["bid"]:
                    return float(ticker["bid"])
                orderbook = self.exchange.fetch_order_book(symbol, limit=1)
                if orderbook and orderbook["bids"] and len(orderbook["bids"]) > 0:
                    return float(orderbook["bids"][0][0])
                return None
            except Exception as exc:
                self.logger.error(
                    "Erro ao obter melhor bid para %s (tentativa %d/%d): %s",
                    symbol,
                    attempt + 1,
                    max_retries,
                    str(exc),
                )
                if attempt == max_retries - 1:
                    self.logger.error(
                        "Falha ao obter melhor ask para %s após %d tentativas"
                        % (symbol, max_retries)
                    )
                    return None
                time.sleep(2)
        return None

    def get_best_ask(self, symbol: str) -> Optional[float]:
        """Fetch the best ask price for a symbol."""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                ticker = self.get_ticker(symbol)
                if ticker and "ask" in ticker and ticker["ask"]:
                    return float(ticker["ask"])
                orderbook = self.exchange.fetch_order_book(symbol, limit=1)
                if orderbook and orderbook["asks"] and len(orderbook["asks"]) > 0:
                    return float(orderbook["asks"][0][0])
                return None
            except Exception as exc:
                self.logger.error(
                    "Erro ao obter melhor ask para %s (tentativa %d/%d): %s"
                    % (symbol, attempt + 1, max_retries, str(exc))
                )
                if attempt == max_retries - 1:
                    self.logger.error(
                        "Falha ao obter melhor ask para %s após %d tentativas"
                        % (symbol, max_retries)
                    )
                    return None
                time.sleep(2)
        return None

    def display_balance_with_precision(self) -> None:
        """Display account balance with formatted precision."""
        try:
            balance = self.get_balance()
            if not self._validate_balance(balance):
                return
            self._print_balance_header()
            self._process_balance_assets(balance["total"])
            ten_percent_balance = self.calculate_ten_percent_balance(balance["total"])
            if ten_percent_balance:
                print("\n📊 10% do Saldo Disponível:")
                print("=" * 50)
                for asset, amount in ten_percent_balance.items():
                    if (
                        amount > 0.1
                    ):  # <-- Filtro atualizado para mostrar apenas saldos > 0.1
                        formatter = self._get_asset_formatter(asset)
                        formatted_amount, emoji = formatter(asset, amount)
                        self.logger.info(f" {emoji} {asset}: {formatted_amount}")
        except Exception as exc:
            self.logger.error("Erro ao exibir saldo: %s" % str(exc))
            self.logger.error("❌ Erro ao obter saldo: %s" % exc)

    def _validate_balance(self, balance: Optional[Dict]) -> bool:
        """Validate if balance data is available."""
        if not balance or not balance.get("total"):
            print("❌ Nenhuma informação de saldo disponível")
            return False
        return True

    def _print_balance_header(self) -> None:
        """Print header for balance display."""
        print("💰 Saldo Disponível:")
        print("=" * 50)

    def _process_balance_assets(self, total_balance: Dict[str, float]) -> None:
        """Process and display each asset in the balance."""
        for asset, amount in total_balance.items():
            if amount > 0.1:
                self._display_single_asset(asset, amount)

    def _display_single_asset(self, asset: str, amount: float) -> None:
        """Display a single asset's balance with appropriate formatting."""
        try:
            formatter = self._get_asset_formatter(asset)
            formatted_amount, emoji = formatter(asset, amount)
            self.logger.info(f" {emoji} {asset}: {formatted_amount}")
        except Exception as exc:
            self.logger.warning("Falha ao formatar %s: %s" % (asset, str(exc)))
            self._display_fallback_asset(asset, amount)

    def _get_asset_formatter(self, asset: str) -> callable:
        """Get the appropriate formatter function for an asset type."""
        if asset in BotConfig.SYMBOLS:
            return self._format_crypto_asset
        elif asset in self.config.STABLECOINS:
            return self._format_stablecoin_asset
        elif asset in FIAT_CURRENCIES:
            return self._format_fiat_asset
        else:
            return self._format_other_asset

    def _format_crypto_asset(self, asset: str, amount: float) -> Tuple[str, str]:
        """Format cryptocurrency asset amount."""
        symbol = f"{asset}/USDC"
        formatted_amount = self.format_amount_with_precision(symbol, amount)
        return formatted_amount, "🪙"

    def _format_stablecoin_asset(self, asset: str, amount: float) -> Tuple[str, str]:
        """Format stablecoin asset amount."""
        formatted_amount = f"{float(amount):.2f}"
        return formatted_amount, "💵"

    def _format_fiat_asset(self, asset: str, amount: float) -> Tuple[str, str]:
        """Format fiat currency asset amount."""
        formatted_amount = f"{float(amount):.2f}"
        return formatted_amount, "💴"

    def _format_other_asset(self, asset: str, amount: float) -> Tuple[str, str]:
        """Format other asset types not covered by specific categories."""
        try:
            symbol = f"{asset}/USDC"
            formatted_amount = self.format_amount_with_precision(symbol, amount)
            return formatted_amount, "🔹"
        except Exception:
            formatted_amount = self._fallback_amount_format(amount)
            return formatted_amount, "⚪"

    def _display_fallback_asset(self, asset: str, amount: float) -> None:
        """Display asset balance using fallback formatting if standard fails."""
        if asset in FIAT_CURRENCIES or asset in self.config.STABLECOINS:
            formatted_fallback = f"{float(amount):.2f}"
            emoji = "💴" if asset in FIAT_CURRENCIES else "💵"
        else:
            formatted_fallback = f"{amount:.8f}".rstrip("0").rstrip(".")
            emoji = "⚠️"
        self.logger.warning(f" {emoji} {asset}: {formatted_fallback}")

    def display_prices_with_precision(self) -> None:
        """Display current market prices for trading symbols."""
        print("\n📈 Preços Atuais do Mercado:")
        print("=" * 50)
        try:
            tickers = self.exchange.fetch_tickers(self.config.TRADING_SYMBOLS)
            self.logger.debug("Tickers obtidos para %s", self.config.TRADING_SYMBOLS)
            for symbol in self.config.TRADING_SYMBOLS:
                ticker = tickers.get(symbol)
                if not ticker or not ticker.get("last"):
                    self.logger.warning(f" ❌ {symbol}: Dados de ticker indisponíveis")
                    continue
                self._print_ticker_info(symbol, ticker)
        except Exception as exc:
            self.logger.error("Erro ao obter tickers: %s", str(exc))
            print(f" ❌ Erro ao obter preços: {exc}")
            for symbol in self.config.TRADING_SYMBOLS:
                self._display_single_price(symbol)

    def _display_single_price(self, symbol: str) -> None:
        """Display price information for a single symbol."""
        try:
            ticker = self.get_ticker(symbol)
            if not ticker or not ticker.get("last"):
                self.logger.warning(f" ❌ {symbol}: No ticker data available")
                return
            self._print_ticker_info(symbol, ticker)
        except Exception as exc:
            self.logger.error("Erro ao obter ticker para %s: %s", symbol, str(exc))
            self.logger.error(f" ❌ {symbol}: Erro ao obter preço - {exc}")

    def _print_ticker_info(self, symbol: str, ticker: Dict) -> None:
        """Print detailed ticker information for a symbol."""
        formatted_price = self.format_price_with_precision(symbol, ticker["last"])
        volume = ticker.get("baseVolume", 0)
        change_percent = ticker.get("percentage", 0)
        change_emoji = "📈" if change_percent >= 0 else "📉"
        self.logger.info(f" {change_emoji} {symbol}")
        self.logger.info(f"   Price: ${formatted_price}")
        if volume > 0:
            formatted_volume = self.format_amount_with_precision(symbol, volume)
            self.logger.info(f"   Volume: {formatted_volume}")
        if change_percent != 0:
            self.logger.info(f"   Change Ticket: {change_percent:+.2f}%")
            print()

    def calculate_ten_percent_balance(
        self, balance: Dict[str, float]
    ) -> Dict[str, float]:
        """Calculate 10% of the balance for each asset."""
        ten_percent_balance = {}
        for asset, amount in balance.items():
            if amount > 0:
                ten_percent_balance[asset] = amount * 0.1
        return ten_percent_balance


def calculate_technical_indicators(df, rsi_period=30, adx_period=14, sWMA_PERIOD=34):
    """
    Calculate various technical indicators for the given DataFrame.
    Parameters:
        df (pd.DataFrame): DataFrame with columns ['open', 'high', 'low', 'close']
        rsi_period (int): Period for RSI calculation
        adx_period (int): Period for ADX calculation
        sWMA_PERIOD (int): Period for SMA calculation
    Returns:
        pd.DataFrame: DataFrame with added indicator columns
    """
    result = df.copy()

    # RSI
    result["rsi"] = talib.RSI(df["close"], timeperiod=rsi_period)

    # ADX
    result["adx"] = talib.ADX(df["high"], df["low"], df["close"], timeperiod=adx_period)

    # SMA
    result["sma"] = talib.SMA(df["close"], timeperiod=sWMA_PERIOD)

    # WMA (Weighted Moving Average)
    result["wma"] = talib.WMA(df["close"], timeperiod=BotConfig.wma_length)

    return result


def swing_base_ai(df):
    """
    Calculate Swing Base - AI indicator with additional technical indicators.
    Parameters:
        df (pd.DataFrame): DataFrame with columns ['open', 'high', 'low', 'close']
    Returns:
        pd.DataFrame: DataFrame with added indicator columns
    """
    # Ensure required columns exist
    if not all(col in df.columns for col in ["open", "high", "low", "close"]):
        raise ValueError(
            "DataFrame must contain 'open', 'high', 'low', 'close' columns"
        )

    # Limit to the last 100 candles for performance
    df = df.tail(100)

    # Initialize output DataFrame
    result = df.copy()

    # Calculate additional technical indicators
    result = calculate_technical_indicators(result)

    # Swing Highs and Lows
    result["res"] = (
        df["high"].rolling(window=BotConfig.swing_bars).max()
    )  # Highest high
    result["sup"] = df["low"].rolling(window=BotConfig.swing_bars).min()  # Lowest low

    # Swing direction logic
    result["avd"] = np.where(
        df["close"] > result["res"].shift(BotConfig.swing_bars2),
        1,
        np.where(df["close"] < result["sup"].shift(BotConfig.swing_bars2), -1, 0),
    )

    # Get the most recent non-zero avd value (mimicking ta.valuewhen)
    result["avn"] = result["avd"].replace(0, np.nan).ffill()
    result["tsl"] = np.where(result["avn"] == 1, result["sup"], result["res"])

    # Simplified K-means Clustering Approximation
    result["price_high"] = df["high"].rolling(window=BotConfig.cluster_bars).max()
    result["price_low"] = df["low"].rolling(window=BotConfig.cluster_bars).min()
    result["price_range"] = result["price_high"] - result["price_low"]
    result["cluster_step"] = result["price_range"] / BotConfig.num_clusters

    # Assign current price to a cluster
    result["current_cluster"] = np.floor(
        (df["close"] - result["price_low"]) / result["cluster_step"]
    )
    result["current_cluster"] = result["current_cluster"].clip(
        upper=BotConfig.num_clusters - 1
    )

    # Volatility and cluster adjustment
    result["volatility"] = result["price_range"] / BotConfig.cluster_bars
    result["cluster_adjust"] = result["cluster_step"] * (
        0.3 + 0.7 * (result["volatility"] / (result["price_range"] + 0.0001))
    )
    result["cluster_weight"] = 1.0 - np.abs(
        result["current_cluster"] - (BotConfig.num_clusters / 2.0)
    ) / (BotConfig.num_clusters / 2.0)

    # Adjusted trailing stop level (tsl_adjusted)
    result["tsl_adjusted"] = np.where(
        result["avn"] == 1,
        result["sup"]
        + result["cluster_adjust"]
        * result["cluster_weight"]
        * (result["current_cluster"] / BotConfig.num_clusters),
        result["res"]
        - result["cluster_adjust"]
        * result["cluster_weight"]
        * (result["current_cluster"] / BotConfig.num_clusters),
    )

    # Swing signals with additional filters using RSI
    result["swing_longX"] = (
        (df["close"] > result["tsl_adjusted"])
        & (df["close"].shift(1) <= result["tsl_adjusted"].shift(1))
        & (df["close"] > result["wma"])
        & (result["rsi"] > 50)
    )
    result["swing_shortX"] = (
        (df["close"] < result["tsl_adjusted"])
        & (df["close"].shift(1) >= result["tsl_adjusted"].shift(1))
        & (df["close"] < result["wma"])
        & (result["rsi"] < 50)
    )
    result["swing_long"] = df["close"] > result["tsl_adjusted"]
    result["swing_short"] = df["close"] < result["tsl_adjusted"]

    return result


# Backtesting functionality
def backtest_strategy(df, client, symbol=BotConfig.SYMBOLS, transaction_cost=0.001):
    """
    Perform backtesting on the given DataFrame using the swing strategy.
    Parameters:
        df (pd.DataFrame): DataFrame with OHLCV data and calculated indicators
        client (OKXClient): Instance of OKXClient to fetch balance from exchange
        symbol (str): Trading symbol to calculate balance for (default: ETH/BTC)
        transaction_cost (float): Transaction cost per trade (as a fraction)
    Returns:
        dict: Backtest results including final balance and performance metrics
    """
    # Fetch balance from exchange
    balance_data = client.get_balance()
    initial_balance = 0
    if balance_data and balance_data.get("total"):
        total_balance = balance_data["total"]
        # Calculate 10% of total balance for the base currency of the symbol
        base_currency = symbol.split("/")[0]  # e.g., ETH from ETH/BTC
        quote_currency = (
            symbol.split("/")[1] if "/" in symbol else "USDC"
        )  # e.g., BTC from ETH/BTC
        if base_currency in total_balance and total_balance[base_currency] > 0:
            ticker = client.get_ticker(symbol)
            if ticker and ticker.get("last"):
                # Convert base currency to quote currency using current market price
                base_to_quote = total_balance[base_currency] * ticker["last"]
                initial_balance = (
                    base_to_quote * 0.1 / ticker["last"]
                )  # 10% in terms of base currency
            else:
                client.logger.warning(
                    "Não foi possível obter preço atual para conversão, usando valor padrão de 10000"
                )
                initial_balance = 10000
        elif quote_currency in total_balance and total_balance[quote_currency] > 0:
            initial_balance = total_balance[quote_currency] * 0.1
        elif "USDC" in total_balance and total_balance["USDC"] > 0:
            # Fallback to USDC if base or quote currency not available
            ticker = client.get_ticker(symbol)
            if ticker and ticker.get("last"):
                usdc_balance = total_balance["USDC"] * 0.1
                initial_balance = usdc_balance / ticker["last"]
            else:
                client.logger.warning(
                    "Não foi possível obter preço atual para conversão de USDC, usando valor padrão de 10000"
                )
                initial_balance = 10000
        else:
            client.logger.warning(
                "Saldo insuficiente para backtest, usando valor padrão de 10000"
            )
            initial_balance = 10000
    else:
        client.logger.warning(
            "Não foi possível obter saldo da exchange, usando valor padrão de 10000"
        )
        initial_balance = 10000

    balance = initial_balance
    position = 0
    entry_price = 0
    trades = []
    equity_curve = [initial_balance]  # Start with initial balance

    result = swing_base_ai(df)

    for i in range(1, len(result)):
        if result["swing_longX"].iloc[i] and position == 0:
            # Enter long position
            entry_price = result["close"].iloc[i]
            position = balance / entry_price * (1 - transaction_cost)
            balance = 0
            trades.append(("buy", result.index[i], entry_price, position))
        elif result["swing_shortX"].iloc[i] and position > 0:
            # Exit long position
            exit_price = result["close"].iloc[i]
            balance = position * exit_price * (1 - transaction_cost)
            position = 0
            trades.append(("sell", result.index[i], exit_price, balance))
        equity_curve.append(
            balance + position * result["close"].iloc[i] if position > 0 else balance
        )

    final_balance = (
        balance + position * result["close"].iloc[-1] if position > 0 else balance
    )
    returns = 0.0
    if initial_balance > 0:  # Avoid division by zero
        returns = (final_balance - initial_balance) / initial_balance * 100
    sharpe_ratio = 0.0
    if np.std(equity_curve) > 0:  # Avoid division by zero
        sharpe_ratio = np.mean(equity_curve) / np.std(equity_curve) * np.sqrt(252)

    return {
        "final_balance": final_balance,
        "returns_percent": returns,
        "sharpe_ratio": sharpe_ratio,
        "trades": trades,
        "equity_curve": equity_curve,
    }


# Example usage
if __name__ == "__main__":
    # Importar o TelegramNotifier para enviar notificações
    from service.service_telegram import TelegramNotifier
    import asyncio

    # Import logger if available, otherwise create a simple one
    try:
        from utils.logger import TradingLogger
    except ImportError:
        import logging

        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        )

        class TradingLogger:
            @staticmethod
            def get_logger(name):
                return logging.getLogger(name)

    # Define custom exceptions if not available elsewhere
    class ConfigurationError(Exception):
        pass

    class ExchangeConnectionError(Exception):
        pass

    # Initialize OKX client to fetch real data
    client = OKXClient()
    timeframe = client.config.TIMEFRAME
    limit = client.config.OHLCV_LIMIT

    # Display account balance
    client.display_balance_with_precision()

    # Initialize Telegram notifier
    notifier = TelegramNotifier()

    # Loop through all symbols defined in SYMBOLS list
    for symbol in client.config.SYMBOLS:
        try:
            print(f"\n=== Análise e Backtest para {symbol} ===\n")
            # Fetch OHLCV data from the exchange
            ohlcv = client.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            data = pd.DataFrame(
                ohlcv, columns=["timestamp", "open", "high", "low", "close", "volume"]
            )
            data["timestamp"] = pd.to_datetime(data["timestamp"], unit="ms")
            data.set_index("timestamp", inplace=True)

            # Apply the indicator
            result = swing_base_ai(data)

            # Log signals and send Telegram notifications
            latest_candle = result.iloc[-1]
            if latest_candle["swing_longX"]:
                client.logger.info(
                    f"Sinal de COMPRA detectado para {symbol} às {latest_candle.name} - Preço: {latest_candle['close']}"
                )
                message = f"🟢 *Sinal de COMPRA* para {symbol}\n📅 Data: {latest_candle.name}\n💰 Preço: {latest_candle['close']}"
                asyncio.run(notifier.send_message(message))
                client.logger.info(
                    f"Notificação Telegram enviada para sinal de COMPRA em {symbol}"
                )
            elif latest_candle["swing_shortX"]:
                client.logger.info(
                    f"Sinal de VENDA detectado para {symbol} às {latest_candle.name} - Preço: {latest_candle['close']}"
                )
                message = f"🔴 *Sinal de VENDA* para {symbol}\n📅 Data: {latest_candle.name}\n💰 Preço: {latest_candle['close']}"
                asyncio.run(notifier.send_message(message))
                client.logger.info(
                    f"Notificação Telegram enviada para sinal de VENDA em {symbol}"
                )

            # Perform backtesting
            backtest_results = backtest_strategy(data, client, symbol)
            base_currency = symbol.split("/")[0]  # e.g., BTC from BTC/USDC
            # Obter preço atual em USD para conversão (usando USDC como proxy)
            ticker_usdc = (
                client.get_ticker(f"{base_currency}/USDC")
                if base_currency != "USDC"
                else None
            )
            initial_balance = backtest_results["equity_curve"][0]
            final_balance = backtest_results["final_balance"]
            initial_usd = (
                initial_balance * ticker_usdc["last"]
                if ticker_usdc and ticker_usdc.get("last")
                else 0
            )
            final_usd = (
                final_balance * ticker_usdc["last"]
                if ticker_usdc and ticker_usdc.get("last")
                else 0
            )

            client.logger.info(f"Resultados do Backtest para {symbol}:")
            if initial_usd > 0:
                client.logger.info(
                    f"Saldo Inicial (10% do disponível): {initial_balance:.4f} {base_currency} | ${initial_usd:.2f}"
                )
                client.logger.info(
                    f"Saldo Final: {final_balance:.4f} {base_currency} | ${final_usd:.2f}"
                )
                pnl_base = final_balance - initial_balance
                pnl_usd = final_usd - initial_usd
                client.logger.info(
                    f"PnL: {pnl_base:+.4f} {base_currency} | ${pnl_usd:+.2f}"
                )
            else:
                client.logger.info(
                    f"Saldo Inicial (10% do disponível): {initial_balance:.4f} {base_currency}"
                )
                client.logger.info(f"Saldo Final: {final_balance:.4f} {base_currency}")
                pnl_base = final_balance - initial_balance
                client.logger.info(f"PnL: {pnl_base:+.4f} {base_currency}")
            client.logger.info(f"Retorno: {backtest_results['returns_percent']:.2f}%")
            client.logger.info(f"Sharpe Ratio: {backtest_results['sharpe_ratio']:.2f}")

            print(f"\nResultados do Backtest para {symbol}:")
            if initial_usd > 0:
                print(
                    f"Saldo Inicial (10% do disponível): {initial_balance:.4f} {base_currency} | ${initial_usd:.2f}"
                )
                print(
                    f"Saldo Final: {final_balance:.4f} {base_currency} | ${final_usd:.2f}"
                )
                pnl_base = final_balance - initial_balance
                pnl_usd = final_usd - initial_usd
                print(f"PnL: {pnl_base:+.4f} {base_currency} | ${pnl_usd:+.2f}")
            else:
                print(
                    f"Saldo Inicial (10% do disponível): {initial_balance:.4f} {base_currency}"
                )
                print(f"Saldo Final: {final_balance:.4f} {base_currency}")
                pnl_base = final_balance - initial_balance
                print(f"PnL: {pnl_base:+.4f} {base_currency}")
            print(f"Retorno: {backtest_results['returns_percent']:.2f}%")
            print(f"Sharpe Ratio: {backtest_results['sharpe_ratio']:.2f}")

            # Optional: Plotting (requires matplotlib and mplfinance for candlestick chart)
            import matplotlib.pyplot as plt
            import mplfinance as mpf

            # Prepare data for candlestick chart
            plot_data = result.copy()
            plot_data.reset_index(inplace=True)
            plot_data["Date"] = plot_data["timestamp"]
            plot_data.set_index("Date", inplace=True)

            # Define additional plots for WMA and TSL Adjusted
            add_plots = [
                mpf.make_addplot(plot_data["wma"], color="silver", label="WMA"),
                mpf.make_addplot(
                    plot_data["tsl_adjusted"],
                    color="orange",
                    linestyle="--",
                    label="TSL Adjusted",
                ),
            ]

            # Plot candlestick chart with additional indicators
            mpf.plot(
                plot_data,
                type="candle",
                style="charles",
                title=f"Swing Base - AI Indicator for {symbol}",
                ylabel="Price",
                addplot=add_plots,
                figsize=(12, 6),
            )

            # Plot signals on a separate figure (since mplfinance doesn't support scatter directly)
            plt.figure(figsize=(12, 6))
            plt.plot(
                plot_data.index,
                plot_data["close"],
                label="Close",
                color="blue",
                alpha=0.5,
            )
            plt.scatter(
                plot_data.index[plot_data["swing_longX"]],
                plot_data["close"][plot_data["swing_longX"]],
                marker="^",
                color="green",
                label="Long Signal",
            )
            plt.scatter(
                plot_data.index[plot_data["swing_shortX"]],
                plot_data["close"][plot_data["swing_shortX"]],
                marker="v",
                color="red",
                label="Short Signal",
            )
            plt.legend()
            plt.title(f"Signals for {symbol}")
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.show()

            # Plot equity curve from backtest
            plt.figure(figsize=(12, 6))
            # Ensure equity_curve has the same length as result.index
            equity_curve = backtest_results["equity_curve"]
            if len(equity_curve) == len(result.index):
                plt.plot(
                    result.index, equity_curve, label="Equity Curve", color="purple"
                )
            else:
                client.logger.warning(
                    f"Tamanho da curva de equity ({len(equity_curve)}) não corresponde ao índice ({len(result.index)}). Ajustando para plotagem."
                )
                # Adjust equity_curve length to match result.index
                if len(equity_curve) < len(result.index):
                    equity_curve.extend(
                        [equity_curve[-1]] * (len(result.index) - len(equity_curve))
                    )
                elif len(equity_curve) > len(result.index):
                    equity_curve = equity_curve[: len(result.index)]
                plt.plot(
                    result.index,
                    equity_curve,
                    label="Equity Curve (Adjusted)",
                    color="purple",
                    linestyle="--",
                )
            plt.title(f"Equity Curve for {symbol} Backtest")
            plt.xlabel("Time")
            plt.ylabel("Portfolio Value")
            plt.legend()
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.show()
        except Exception as e:
            client.logger.error(f"Erro ao obter dados da exchange para {symbol}: {e}")
            print(f"Erro ao obter dados da exchange para {symbol}: {e}")
