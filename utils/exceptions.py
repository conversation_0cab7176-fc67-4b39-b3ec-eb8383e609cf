"""
Exceções customizadas para o bot de trading.
"""

class TradingBotError(Exception):
    """Exceção base para o bot de trading."""
    pass

class DataFetchError(TradingBotError):
    """Erro ao obter dados da exchange."""
    pass

class CalculationError(TradingBotError):
    """Erro em cálculos de indicadores."""
    pass

class ConfigurationError(TradingBotError):
    """Erro de configuração."""
    pass

class ExchangeConnectionError(TradingBotError):
    """Erro de conexão com a exchange."""
    pass
