"""
Common utility functions for trading bots to reduce code duplication and improve modularity.
This module centralizes functions related to configuration loading, notifications, and other shared utilities.
"""

import json
import asyncio
from typing import Optional, Dict, Any
from service.service_alerts import SoundNotifications
from service.service_telegram import TelegramNotifier


def load_parameters(file_path: str = "parameters.json") -> Dict[str, Any]:
    """
    Load parameters from a JSON configuration file.
    
    Args:
        file_path: Path to the JSON configuration file.
    
    Returns:
        Dictionary containing the loaded parameters.
    """
    with open(file_path, "r") as f:
        return json.load(f)


async def send_telegram_notification(notifier: Optional[TelegramNotifier], message: str) -> None:
    """
    Send a notification via Telegram if the notifier is available.
    
    Args:
        notifier: Instance of TelegramNotifier for sending messages.
        message: The message to send.
    """
    if notifier:
        try:
            await notifier.send_message(message)
        except Exception as exc:
            from utils.logger import TradingLogger
            logger = TradingLogger.get_logger(__name__)
            logger.warning("Falha ao enviar notificação Telegram: %s", str(exc))


async def play_alert(sound_alert: Optional[SoundNotifications], alert_type: str, volume: float = 0.5) -> None:
    """
    Play a sound alert if the sound notification system is available.
    
    Args:
        sound_alert: Instance of SoundNotifications for playing alerts.
        alert_type: Type of alert sound to play.
        volume: Volume level for the alert sound.
    """
    if sound_alert:
        try:
            sound_alert.play_notification(alert_type, volume=volume)
        except Exception as exc:
            from utils.logger import TradingLogger
            logger = TradingLogger.get_logger(__name__)
            logger.warning("Falha ao reproduzir alerta: %s", str(exc))
