"""
Utility functions for building notification messages for trading bots.
This module centralizes functions related to notification message construction to avoid circular imports.
"""

from typing import Optional, Dict, Any
from core.config import BotConfig


async def build_startup_message(balance: Optional[Dict], config: BotConfig) -> str:
    """
    Build the startup notification message for the bot.
    
    Args:
        balance: Dictionary containing balance information.
        config: Bot configuration object with mode and script name.
    
    Returns:
        Formatted startup message string.
    """
    mode_text = "🟢 LIVE TRADING" if not config.SANDBOX_MODE else "🔴 SANDBOX MODE"
    message = f"""🚀 * {config.SCRIPT_NAME} Iniciado*
-----------------------------
• *Mode*: {mode_text}
• *Balance*:
💰 Available Balance:
"""
    if balance and balance.get("total"):
        for asset, amount in balance["total"].items():
            if amount > 0:
                message += f" 🪙 {asset}: {amount:.8f}\n"
    return message
