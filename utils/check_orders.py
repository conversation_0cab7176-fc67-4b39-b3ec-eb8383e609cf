from typing import List, Dict
import sqlite3

def check_regular_orders(client, symbol: str) -> List[Dict]:
    try:
        # Verificar se client tem atributo exchange, caso contr<PERSON>rio, assumir que client é a exchange
        exchange = getattr(client, 'exchange', client)
        open_orders = exchange.fetch_open_orders(symbol)
        regular_orders = [
            order
            for order in open_orders
            if order.get("type") == "limit" or order.get("type") == "market"
        ]
        client.logger.debug(
            "Ordens regulares verificadas para %s: %d encontradas",
            symbol,
            len(regular_orders),
        )
        return regular_orders
    except Exception as exc:
        client.logger.error("Erro ao verificar ordens para %s: %s", symbol, str(exc))
        raise Exception(f"Falha ao verificar ordens para {symbol}") from exc

def display_regular_orders(client) -> None:
    from tabulate import tabulate
    print("\n📉 Ordens Regulares Abertas:")
    print("=" * 118)
    try:
        found_orders = False
        for symbol in client.config.TRADING_SYMBOLS:
            regular_orders = check_regular_orders(client, symbol)
            if regular_orders:
                found_orders = True
                print(f" 📌 {symbol}:")
                headers = ["Símbolo", "ID", "Tipo", "Lado", "Quantidade", "Preço"]
                table_data = [
                    [
                        symbol,
                        order.get("id", "N/A"),
                        order.get("type", "N/A"),
                        order.get("side", "N/A"),
                        order.get("amount", "N/A"),
                        order.get("price", "N/A"),
                    ]
                    for order in regular_orders[:5]
                ]
                print(tabulate(table_data, headers, tablefmt="fancy_grid"))
                if len(regular_orders) > 5:
                    print(f" ... e mais {len(regular_orders) - 5} ordens.")
        if not found_orders:
            print(" Nenhuma ordem regular aberta encontrada.")
    except Exception as exc:
        client.logger.error(
            "Erro ao exibir ordens regulares: name 'tabulate' is not defined",
            exc_info=exc,
        )
        print(f" ❌ Erro ao verificar ordens regulares: {exc}")

def check_oco_orders(client, symbol: str) -> List[Dict]:
    try:
        # Verificar se client tem atributo exchange, caso contrário, assumir que client é a exchange
        exchange = getattr(client, 'exchange', client)
        oco_orders = exchange.fetch_open_orders(
            symbol, params={"trigger": True, "ordType": "oco"}
        )
        client.logger.debug(
            "Ordens OCO verificadas para %s: %d encontradas",
            symbol,
            len(oco_orders),
        )
        return oco_orders
    except Exception as exc:
        client.logger.error(
            "Erro ao verificar ordens OCO para %s: %s", symbol, str(exc)
        )
        raise Exception(
            f"Falha ao verificar ordens OCO para {symbol}"
        ) from exc

def display_oco_orders(client) -> None:
    from tabulate import tabulate
    print("\n📉 Ordens OCO Abertas:")
    print("=" * 118)
    try:
        found_orders = False
        for symbol in client.config.TRADING_SYMBOLS:
            oco_orders = check_oco_orders(client, symbol)
            if oco_orders:
                found_orders = True
                print(f" 📌 {symbol}:")
                tabular_data = []
                for order in oco_orders[:5]:
                    order_id = order.get("id", "N/A")
                    side = order.get("side", "N/A")
                    amount = order.get("amount", "N/A")
                    price = order.get("price", "N/A")
                    trigger_price = order.get("triggerPrice", "N/A")
                    status = order.get("status", "N/A")
                    stop_loss = (
                        order.get("stopLossPrice")
                        or order.get("stop_loss_price")
                        or order.get("stopPrice")
                        or order.get("stop_price")
                        or (order.get("triggerPrice") if side == "sell" else None)
                        or "N/A"
                    )
                    take_profit = (
                        order.get("takeProfitPrice")
                        or order.get("take_profit_price")
                        or (order.get("triggerPrice") if side == "buy" else None)
                        or "N/A"
                    )
                    tabular_data.append(
                        [
                            symbol,
                            order_id,
                            side,
                            amount,
                            price,
                            trigger_price,
                            stop_loss,
                            take_profit,
                            status,
                        ]
                    )
                print(
                    tabulate(
                        tabular_data,
                        headers=[
                            "Símbolo",
                            "ID",
                            "Lado",
                            "Quantidade",
                            "Preço",
                            "Trigger",
                            "Stop Loss",
                            "Take Profit",
                            "Status",
                        ],
                        tablefmt="fancy_grid",
                    )
                )
                if len(oco_orders) > 5:
                    print(f" ... e mais {len(oco_orders) - 5} ordens OCO.")
        if not found_orders:
            print(" Nenhuma ordem OCO aberta encontrada.")
    except Exception as exc:
        client.logger.error("Erro ao exibir ordens OCO: %s", str(exc))
        print(f" ❌ Erro ao verificar ordens OCO: {exc}")

def check_trailing_stop_orders(client, symbol: str) -> List[Dict]:
    try:
        # Verificar se client tem atributo exchange, caso contrário, assumir que client é a exchange
        exchange = getattr(client, 'exchange', client)
        # Obter ordens algorítmicas pendentes específicas para trailing stop
        inst_id = exchange.markets[symbol]["id"]
        algo_orders = exchange.private_get_trade_orders_algo_pending({
            "instId": inst_id,
            "ordType": "move_order_stop"
        })
        
        trailing_orders = []
        if algo_orders and "data" in algo_orders:
            for order in algo_orders["data"]:
                # Mapear campos relevantes para o formato esperado
                formatted_order = {
                    "id": order.get("algoId", "N/A"),
                    "symbol": symbol,
                    "side": order.get("side", "N/A").lower(),
                    "amount": float(order.get("sz", "0")),
                    "status": "open" if order.get("state") == "live" else order.get("state", "N/A"),
                    "type": "move_order_stop",
                    "triggerPrice": float(order.get("activePx", "0")) if order.get("activePx") else "N/A",
                    "trailingPercent": float(order.get("callbackRatio", "0")) * 100 if order.get("callbackRatio") else "N/A",  # Converter para porcentagem
                    "trailingAmount": "N/A"  # Será calculado na exibição, se possível
                }
                trailing_orders.append(formatted_order)
        
        client.logger.debug(
            "Ordens Trailing Stop verificadas para %s: %d encontradas",
            symbol,
            len(trailing_orders),
        )
        return trailing_orders
    except Exception as exc:
        client.logger.error(
            "Erro ao verificar ordens Trailing Stop para %s: %s", symbol, str(exc)
        )
        return []

def check_closed_trailing_stop_orders(client, symbol: str, database) -> List[Dict]:
    """
    Check for recently closed trailing stop orders and update database associations.
    
    Args:
        client: The exchange client instance.
        symbol: The trading pair symbol to check orders for.
        database: The database instance to update order associations.
    
    Returns:
        List of closed trailing stop orders that were processed.
    """
    try:
        # Verificar se client tem atributo exchange, caso contrário, assumir que client é a exchange
        exchange = getattr(client, 'exchange', client)
        # Fetch closed algo orders with trailing stop parameters
        inst_id = exchange.markets[symbol]["id"]
        closed_algo_orders = exchange.private_get_trade_orders_algo_history({
            "instId": inst_id,
            "ordType": "move_order_stop",
            "state": "effective"  # Orders that have been triggered
        })
        
        processed_orders = []
        if closed_algo_orders and "data" in closed_algo_orders:
            for order in closed_algo_orders["data"]:
                algo_id = order.get("algoId", "N/A")
                # Check if this order is already in trade_history to avoid duplicate processing
                with sqlite3.connect(database.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute(
                        "SELECT COUNT(*) FROM trade_history WHERE sell_order_id = ?",
                        (algo_id,)
                    )
                    if cursor.fetchone()[0] > 0:
                        continue  # Skip already processed orders
                
                # If it's a sell order, try to find the corresponding buy order from trailing_stops table
                if order.get("side", "").lower() == "sell":
                    with sqlite3.connect(database.db_path) as conn:
                        cursor = conn.cursor()
                        cursor.execute(
                            "SELECT order_id FROM trailing_stops WHERE algo_id = ? AND symbol = ?",
                            (algo_id, symbol)
                        )
                        result = cursor.fetchone()
                        if result:
                            buy_order_id = result[0]
                            buy_order = database.get_order_by_id(buy_order_id)
                            if buy_order:
                                # Save to trade_history
                                with sqlite3.connect(database.db_path) as conn:
                                    cursor = conn.cursor()
                                    cursor.execute(
                                        """
                                        INSERT INTO trade_history 
                                        (symbol, buy_order_id, sell_order_id, buy_price, sell_price, amount, pnl, open_timestamp, close_timestamp)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                                        """,
                                        (
                                            symbol,
                                            buy_order_id,
                                            algo_id,
                                            buy_order.get("price"),
                                            float(order.get("fillPx", "0")) if order.get("fillPx") else 0,
                                            float(order.get("sz", "0")),
                                            None,  # PnL to be calculated if needed
                                            buy_order.get("timestamp"),
                                            order.get("uTime", order.get("cTime", ""))
                                        ),
                                    )
                                    conn.commit()
                                processed_orders.append(order)
                                client.logger.info(
                                    "Associated trailing stop sell order %s with buy order %s for %s",
                                    algo_id, buy_order_id, symbol
                                )
                
                # Save the order to the orders table if not already present
                formatted_order = {
                    "id": algo_id,
                    "symbol": symbol,
                    "type": "move_order_stop",
                    "side": order.get("side", "N/A").lower(),
                    "price": float(order.get("fillPx", "0")) if order.get("fillPx") else None,
                    "amount": float(order.get("sz", "0")),
                    "status": "closed",
                    "pnl": None,
                    "info": {"ordType": "move_order_stop"}
                }
                database.save_order(formatted_order)
        
        client.logger.debug(
            "Closed trailing stop orders checked for %s: %d processed",
            symbol,
            len(processed_orders)
        )
        return processed_orders
    except Exception as exc:
        client.logger.error(
            "Error checking closed trailing stop orders for %s: %s", symbol, str(exc)
        )
        return []

def check_closed_oco_orders(client, symbol: str, database) -> List[Dict]:
    """
    Check for recently closed OCO orders and update database associations.
    
    Args:
        client: The exchange client instance.
        symbol: The trading pair symbol to check orders for.
        database: The database instance to update order associations.
    
    Returns:
        List of closed OCO orders that were processed.
    """
    try:
        # Verificar se client tem atributo exchange, caso contrário, assumir que client é a exchange
        exchange = getattr(client, 'exchange', client)
        # Fetch closed orders with OCO parameters
        closed_orders = exchange.fetch_closed_orders(
            symbol, params={"trigger": True, "ordType": "oco"}
        )
        processed_orders = []
        
        for order in closed_orders:
            order_id = order.get("id")
            # Check if this order is already in trade_history to avoid duplicate processing
            with sqlite3.connect(database.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT COUNT(*) FROM trade_history WHERE sell_order_id = ?",
                    (order_id,)
                )
                if cursor.fetchone()[0] > 0:
                    continue  # Skip already processed orders
            
            # If it's a sell order, try to find the corresponding buy order
            if order.get("side", "").lower() == "sell":
                buy_order = database.get_matching_buy_order(order_id, symbol)
                if buy_order:
                    # Save to trade_history
                    with sqlite3.connect(database.db_path) as conn:
                        cursor = conn.cursor()
                        cursor.execute(
                            """
                            INSERT INTO trade_history 
                            (symbol, buy_order_id, sell_order_id, buy_price, sell_price, amount, pnl, open_timestamp, close_timestamp)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                            """,
                            (
                                symbol,
                                buy_order.get("id"),
                                order_id,
                                buy_order.get("price"),
                                order.get("price"),
                                order.get("amount"),
                                order.get("pnl"),
                                buy_order.get("timestamp"),
                                order.get("timestamp")
                            ),
                        )
                        conn.commit()
                    processed_orders.append(order)
                    client.logger.debug(
                        "Associated sell order %s with buy order %s for %s",
                        order_id, buy_order.get("id"), symbol
                    )
            
            # Save the order to the orders table if not already present
            database.save_order(order)
        
        client.logger.debug(
            "Closed OCO orders checked for %s: %d processed",
            symbol,
            len(processed_orders)
        )
        return processed_orders
    except Exception as exc:
        client.logger.error(
            "Error checking closed OCO orders for %s: %s", symbol, str(exc)
        )
        return []

def display_trailing_stop_orders(client) -> None:
    from tabulate import tabulate
    print("\n📉 Ordens Trailing Stop Abertas:")
    print("=" * 118)
    try:
        found_orders = False
        for symbol in client.config.TRADING_SYMBOLS:
            trailing_orders = check_trailing_stop_orders(client, symbol)
            if trailing_orders:
                found_orders = True
                print(f" 📌 {symbol}:")
                headers = [
                    "Símbolo",
                    "ID",
                    "Lado",
                    "Quantidade",
                    "Trigger",
                    "Trailing %",
                    "Trailing Amount",
                    "Estado",
                ]
                table = []
                for order in trailing_orders[:5]:
                    order_id = order.get("id", "N/A")
                    side = order.get("side", "N/A")
                    amount = order.get("amount", "N/A")
                    if isinstance(amount, (int, float)):
                        amount = client.format_amount_with_precision(symbol, amount)
                    trigger_price = order.get("triggerPrice", "N/A")
                    trailing_percent = order.get("trailingPercent", "N/A")
                    # Calcular Trailing Amount se possível
                    if trailing_percent != "N/A" and trigger_price != "N/A":
                        trailing_amount = (trailing_percent / 100) * trigger_price
                        trailing_amount = round(trailing_amount, 2)
                    else:
                        trailing_amount = "N/A"
                    status = order.get("status", "N/A")
                    table.append(
                        [
                            symbol,
                            order_id,
                            side,
                            amount,
                            trigger_price,
                            f"{trailing_percent:.2f}%" if isinstance(trailing_percent, (int, float)) else trailing_percent,
                            trailing_amount,
                            status,
                        ]
                    )
                print(tabulate(table, headers, tablefmt="fancy_grid"))
                if len(trailing_orders) > 5:
                    print(
                        f" ... e mais {len(trailing_orders) - 5} ordens Trailing Stop."
                    )
        if not found_orders:
            print(" Nenhuma ordem Trailing Stop aberta encontrada.")
    except Exception as exc:
        client.logger.error("Erro ao exibir ordens Trailing Stop: %s", str(exc))
        print(f" ❌ Erro ao verificar ordens Trailing Stop: {exc}")
