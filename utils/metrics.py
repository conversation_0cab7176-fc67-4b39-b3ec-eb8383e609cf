"""
Módulo para cálculo de métricas de desempenho de trading.
"""

import numpy as np
from typing import Dict, List, Optional
from utils.logger import TradingLogger
from utils.database import TradingDatabase

class MetricsCalculator:
    """Classe para calcular métricas de desempenho após conclusão de ordens."""
    
    def __init__(self, db_path: str, logger_name: str = __name__):
        """
        Inicializa o MetricsCalculator com conexão ao banco de dados e logger.
        
        Args:
            db_path (str): Caminho para o banco de dados SQLite.
            logger_name (str): Nome do logger para registro de atividades.
        """
        self.logger = TradingLogger.get_logger(logger_name)
        self.db = TradingDatabase(db_path)
        self.logger.info("Metrics Ascending MetricsCalculator inicializado com sucesso.")
        
    def calculate_pnl(self, buy_price: float, sell_price: float, amount: float) -> float:
        """
        Calcula o Lucro/Prejuízo (PnL) de uma operação.
        
        Args:
            buy_price (float): Preço de compra.
            sell_price (float): Preço de venda.
            amount (float): Quantidade da operação.
            
        Returns:
            float: Valor do PnL em moeda base.
        """
        try:
            pnl = (sell_price - buy_price) * amount
            self.logger.debug("PnL calculado: %.2f (Compra: %.2f, Venda: %.2f, Quantidade: %.2f)", 
                            pnl, buy_price, sell_price, amount)
            return pnl
        except Exception as e:
            self.logger.error("Erro ao calcular PnL: %s", str(e))
            return 0.0
            
    def calculate_win_rate(self, symbol: str) -> float:
        """
        Calcula a taxa de acerto (win rate) para um símbolo baseado em ordens fechadas.
        
        Args:
            symbol (str): Símbolo do par de trading.
            
        Returns:
            float: Percentual de operações lucrativas (0.0 a 1.0).
        """
        try:
            closed_orders = self.db.get_closed_orders(symbol)
            if not closed_orders:
                self.logger.debug("Nenhuma ordem fechada encontrada para %s", symbol)
                return 0.0
                
            total_trades = len(closed_orders)
            winning_trades = 0
            
            for order in closed_orders:
                if order.get("side") == "sell":
                    buy_order = self.db.get_matching_buy_order(order.get("id", ""), symbol)
                    if buy_order:
                        buy_price = float(buy_order.get("price", 0.0) or 0.0)
                        sell_price = float(order.get("price", 0.0) or 0.0)
                        if sell_price > buy_price:
                            winning_trades += 1
                            
            win_rate = winning_trades / total_trades if total_trades > 0 else 0.0
            self.logger.debug("Taxa de acerto para %s: %.2f%% (%d/%d)", 
                            symbol, win_rate * 100, winning_trades, total_trades)
            return win_rate
        except Exception as e:
            self.logger.error("Erro ao calcular taxa de acerto para %s: %s", symbol, str(e))
            return 0.0
            
    def calculate_sharpe_ratio(self, symbol: str, risk_free_rate: float = 0.02) -> float:
        """
        Calcula o Sharpe Ratio para um símbolo baseado em retornos históricos.
        
        Args:
            symbol (str): Símbolo do par de trading.
            risk_free_rate (float): Taxa livre de risco anual (padrão 2%).
            
        Returns:
            float: Sharpe Ratio.
        """
        try:
            closed_orders = self.db.get_closed_orders(symbol)
            if len(closed_orders) < 2:
                self.logger.debug("Ordens insuficientes para calcular Sharpe Ratio para %s", symbol)
                return 0.0
                
            returns = []
            for order in closed_orders:
                if order.get("side") == "sell":
                    buy_order = self.db.get_matching_buy_order(order.get("id", ""), symbol)
                    if buy_order:
                        buy_price = float(buy_order.get("price", 0.0) or 0.0)
                        sell_price = float(order.get("price", 0.0) or 0.0)
                        ret = (sell_price - buy_price) / buy_price
                        returns.append(ret)
                        
            if not returns:
                self.logger.debug("Nenhum retorno calculado para Sharpe Ratio de %s", symbol)
                return 0.0
                
            mean_return = np.mean(returns)
            std_return = np.std(returns, ddof=1) if len(returns) > 1 else 0.0
            annualized_return = mean_return * 252  # Aproximadamente 252 dias de trading por ano
            annualized_std = std_return * np.sqrt(252)
            
            if annualized_std > 0:
                sharpe_ratio = (annualized_return - risk_free_rate) / annualized_std
            else:
                sharpe_ratio = 0.0
                self.logger.warning("Divisão por zero evitada no cálculo do Sharpe Ratio para %s: desvio padrão anualizado é 0", symbol)
            self.logger.debug("Sharpe Ratio para %s: %.2f", symbol, sharpe_ratio)
            return sharpe_ratio
        except Exception as e:
            self.logger.error("Erro ao calcular Sharpe Ratio para %s: %s", symbol, str(e))
            return 0.0
            
    def calculate_sortino_ratio(self, symbol: str, risk_free_rate: float = 0.02) -> float:
        """
        Calcula o Sortino Ratio para um símbolo baseado em retornos históricos.
        
        Args:
            symbol (str): Símbolo do par de trading.
            risk_free_rate (float): Taxa livre de risco anual (padrão 2%).
            
        Returns:
            float: Sortino Ratio.
        """
        try:
            closed_orders = self.db.get_closed_orders(symbol)
            if len(closed_orders) < 2:
                self.logger.debug("Ordens insuficientes para calcular Sortino Ratio para %s", symbol)
                return 0.0
                
            returns = []
            for order in closed_orders:
                if order.get("side") == "sell":
                    buy_order = self.db.get_matching_buy_order(order.get("id", ""), symbol)
                    if buy_order:
                        buy_price = float(buy_order.get("price", 0.0) or 0.0)
                        sell_price = float(order.get("price", 0.0) or 0.0)
                        ret = (sell_price - buy_price) / buy_price
                        returns.append(ret)
                        
            if not returns:
                self.logger.debug("Nenhum retorno calculado para Sortino Ratio de %s", symbol)
                return 0.0
                
            mean_return = np.mean(returns)
            downside_returns = [r for r in returns if r < 0]
            downside_std = np.std(downside_returns, ddof=1) if downside_returns else 0.0
            annualized_return = mean_return * 252
            annualized_downside_std = downside_std * np.sqrt(252)
            
            if annualized_downside_std > 0:
                sortino_ratio = (annualized_return - risk_free_rate) / annualized_downside_std
            else:
                sortino_ratio = 0.0
                self.logger.warning("Divisão por zero evitada no cálculo do Sortino Ratio para %s: desvio padrão de downside anualizado é 0", symbol)
            self.logger.debug("Sortino Ratio para %s: %.2f", symbol, sortino_ratio)
            return sortino_ratio
        except Exception as e:
            self.logger.error("Erro ao calcular Sortino Ratio para %s: %s", symbol, str(e))
            return 0.0
            
    def calculate_max_drawdown(self, symbol: str) -> float:
        """
        Calcula o Maximum Drawdown para um símbolo baseado em retornos históricos.
        
        Args:
            symbol (str): Símbolo do par de trading.
            
        Returns:
            float: Maximum Drawdown como percentual negativo (0.0 a -1.0).
        """
        try:
            closed_orders = self.db.get_closed_orders(symbol)
            if not closed_orders:
                self.logger.debug("Nenhuma ordem fechada para calcular Max Drawdown de %s", symbol)
                return 0.0
                
            equity_curve = [0.0]
            for order in closed_orders:
                if order.get("side") == "sell":
                    buy_order = self.db.get_matching_buy_order(order.get("id", ""), symbol)
                    if buy_order:
                        buy_price = float(buy_order.get("price", 0.0) or 0.0)
                        sell_price = float(order.get("price", 0.0) or 0.0)
                        amount = min(float(buy_order.get("amount", 0.0) or 0.0), 
                                   float(order.get("amount", 0.0) or 0.0))
                        pnl = (sell_price - buy_price) * amount
                        equity_curve.append(equity_curve[-1] + pnl)
                        
            if len(equity_curve) < 2:
                self.logger.debug("Dados insuficientes na curva de equity para Max Drawdown de %s", symbol)
                return 0.0
                
            peak = equity_curve[0]
            max_drawdown = 0.0
            
            for equity in equity_curve:
                if equity > peak:
                    peak = equity
                drawdown = (equity - peak) / peak if peak != 0 else 0.0
                if drawdown < max_drawdown:
                    max_drawdown = drawdown
                    
            self.logger.debug("Max Drawdown para %s: %.2f%%", symbol, max_drawdown * 100)
            return max_drawdown
        except Exception as e:
            self.logger.error("Erro ao calcular Max Drawdown para %s: %s", symbol, str(e))
            return 0.0
            
    def get_trade_metrics(self, symbol: str, buy_price: float, sell_price: float, amount: float) -> Dict:
        """
        Calcula métricas para uma operação específica e métricas acumuladas do símbolo.
        
        Args:
            symbol (str): Símbolo do par de trading.
            buy_price (float): Preço de compra.
            sell_price (float): Preço de venda.
            amount (float): Quantidade da operação.
            
        Returns:
            Dict: Dicionário com métricas da operação e acumuladas.
        """
        try:
            pnl = self.calculate_pnl(buy_price, sell_price, amount)
            win_rate = self.calculate_win_rate(symbol)
            sharpe_ratio = self.calculate_sharpe_ratio(symbol)
            sortino_ratio = self.calculate_sortino_ratio(symbol)
            max_drawdown = self.calculate_max_drawdown(symbol)
            
            metrics = {
                "pnl": pnl,
                "win_rate": win_rate,
                "sharpe_ratio": sharpe_ratio,
                "sortino_ratio": sortino_ratio,
                "max_drawdown": max_drawdown
            }
            
            self.logger.info("Métricas calculadas para operação de %s: PnL=%.2f, WinRate=%.2f%%", 
                           symbol, pnl, win_rate * 100)
            return metrics
        except Exception as e:
            self.logger.error("Erro ao calcular métricas de operação para %s: %s", symbol, str(e))
            return {
                "pnl": 0.0,
                "win_rate": 0.0,
                "sharpe_ratio": 0.0,
                "sortino_ratio": 0.0,
                "max_drawdown": 0.0
            }
