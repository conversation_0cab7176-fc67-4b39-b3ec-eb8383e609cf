"""
Script para monitorar Spot Grid Bots da OKX usando CCXT
Autor: Bluetuga
Data: 2025-06-18
"""

import os
import sys
import json
from datetime import datetime
from dotenv import load_dotenv
import ccxt
from typing import Dict, List, Optional, Any
from utils.error_handler import <PERSON>rror<PERSON>andler
from utils.logger import TradingLogger

# Constants for magic numbers and strings
GRID_ORD_TYPE = "grid"
SUB_ORDER_TYPE_LIVE = "live"
DEFAULT_SUB_ORDER_LIMIT = 10
DEFAULT_HISTORY_LIMIT = 5
DETAILED_SUB_ORDER_LIMIT = 50
MAX_SUB_ORDERS_DISPLAY = 10
CONTINUOUS_MONITOR_DELAY = 30
EXPORT_HISTORY_LIMIT = 20
CONFIG_PREFIX = "live"
SANDBOX_MODE = False


class OKXGridBotAPI:
    """Classe para interagir com a API da OKX e obter dados de Grid Bots"""

    def __init__(self, config: Dict[str, Any] = None):
        """Inicializa o cliente da API da OKX com configuração opcional"""
        self.exchange = None
        self.logger = TradingLogger.get_logger(__name__)
        self.error_handler = ErrorHandler(
            logger_name=__name__, max_retries=3, retry_delay=2
        )
        self.config = config if config else self._default_config()
        self.load_credentials()
        self.initialize_exchange()

    def _default_config(self) -> Dict[str, Any]:
        """Retorna uma configuração padrão se nenhuma for fornecida"""
        return {
            "prefix": CONFIG_PREFIX,
            "sandbox_mode": SANDBOX_MODE,
            "api_key_env": f"{CONFIG_PREFIX}_okx_apiKey",
            "secret_key_env": f"{CONFIG_PREFIX}_okx_secret",
            "password_env": f"{CONFIG_PREFIX}_okx_password",
        }

    def load_credentials(self):
        """Carrega credenciais do arquivo .env com base na configuração"""
        load_dotenv()

        self.api_key = os.getenv(self.config["api_key_env"])
        self.secret_key = os.getenv(self.config["secret_key_env"])
        self.password = os.getenv(self.config["password_env"])

        if not all([self.api_key, self.secret_key, self.password]):
            error_msg = f"Credenciais não encontradas para prefixo {self.config['prefix']}. Verifique seu arquivo .env"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

    def initialize_exchange(self):
        """Inicializa a conexão com a OKX usando um adaptador para reduzir acoplamento"""

        def init_exchange():
            return ccxt.myokx(
                {
                    "apiKey": self.api_key,
                    "secret": self.secret_key,
                    "password": self.password,
                    "sandbox": self.config["sandbox_mode"],
                    "enableRateLimit": True,
                }
            )

        result = self.error_handler.execute_with_retry(
            init_exchange, "Inicializar conexão com OKX"
        )
        if result is None:
            error_msg = "Falha ao inicializar conexão com OKX após várias tentativas"
            self.logger.error(error_msg)
            raise Exception(error_msg)
        self.exchange = result

    def _make_api_call(
        self, method: callable, params: Dict[str, Any], operation_name: str
    ) -> Optional[Any]:
        """Método auxiliar para realizar chamadas de API com tratamento de erro padronizado"""

        def api_call():
            response = method(params)
            if response.get("code") == "0":
                data = response.get("data", [])
                return (
                    data[0]
                    if data and isinstance(data, list) and len(data) == 1
                    else data
                )
            else:
                error_msg = f"Erro na API: {response.get('msg')}"
                self.logger.error(error_msg)
                return None

        result = self.error_handler.execute_with_retry(api_call, operation_name)
        if result is None:
            self.logger.warning(
                f"Falha ao executar {operation_name} após várias tentativas"
            )
        return result

    def get_active_grid_bots(self) -> Optional[List[Dict]]:
        """Obtém lista de grid bots ativos"""
        return self._make_api_call(
            self.exchange.privateGetTradingBotGridOrdersAlgoPending,
            {"algoOrdType": GRID_ORD_TYPE},
            "Obter grid bots ativos",
        )

    def get_grid_bot_details(self, algo_id: str) -> Optional[Dict]:
        """Obtém detalhes de um grid bot específico"""
        return self._make_api_call(
            self.exchange.privateGetTradingBotGridOrdersAlgoDetails,
            {"algoId": algo_id, "algoOrdType": GRID_ORD_TYPE},
            f"Obter detalhes do grid bot {algo_id}",
        )

    def get_grid_bot_sub_orders(
        self, algo_id: str, limit: int = DEFAULT_SUB_ORDER_LIMIT
    ) -> Optional[List[Dict]]:
        """Obtém sub-ordens de um grid bot"""
        return self._make_api_call(
            self.exchange.privateGetTradingBotGridSubOrders,
            {
                "algoId": algo_id,
                "algoOrdType": GRID_ORD_TYPE,
                "type": SUB_ORDER_TYPE_LIVE,
                "limit": str(limit),
            },
            f"Obter sub-ordens do grid bot {algo_id}",
        )

    def get_grid_bot_history(
        self, limit: int = DEFAULT_HISTORY_LIMIT
    ) -> Optional[List[Dict]]:
        """Obtém histórico de grid bots"""
        return self._make_api_call(
            self.exchange.privateGetTradingBotGridOrdersAlgoHistory,
            {"limit": str(limit), "algoOrdType": GRID_ORD_TYPE},
            "Obter histórico de grid bots",
        )


class OKXGridBotFormatter:
    """Classe para formatar dados de Grid Bots da OKX"""

    def calculate_roi(self, total_pnl: str, investment: str) -> str:
        """Calcula o ROI do grid bot"""
        try:
            pnl_val = float(total_pnl)
            inv_val = float(investment)
            if inv_val > 0:
                roi = (pnl_val / inv_val) * 100
                return f"{roi:+.2f}%"
            return "N/A"
        except (ValueError, TypeError, ZeroDivisionError):
            return "N/A"

    def format_currency(self, value: str, currency: str = "USDT") -> str:
        """Formata valores monetários"""
        try:
            num_value = float(value)
            return f"{num_value:,.2f} {currency}"
        except (ValueError, TypeError):
            return f"{value} {currency}"

    def format_percentage(self, value: str) -> str:
        """Formata percentuais"""
        try:
            num_value = float(value) * 100
            return f"{num_value:+.2f}%"
        except (ValueError, TypeError):
            return f"{value}%"

    def get_pnl_status_emoji(self, pnl: str) -> str:
        """Retorna emoji baseado no PnL"""
        try:
            pnl_val = float(pnl)
            if pnl_val > 0:
                return "🟢"
            elif pnl_val < 0:
                return "🔴"
            else:
                return "⚪"
        except (ValueError, TypeError):
            return "⚪"

    def format_grid_bot_info(self, bot_data: Dict, details: Dict = None) -> List[str]:
        """Formata informações de um grid bot como lista de strings"""
        total_pnl = bot_data.get("totalPnl", "0")
        pnl_emoji = self.get_pnl_status_emoji(total_pnl)
        output = [
            "\n" + "=" * 70,
            f"🤖 Grid Bot ID: {bot_data.get('algoId', 'N/A')}",
            f"📊 Símbolo: {bot_data.get('instId', 'N/A')}",
            f"🔄 Estado: {bot_data.get('state', 'N/A')}",
            f"{pnl_emoji} PnL Total: {self.format_currency(total_pnl)}",
            f"📈 PnL da Grade: {self.format_currency(bot_data.get('gridProfit', '0'))}",
            f"💸 PnL Não Realizado: {self.format_currency(bot_data.get('floatProfit', '0'))}",
            f"💵 Investimento: {self.format_currency(bot_data.get('investment', '0'))}",
            f"📊 ROI: {self.calculate_roi(total_pnl, bot_data.get('investment', '0'))}",
        ]

        if details:
            output.extend(
                [
                    f"🔄 Arbitragens: {details.get('arbitrageNum', 'N/A')}",
                    f"📊 Taxa Anual: {self.format_percentage(details.get('annualizedRate', '0'))}",
                    f"⏰ Criado em: {self.timestamp_to_date(details.get('cTime', '0'))}",
                    f"🔧 Preço Inferior: {details.get('minPx', 'N/A')}",
                    f"🔧 Preço Superior: {details.get('maxPx', 'N/A')}",
                    f"🔧 Quantidade de Grids: {details.get('gridNum', 'N/A')}",
                ]
            )
        return output

    def format_sub_orders_summary(self, sub_orders: List[Dict]) -> List[str]:
        """Formata resumo das sub-ordens como lista de strings"""
        if not sub_orders:
            return ["📋 Nenhuma sub-ordem ativa"]

        buy_orders = [o for o in sub_orders if o.get("side") == "buy"]
        sell_orders = [o for o in sub_orders if o.get("side") == "sell"]
        return [
            f"📋 Sub-ordens ativas: {len(sub_orders)}",
            f"• Compra: {len(buy_orders)}",
            f"• Venda: {len(sell_orders)}",
        ]

    def format_detailed_sub_orders(self, sub_orders: List[Dict]) -> List[str]:
        """Formata detalhes das sub-ordens como lista de strings"""
        output = [f"SUB-ORDENS ATIVAS ({len(sub_orders)}):"]
        for i, order in enumerate(sub_orders[:MAX_SUB_ORDERS_DISPLAY], 1):
            side_emoji = "🟢" if order.get("side") == "buy" else "🔴"
            output.append(
                f"{i}. {side_emoji} {order.get('side', 'N/A').upper()}: "
                f"{order.get('sz', '0')} @ {order.get('px', '0')}"
            )
        if len(sub_orders) > MAX_SUB_ORDERS_DISPLAY:
            output.append(
                f"... e mais {len(sub_orders) - MAX_SUB_ORDERS_DISPLAY} ordens"
            )
        return output

    def timestamp_to_date(self, timestamp: str) -> str:
        """Converte timestamp para data legível"""
        try:
            ts = int(timestamp) / 1000  # OKX usa timestamp em milissegundos
            return datetime.fromtimestamp(ts).strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError):
            return "Data inválida"


class OutputHandler:
    """Classe para gerenciar saída de dados, desacoplando lógica de exibição"""

    def __init__(self, logger=None):
        """Inicializa o manipulador de saída com um logger opcional"""
        self.logger = logger or TradingLogger.get_logger(__name__)

    def display(self, message: str):
        """Exibe uma mensagem, usando logger se disponível"""
        if isinstance(message, list):
            for msg in message:
                self.logger.info(msg)
        else:
            self.logger.info(message)

    def display_error(self, message: str):
        """Exibe uma mensagem de erro"""
        self.logger.error(message)

    def display_warning(self, message: str):
        """Exibe uma mensagem de aviso"""
        self.logger.warning(message)


class OKXGridBotMonitor:
    """Classe principal para coordenar o monitoramento de Grid Bots da OKX"""

    def __init__(self, config: Dict[str, Any] = None):
        """Inicializa o monitor de grid bots com configuração opcional"""
        self.api = OKXGridBotAPI(config)
        self.formatter = OKXGridBotFormatter()
        self.logger = TradingLogger.get_logger(__name__)
        self.error_handler = ErrorHandler(
            logger_name=__name__, max_retries=3, retry_delay=2
        )
        self.output_handler = OutputHandler(self.logger)

    def monitor_active_bots(self) -> Dict[str, Any]:
        """Monitora todos os grid bots ativos e retorna dados para exibição"""
        self.logger.info("Buscando grid bots ativos...")
        active_bots = self.api.get_active_grid_bots()

        if not active_bots:
            self.logger.warning("Nenhum grid bot ativo encontrado")
            return {"active_bots": [], "total_pnl": 0, "total_investment": 0}

        self.logger.info(f"Encontrados {len(active_bots)} grid bot(s) ativo(s)")
        total_pnl = 0
        total_investment = 0
        bot_details_list = []

        for i, bot in enumerate(active_bots, 1):
            self.logger.debug(f"Processando Grid Bot {i}/{len(active_bots)}")
            algo_id = bot.get("algoId")
            details = self.api.get_grid_bot_details(algo_id) if algo_id else None
            sub_orders = (
                self.api.get_grid_bot_sub_orders(algo_id, DEFAULT_SUB_ORDER_LIMIT)
                if algo_id
                else None
            )

            bot_details_list.append(
                {"bot_data": bot, "details": details, "sub_orders": sub_orders}
            )

            try:
                total_pnl += float(bot.get("totalPnl", "0"))
                total_investment += float(bot.get("investment", "0"))
            except (ValueError, TypeError):
                self.logger.debug(f"Erro ao converter valores para bot {algo_id}")

        return {
            "active_bots": bot_details_list,
            "total_pnl": total_pnl,
            "total_investment": total_investment,
        }

    def get_bot_history(self, limit: int = DEFAULT_HISTORY_LIMIT) -> List[Dict]:
        """Obtém histórico de grid bots"""
        self.logger.info(f"Buscando histórico dos últimos {limit} grid bots...")
        history = self.api.get_grid_bot_history(limit)
        if not history:
            self.logger.warning("Nenhum histórico encontrado")
            return []
        self.logger.info(f"Encontrados {len(history)} bot(s) no histórico")
        return history

    def get_detailed_bot_info(self, algo_id: str) -> Dict[str, Any]:
        """Obtém informações detalhadas de um bot específico"""
        self.logger.info(f"Buscando detalhes do bot {algo_id}...")
        details = self.api.get_grid_bot_details(algo_id)
        if not details:
            self.logger.warning("Bot não encontrado")
            return {"details": None, "sub_orders": []}
        sub_orders = self.api.get_grid_bot_sub_orders(algo_id, DETAILED_SUB_ORDER_LIMIT)
        return {"details": details, "sub_orders": sub_orders if sub_orders else []}

    def export_data_to_json(self) -> bool:
        """Exporta dados de bots ativos e histórico para arquivo JSON"""

        def export_data():
            self.logger.info("Exportando dados...")
            active_bots = self.api.get_active_grid_bots()
            history = self.api.get_grid_bot_history(EXPORT_HISTORY_LIMIT)
            detailed_active_bots = []

            if active_bots:
                for bot in active_bots:
                    algo_id = bot.get("algoId")
                    if algo_id:
                        details = self.api.get_grid_bot_details(algo_id)
                        sub_orders = self.api.get_grid_bot_sub_orders(
                            algo_id, DETAILED_SUB_ORDER_LIMIT
                        )
                        bot_data = {
                            "basic_info": bot,
                            "details": details,
                            "sub_orders": sub_orders,
                        }
                        detailed_active_bots.append(bot_data)

            data = {
                "export_timestamp": datetime.now().isoformat(),
                "summary": {
                    "active_bots_count": len(active_bots) if active_bots else 0,
                    "history_count": len(history) if history else 0,
                },
                "active_bots": detailed_active_bots,
                "history": history or [],
            }

            filename = f"okx_grid_bots_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"Dados exportados para: {filename}")
            self.logger.info(f"Bots ativos: {len(detailed_active_bots)}")
            self.logger.info(f"Histórico: {len(history) if history else 0}")
            return True

        result = self.error_handler.execute_with_retry(
            export_data, "Exportar dados para JSON"
        )
        return result is not None


class GridBotUI:
    """Classe para gerenciar a interface do usuário e interações"""

    def __init__(self, monitor: OKXGridBotMonitor):
        """Inicializa a interface do usuário com uma instância de monitor"""
        self.monitor = monitor
        self.formatter = monitor.formatter
        self.output_handler = monitor.output_handler
        self.logger = monitor.logger

    def display_menu(self):
        """Exibe o menu principal"""
        menu = [
            "\n" + "=" * 70,
            "🤖 MONITOR DE GRID BOTS OKX",
            "=" * 70,
            "1. 📊 Monitorar bots ativos",
            "2. 📚 Ver histórico de bots",
            "3. 🔍 Buscar bot específico (detalhado)",
            "4. 📤 Exportar dados para JSON",
            "5. 🔄 Atualizar dados em tempo real",
            "6. ❌ Sair",
            "=" * 70,
        ]
        self.output_handler.display(menu)

    def get_user_choice(self) -> str:
        """Obtém a escolha do usuário com validação"""
        choice = input("Escolha uma opção (1-6): ").strip()
        if not choice or not choice.isdigit() or int(choice) not in range(1, 7):
            self.output_handler.display_error(
                "Opção inválida. Escolha um número entre 1 e 6."
            )
            return "0"
        return choice

    def get_history_limit(self) -> int:
        """Obtém o limite para histórico de bots com validação"""
        limit_str = input("Quantos bots mostrar no histórico? (padrão: 5): ").strip()
        if not limit_str or not limit_str.isdigit():
            return DEFAULT_HISTORY_LIMIT
        limit = int(limit_str)
        if limit <= 0:
            self.output_handler.display_warning("Limite inválido, usando valor padrão.")
            return DEFAULT_HISTORY_LIMIT
        return limit

    def get_bot_id(self) -> str:
        """Obtém o ID do bot do usuário"""
        algo_id = input("Digite o ID do grid bot: ").strip()
        if not algo_id:
            self.output_handler.display_error("ID do bot não pode estar vazio.")
        return algo_id

    def run_interactive_menu(self):
        """Executa o menu interativo principal"""
        while True:
            self.display_menu()
            choice = self.get_user_choice()
            if choice == "1":
                data = self.monitor.monitor_active_bots()
                self.display_active_bots(data)
            elif choice == "2":
                limit = self.get_history_limit()
                history = self.monitor.get_bot_history(limit)
                self.display_history(history)
            elif choice == "3":
                algo_id = self.get_bot_id()
                if algo_id:
                    data = self.monitor.get_detailed_bot_info(algo_id)
                    self.display_detailed_bot_info(data)
            elif choice == "4":
                success = self.monitor.export_data_to_json()
                if not success:
                    self.output_handler.display_error("Falha ao exportar dados.")
            elif choice == "5":
                self.run_continuous_monitoring()
            elif choice == "6":
                self.output_handler.display("👋 Saindo...")
                break
            input("\nPressione Enter para continuar...")

    def display_active_bots(self, data: Dict[str, Any]):
        """Exibe informações sobre bots ativos"""
        active_bots = data.get("active_bots", [])
        if not active_bots:
            self.output_handler.display_warning("Nenhum bot ativo encontrado.")
            return

        for i, bot_info in enumerate(active_bots, 1):
            self.output_handler.display(f"Grid Bot {i}/{len(active_bots)}")
            bot_data = bot_info["bot_data"]
            details = bot_info["details"]
            sub_orders = bot_info["sub_orders"]
            self.output_handler.display(
                self.formatter.format_grid_bot_info(bot_data, details)
            )
            if sub_orders is not None:
                self.output_handler.display(
                    self.formatter.format_sub_orders_summary(sub_orders)
                )

        if len(active_bots) > 1:
            total_pnl = data.get("total_pnl", 0)
            total_investment = data.get("total_investment", 0)
            summary = [
                "=" * 70,
                "RESUMO GERAL",
                "=" * 70,
                f"Total de Bots: {len(active_bots)}",
                f"PnL Total: {self.formatter.format_currency(str(total_pnl))}",
                f"Investimento Total: {self.formatter.format_currency(str(total_investment))}",
                f"ROI Geral: {self.formatter.calculate_roi(str(total_pnl), str(total_investment))}",
            ]
            self.output_handler.display(summary)

    def display_history(self, history: List[Dict]):
        """Exibe histórico de bots"""
        if not history:
            self.output_handler.display_warning("Nenhum histórico encontrado.")
            return

        for i, bot in enumerate(history, 1):
            self.output_handler.display(f"Bot Histórico {i}/{len(history)}")
            self.output_handler.display(self.formatter.format_grid_bot_info(bot))

    def display_detailed_bot_info(self, data: Dict[str, Any]):
        """Exibe informações detalhadas de um bot específico"""
        details = data.get("details")
        if not details:
            self.output_handler.display_warning("Bot não encontrado.")
            return

        self.output_handler.display(
            self.formatter.format_grid_bot_info(details, details)
        )
        sub_orders = data.get("sub_orders", [])
        if sub_orders:
            self.output_handler.display(
                self.formatter.format_detailed_sub_orders(sub_orders)
            )

    def run_continuous_monitoring(self):
        """Executa monitoramento contínuo até interrupção pelo usuário"""
        import time

        self.logger.info("Monitoramento contínuo iniciado (Ctrl+C para parar)")
        self.logger.info(f"Atualizando a cada {CONTINUOUS_MONITOR_DELAY} segundos...")

        try:
            while True:
                self.logger.info(
                    f"Atualização: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                )
                data = self.monitor.monitor_active_bots()
                self.display_active_bots(data)
                self.logger.info(f"Aguardando {CONTINUOUS_MONITOR_DELAY} segundos...")
                time.sleep(CONTINUOUS_MONITOR_DELAY)
        except KeyboardInterrupt:
            self.logger.info("Monitoramento contínuo interrompido")


def main():
    """Função principal"""
    logger = TradingLogger.get_logger(__name__)
    error_handler = ErrorHandler(logger_name=__name__, max_retries=3, retry_delay=2)

    def start_monitor():
        logger.info("Iniciando Monitor de Grid Bots OKX...")
        monitor = OKXGridBotMonitor()
        ui = GridBotUI(monitor)
        ui.run_interactive_menu()
        return True

    try:
        result = error_handler.execute_with_retry(
            start_monitor, "Iniciar Monitor de Grid Bots OKX"
        )
        if result is None:
            logger.error("Falha ao iniciar o monitor após várias tentativas")
            sys.exit(1)
    except KeyboardInterrupt:
        logger.info("Programa interrompido pelo usuário")
        sys.exit(0)


if __name__ == "__main__":
    main()
