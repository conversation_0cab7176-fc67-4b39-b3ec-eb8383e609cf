"""
Módulo auxiliar para centralizar o tratamento de erros repetitivos no bot de trading.
"""

import time
from typing import Optional, Callable, Any
from utils.logger import TradingLogger
from utils.exceptions import ExchangeConnectionError, ConfigurationError

class ErrorHandler:
    """Classe para gerenciar tentativas de reconexão e tratamento de erros com logging."""
    
    def __init__(self, logger_name: str, max_retries: int = 3, retry_delay: int = 2):
        """
        Inicializa o manipulador de erros.
        
        Args:
            logger_name (str): Nome do logger a ser usado.
            max_retries (int): Número máximo de tentativas para operações.
            retry_delay (int): Tempo de espera entre tentativas em segundos.
        """
        self.logger = TradingLogger.get_logger(logger_name)
        self.max_retries = max_retries
        self.retry_delay = retry_delay
    
    def execute_with_retry(self, operation: Callable, operation_name: str, *args, **kwargs) -> Optional[Any]:
        """
        Executa uma operação com tentativas de retry em caso de falha.
        
        Args:
            operation (Callable): Função a ser executada.
            operation_name (str): Nome da operação para logging.
            *args, **kwargs: Argumentos a serem passados para a operação.
            
        Returns:
            Optional[Any]: Resultado da operação ou None em caso de falha após todas as tentativas.
        """
        for attempt in range(self.max_retries):
            try:
                result = operation(*args, **kwargs)
                self.logger.debug(f"{operation_name} executado com sucesso na tentativa {attempt + 1}")
                return result
            except Exception as exc:
                self.logger.error(
                    f"Erro ao executar {operation_name} (tentativa {attempt + 1}/{self.max_retries}): {str(exc)}"
                )
                if attempt == self.max_retries - 1:
                    self.logger.error(
                        f"Falha ao executar {operation_name} após {self.max_retries} tentativas"
                    )
                    if isinstance(exc, (ExchangeConnectionError, ConfigurationError)):
                        raise
                    return None
                time.sleep(self.retry_delay)
        return None
    
    def log_and_handle_error(self, context: str, exc: Exception, critical: bool = False) -> None:
        """
        Registra um erro e fornece mensagens de log apropriadas.
        
        Args:
            context (str): Contexto ou descrição do erro.
            exc (Exception): Exceção capturada.
            critical (bool): Se o erro é crítico e deve gerar um raise.
        """
        self.logger.error(f"Erro em {context}: {str(exc)}")
        if critical:
            raise exc
            
    # Exemplo de uso do ErrorHandler:
    #
    # error_handler = ErrorHandler(logger_name="TradingBot", max_retries=3, retry_delay=2)
    #
    # # Exemplo 1: Usando execute_with_retry para operações com retry
    # def fetch_balance():
    #     return exchange.fetch_balance()
    #
    # balance = error_handler.execute_with_retry(fetch_balance, "Obter Saldo")
    # if balance is None:
    #     print("Falha ao obter saldo após várias tentativas")
    #
    # # Exemplo 2: Usando log_and_handle_error para logging de erros
    # try:
    #     result = some_operation()
    # except Exception as e:
    #     error_handler.log_and_handle_error("Operação XYZ", e, critical=False)
