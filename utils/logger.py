"""
Uso:
    from utils.logger_2 import get_logger
    log = get_logger(__name__)
    log.info("Estratégia executada com sucesso")
"""

import logging
import sys
from pathlib import Path
from typing import Optional, Dict
from datetime import datetime


class SensitiveDataFilter(logging.Filter):
    """Filtro para mascarar informações sensíveis nos logs"""
    def __init__(self):
        super().__init__()
        self.sensitive_patterns = [
            ("api_key", r"api_key=[^\s]*", "api_key=********"),
            ("secret", r"secret=[^\s]*", "secret=********"),
            ("password", r"password=[^\s]*", "password=********"),
            ("token", r"token=[^\s]*", "token=********"),
        ]

    def filter(self, record):
        msg = str(record.msg)
        for name, pattern, replacement in self.sensitive_patterns:
            import re
            msg = re.sub(pattern, replacement, msg)
        record.msg = msg
        return True


class ColorFormatter(logging.Formatter):
    COLORS = {
        logging.DEBUG: "\033[36m",  # Ciano - para debug detalhado
        logging.INFO: "\033[37m",  # Branco - info geral
        logging.WARNING: "\033[33m",  # Amarelo - avisos
        logging.ERROR: "\033[31m",  # Vermelho - erros
        logging.CRITICAL: "\033[35m",  # Magenta - crítico
    }
    RESET = "\033[0m"
    BOLD = "\033[1m"

    def __init__(self):
        super().__init__()
        self.formatters = {}

    def format(self, record):
        color = self.COLORS.get(record.levelno, self.RESET)
        if record.levelno >= logging.WARNING:
            format_str = f"{self.BOLD}{color}%(levelname)s{self.RESET} - {color}%(message)s{self.RESET}"
        else:
            format_str = f"{color}%(levelname)s - %(message)s{self.RESET}"

        if record.levelno not in self.formatters:
            self.formatters[record.levelno] = logging.Formatter(format_str)

        return self.formatters[record.levelno].format(record)


class TradingLogger:
    """Gerenciador centralizado de loggers para trading"""

    _loggers: Dict[str, logging.Logger] = {}
    _default_config = {
        "level": logging.INFO,
        "log_dir": "logs",
        "file_format": "%(asctime)s | %(name)s | %(levelname)s | %(message)s",
        "console_format": ColorFormatter()
    }

    @classmethod
    def get_logger(
        cls,
        name: str,
        level: Optional[int] = None,
        log_dir: Optional[str] = None,
        enable_file_log: bool = True,
        enable_console_log: bool = True,
    ) -> logging.Logger:
        """
        Retorna logger configurado com handlers otimizados

        Args:
            name: Nome do logger (recomendado usar __name__)
            level: Nível de log (default: INFO)
            log_dir: Diretório dos logs (default: 'logs')
            enable_file_log: Habilita log em arquivo
            enable_console_log: Habilita log no console
        """

        if name in cls._loggers:
            return cls._loggers[name]

        logger = logging.getLogger(name)
        logger.setLevel(level or cls._default_config["level"])
        logger.propagate = False

        # Limpa handlers existentes para evitar duplicação
        logger.handlers.clear()

        try:
            if enable_file_log:
                cls._add_file_handler(logger, name, log_dir)

            if enable_console_log:
                cls._add_console_handler(logger)

        except Exception as e:
            # Usa um logger temporário para registrar o erro
            temp_logger = logging.getLogger("logger_setup_error")
            temp_logger.setLevel(logging.ERROR)
            if not temp_logger.handlers:
                temp_console_handler = logging.StreamHandler(sys.stdout)
                temp_console_handler.setFormatter(cls._default_config["console_format"])
                temp_logger.addHandler(temp_console_handler)
            temp_logger.error(f"⚠️  Erro na configuração do logger '{name}': {str(e)}")
            # Fallback: logger básico apenas para console
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(cls._default_config["console_format"])
            logger.addHandler(console_handler)

        cls._loggers[name] = logger
        return logger

    @classmethod
    def _add_file_handler(
        cls, logger: logging.Logger, name: str, log_dir: Optional[str]
    ):
        """Adiciona handler para arquivo com rotação"""
        from logging.handlers import RotatingFileHandler

        log_path = Path(log_dir or cls._default_config["log_dir"])
        log_path.mkdir(exist_ok=True)

        # Nome do arquivo com timestamp para organização
        today = datetime.now().strftime("%Y-%m-%d")
        log_file = log_path / f"{name}_{today}.log"

        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding="utf-8",
        )

        file_formatter = logging.Formatter(cls._default_config["file_format"])
        file_handler.setFormatter(file_formatter)
        file_handler.addFilter(SensitiveDataFilter())
        logger.addHandler(file_handler)

    @classmethod
    def _add_console_handler(cls, logger: logging.Logger):
        """Adiciona handler colorido para console"""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(cls._default_config["console_format"])
        console_handler.addFilter(SensitiveDataFilter())
        logger.addHandler(console_handler)

    @classmethod
    def set_global_level(cls, level: int):
        """Define nível global para todos os loggers criados"""
        cls._default_config["level"] = level
        for logger in cls._loggers.values():
            logger.setLevel(level)

    @classmethod
    def configure_trading_loggers(
        cls, strategy_level: int = logging.INFO, debug_mode: bool = False
    ):
        """Configuração específica para estratégias de trading"""
        base_level = logging.DEBUG if debug_mode else strategy_level

        # Loggers específicos para diferentes componentes
        loggers_config = {
            "strategy": base_level,
            "backtest": base_level,
            "ccxt": logging.WARNING,  # CCXT pode ser verboso
            "talib": logging.INFO,
            "portfolio": base_level,
            "risk_management": base_level,
        }

        for logger_name, level in loggers_config.items():
            logger = cls.get_logger(logger_name, level=level)
            logger.info(
                f"Logger '{logger_name}' configurado (nível: {logging.getLevelName(level)})"
            )


# Exemplo de uso e teste
# if __name__ == "__main__":
#     instance = TradingLogger()
#     # Teste dos diferentes níveis e cores
#     log = instance.get_logger("trading_test")

#     print("🔄 Testando sistema de logging...")
#     log.debug("🔧 Debug: Análise técnica - RSI calculado")
#     log.info("📊 Info: Estratégia MACD executada com sucesso")
#     log.warning("⚠️  Warning: Volume baixo detectado - possível falso sinal")
#     log.error("❌ Error: Falha na conexão com exchange")
#     log.critical("🚨 Critical: Stop-loss ativado - posição fechada")

#     # Teste de logger específico para backtest
#     backtest_log = instance.get_logger("backtest")
#     backtest_log.info("✅ Backtest concluído: 156 trades, 68% win rate")

#     print("\n📁 Logs salvos em: ./logs/")
#     print("🎯 Logger pronto para uso em projetos de trading!")
