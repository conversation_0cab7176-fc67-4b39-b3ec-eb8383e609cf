"""
Módulo para validação de condições de ordens de trading.
"""

from typing import Dict, Optional
from utils.logger import TradingLogger
from utils.check_orders import check_regular_orders, check_oco_orders, check_trailing_stop_orders


class OrderValidator:
    """Classe para centralizar métodos de validação de ordens."""

    def __init__(self, client):
        """
        Inicializa o validador de ordens com um cliente da exchange.

        Args:
            client: Instância do cliente da exchange (ex: OKXClient)
        """
        self.client = client
        self.logger = TradingLogger.get_logger(__name__)

    def _validate_ticker(self, symbol: str) -> bool:
        """
        Verifica se o mercado está ativo obtendo o ticker.

        Args:
            symbol: Par de trading

        Returns:
            True se o ticker está disponível, False caso contrário
        """
        ticker = self.client.get_ticker(symbol)
        if not ticker:
            self.logger.warning("Ticker indisponível para %s", symbol)
            return False
        return True

    def _check_existing_regular_orders(self, symbol: str) -> bool:
        """
        Verifica se existem ordens regulares abertas para o símbolo.

        Args:
            symbol: Par de trading

        Returns:
            True se não há ordens regulares abertas, False caso contrário
        """
        existing_orders = check_regular_orders(self.client, symbol)
        if existing_orders:
            self.logger.info(
                "Já existem %d ordens regulares abertas para %s",
                len(existing_orders),
                symbol,
            )
            return False
        return True

    def _check_existing_oco_orders(self, symbol: str) -> bool:
        """
        Verifica se existem ordens TP/SL (OCO) abertas para o símbolo.

        Args:
            symbol: Par de trading

        Returns:
            True se não há ordens OCO abertas, False caso contrário
        """
        trailing_orders = check_oco_orders(self.client, symbol)
        if trailing_orders:
            self.logger.info(
                "Já existem %d ordens TP/SL (OCO) para %s. Aguardando fechamento.",
                len(trailing_orders),
                symbol,
            )
            return False
        return True

    def _check_existing_trailing_stop_orders(self, symbol: str) -> bool:
        """
        Verifica se existem ordens de trailing stop abertas para o símbolo.

        Args:
            symbol: Par de trading

        Returns:
            True se não há ordens de trailing stop abertas, False caso contrário
        """
        trailing_orders = check_trailing_stop_orders(self.client, symbol)
        if trailing_orders:
            self.logger.info(
                "Já existem %d ordens de trailing stop para %s. Aguardando fechamento.",
                len(trailing_orders),
                symbol,
            )
            return False
        return True

    def _check_balance_for_symbol(self, symbol: str) -> bool:
        """
        Verifica se há saldo suficiente para colocar uma ordem.

        Args:
            symbol: Par de trading

        Returns:
            True se há saldo suficiente, False caso contrário
        """
        balance = self.client.get_balance()
        quote_currency = symbol.split("/")[1]
        available_balance = balance["total"].get(quote_currency, 0)

        if available_balance <= 10:  # Mínimo de 10 unidades da moeda base
            self.logger.warning(
                "Saldo insuficiente para %s: %s", quote_currency, available_balance
            )
            return False
        return True

    def validate_order_conditions(self, symbol: str, strategy_type: str = "regular") -> bool:
        """
        Valida se as condições estão adequadas para colocar uma ordem.

        Args:
            symbol: Par de trading
            strategy_type: Tipo de estratégia ('regular', 'oco', 'trailing', 'grid')

        Returns:
            True se condições estão OK, False caso contrário
        """
        try:
            if not self._validate_ticker(symbol):
                return False
            if not self._check_existing_regular_orders(symbol):
                return False
            if strategy_type == "oco" and not self._check_existing_oco_orders(symbol):
                return False
            if strategy_type == "trailing" and not self._check_existing_trailing_stop_orders(symbol):
                return False
            if strategy_type != "grid" and not self._check_balance_for_symbol(symbol):
                return False
            return True
        except Exception as exc:
            self.logger.error("Erro ao validar condições para %s: %s", symbol, str(exc))
            return False
