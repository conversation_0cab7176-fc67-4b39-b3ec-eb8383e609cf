"""
Script para monitorar Spot Grid Bots da OKX usando CCXT
Autor: Bluetuga
Data: 2025-06-18
"""

import os
import sys
import json
from datetime import datetime
from dotenv import load_dotenv
import ccxt
from typing import Dict, List, Optional
from utils.error_handler import <PERSON><PERSON>r<PERSON><PERSON><PERSON>
from utils.logger import TradingLogger


class OKXGridBotAPI:
    """Classe para interagir com a API da OKX e obter dados de Grid Bots"""

    def __init__(self):
        """Inicializa o cliente da API da OKX"""
        self.exchange = None
        self.logger = TradingLogger.get_logger(__name__)
        self.error_handler = ErrorHandler(logger_name=__name__, max_retries=3, retry_delay=2)
        self.load_credentials()
        self.initialize_exchange()

    def load_credentials(self):
        """Carrega credenciais do arquivo .env"""
        load_dotenv()

        self.api_key = os.getenv("live_okx_apiKey")
        self.secret_key = os.getenv("live_okx_secret")
        self.password = os.getenv("live_okx_password")

        if not all([self.api_key, self.secret_key, self.password]):
            error_msg = "Credenciais não encontradas. Verifique seu arquivo .env"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

    def initialize_exchange(self):
        """Inicializa a conexão com a OKX"""
        def init_exchange():
            return ccxt.myokx(
                {
                    "apiKey": self.api_key,
                    "secret": self.secret_key,
                    "password": self.password,
                    "sandbox": False,  # Mude para True se estiver usando sandbox
                    "enableRateLimit": True,
                }
            )

        result = self.error_handler.execute_with_retry(init_exchange, "Inicializar conexão com OKX")
        if result is None:
            error_msg = "Falha ao inicializar conexão com OKX após várias tentativas"
            self.logger.error(error_msg)
            raise Exception(error_msg)
        self.exchange = result

    def get_active_grid_bots(self) -> Optional[List[Dict]]:
        """Obtém lista de grid bots ativos"""
        def fetch_active_bots():
            response = self.exchange.privateGetTradingBotGridOrdersAlgoPending(
                {"algoOrdType": "grid"}
            )
            if response.get("code") == "0":
                return response.get("data", [])
            else:
                error_msg = f"Erro na API: {response.get('msg')}"
                self.logger.error(error_msg)
                return None

        result = self.error_handler.execute_with_retry(fetch_active_bots, "Obter grid bots ativos")
        if result is None:
            self.logger.warning("Falha ao obter grid bots ativos após várias tentativas")
        return result

    def get_grid_bot_details(self, algo_id: str) -> Optional[Dict]:
        """Obtém detalhes de um grid bot específico"""
        def fetch_bot_details():
            response = self.exchange.privateGetTradingBotGridOrdersAlgoDetails(
                {"algoId": algo_id, "algoOrdType": "grid"}
            )
            if response.get("code") == "0":
                data = response.get("data", [])
                return data[0] if data else None
            else:
                error_msg = f"Erro na API: {response.get('msg')}"
                self.logger.error(error_msg)
                return None

        result = self.error_handler.execute_with_retry(fetch_bot_details, f"Obter detalhes do grid bot {algo_id}")
        if result is None:
            self.logger.warning(f"Falha ao obter detalhes do grid bot {algo_id} após várias tentativas")
        return result

    def get_grid_bot_sub_orders(self, algo_id: str, limit: int = 10) -> Optional[List[Dict]]:
        """Obtém sub-ordens de um grid bot"""
        def fetch_sub_orders():
            response = self.exchange.privateGetTradingBotGridSubOrders(
                {
                    "algoId": algo_id,
                    "algoOrdType": "grid",
                    "type": "live",  # ou 'filled' para ordens executadas
                    "limit": str(limit),
                }
            )
            if response.get("code") == "0":
                return response.get("data", [])
            else:
                error_msg = f"Erro na API: {response.get('msg')}"
                self.logger.error(error_msg)
                return None

        result = self.error_handler.execute_with_retry(fetch_sub_orders, f"Obter sub-ordens do grid bot {algo_id}")
        if result is None:
            self.logger.warning(f"Falha ao obter sub-ordens do grid bot {algo_id} após várias tentativas")
        return result

    def get_grid_bot_history(self, limit: int = 10) -> Optional[List[Dict]]:
        """Obtém histórico de grid bots"""
        def fetch_history():
            response = self.exchange.privateGetTradingBotGridOrdersAlgoHistory(
                {"limit": str(limit), "algoOrdType": "grid"}
            )
            if response.get("code") == "0":
                return response.get("data", [])
            else:
                error_msg = f"Erro na API: {response.get('msg')}"
                self.logger.error(error_msg)
                return None

        result = self.error_handler.execute_with_retry(fetch_history, "Obter histórico de grid bots")
        if result is None:
            self.logger.warning("Falha ao obter histórico de grid bots após várias tentativas")
        return result


class OKXGridBotFormatter:
    """Classe para formatar e exibir dados de Grid Bots da OKX"""

    def calculate_roi(self, total_pnl: str, investment: str) -> str:
        """Calcula o ROI do grid bot"""
        try:
            pnl_val = float(total_pnl)
            inv_val = float(investment)
            if inv_val > 0:
                roi = (pnl_val / inv_val) * 100
                return f"{roi:+.2f}%"
            return "N/A"
        except (ValueError, TypeError, ZeroDivisionError):
            return "N/A"

    def format_currency(self, value: str, currency: str = "USDT") -> str:
        """Formata valores monetários"""
        try:
            num_value = float(value)
            return f"{num_value:,.2f} {currency}"
        except (ValueError, TypeError):
            return f"{value} {currency}"

    def format_percentage(self, value: str) -> str:
        """Formata percentuais"""
        try:
            num_value = float(value) * 100
            return f"{num_value:+.2f}%"
        except (ValueError, TypeError):
            return f"{value}%"

    def get_pnl_status_emoji(self, pnl: str) -> str:
        """Retorna emoji baseado no PnL"""
        try:
            pnl_val = float(pnl)
            if pnl_val > 0:
                return "🟢"
            elif pnl_val < 0:
                return "🔴"
            else:
                return "⚪"
        except (ValueError, TypeError):
            return "⚪"

    def display_grid_bot_info(self, bot_data: Dict, details: Dict = None):
        """Exibe informações de um grid bot"""
        total_pnl = bot_data.get("totalPnl", "0")
        pnl_emoji = self.get_pnl_status_emoji(total_pnl)

        print("\n" + "=" * 70)
        print(f"🤖 Grid Bot ID: {bot_data.get('algoId', 'N/A')}")
        print(f"📊 Símbolo: {bot_data.get('instId', 'N/A')}")
        print(f"🔄 Estado: {bot_data.get('state', 'N/A')}")
        print(f"{pnl_emoji} PnL Total: {self.format_currency(total_pnl)}")
        print(
            f"📈 PnL da Grade: {self.format_currency(bot_data.get('gridProfit', '0'))}"
        )
        print(
            f"💸 PnL Não Realizado: {self.format_currency(bot_data.get('floatProfit', '0'))}"
        )
        print(
            f"💵 Investimento: {self.format_currency(bot_data.get('investment', '0'))}"
        )
        print(
            f"📊 ROI: {self.calculate_roi(total_pnl, bot_data.get('investment', '0'))}"
        )

        if details:
            print(f"🔄 Arbitragens: {details.get('arbitrageNum', 'N/A')}")
            print(
                f"📊 Taxa Anual: {self.format_percentage(details.get('annualizedRate', '0'))}"
            )
            print(f"⏰ Criado em: {self.timestamp_to_date(details.get('cTime', '0'))}")
            print(f"🔧 Preço Inferior: {details.get('minPx', 'N/A')}")
            print(f"🔧 Preço Superior: {details.get('maxPx', 'N/A')}")
            print(f"🔧 Quantidade de Grids: {details.get('gridNum', 'N/A')}")

    def display_sub_orders_summary(self, sub_orders: List[Dict]):
        """Exibe resumo das sub-ordens"""
        if not sub_orders:
            print("📋 Nenhuma sub-ordem ativa")
            return

        buy_orders = [o for o in sub_orders if o.get("side") == "buy"]
        sell_orders = [o for o in sub_orders if o.get("side") == "sell"]

        print(f"📋 Sub-ordens ativas: {len(sub_orders)}")
        print(f"• Compra: {len(buy_orders)}")
        print(f"• Venda: {len(sell_orders)}")

    def timestamp_to_date(self, timestamp: str) -> str:
        """Converte timestamp para data legível"""
        try:
            ts = int(timestamp) / 1000  # OKX usa timestamp em milissegundos
            return datetime.fromtimestamp(ts).strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError):
            return "Data inválida"


class OKXGridBotMonitor:
    """Classe principal para monitorar Grid Bots da OKX"""

    def __init__(self):
        """Inicializa o monitor de grid bots"""
        self.api = OKXGridBotAPI()
        self.formatter = OKXGridBotFormatter()
        self.logger = TradingLogger.get_logger(__name__)
        self.error_handler = ErrorHandler(logger_name=__name__, max_retries=3, retry_delay=2)

    def monitor_active_bots(self):
        """Monitora todos os grid bots ativos"""
        self.logger.info("Buscando grid bots ativos...")

        active_bots = self.api.get_active_grid_bots()

        if not active_bots:
            self.logger.warning("Nenhum grid bot ativo encontrado")
            return

        self.logger.info(f"Encontrados {len(active_bots)} grid bot(s) ativo(s)")

        total_pnl = 0
        total_investment = 0

        for i, bot in enumerate(active_bots, 1):
            self.logger.info(f"Grid Bot {i}/{len(active_bots)}")

            # Obter detalhes do bot
            algo_id = bot.get("algoId")
            details = self.api.get_grid_bot_details(algo_id) if algo_id else None

            # Exibir informações principais
            self.formatter.display_grid_bot_info(bot, details)

            # Obter e exibir sub-ordens
            sub_orders = self.api.get_grid_bot_sub_orders(algo_id, 20) if algo_id else None
            if sub_orders is not None:
                self.formatter.display_sub_orders_summary(sub_orders)

            # Acumular totais para resumo
            try:
                total_pnl += float(bot.get("totalPnl", "0"))
                total_investment += float(bot.get("investment", "0"))
            except (ValueError, TypeError):
                pass

        # Exibir resumo geral
        if len(active_bots) > 1:
            self.logger.info("=" * 70)
            self.logger.info("RESUMO GERAL")
            self.logger.info("=" * 70)
            self.logger.info(f"Total de Bots: {len(active_bots)}")
            self.logger.info(f"PnL Total: {self.formatter.format_currency(str(total_pnl))}")
            self.logger.info(
                f"Investimento Total: {self.formatter.format_currency(str(total_investment))}"
            )
            self.logger.info(
                f"ROI Geral: {self.formatter.calculate_roi(str(total_pnl), str(total_investment))}"
            )

    def show_bot_history(self, limit: int = 5):
        """Mostra histórico de grid bots"""
        self.logger.info(f"Histórico dos últimos {limit} grid bots...")

        history = self.api.get_grid_bot_history(limit)

        if not history:
            self.logger.warning("Nenhum histórico encontrado")
            return

        self.logger.info(f"Encontrados {len(history)} bot(s) no histórico")

        for i, bot in enumerate(history, 1):
            self.logger.info(f"Bot Histórico {i}/{len(history)}")
            self.formatter.display_grid_bot_info(bot)

    def show_detailed_bot_info(self, algo_id: str):
        """Mostra informações detalhadas de um bot específico"""
        self.logger.info(f"Buscando detalhes do bot {algo_id}...")

        details = self.api.get_grid_bot_details(algo_id)
        if not details:
            self.logger.warning("Bot não encontrado")
            return

        self.formatter.display_grid_bot_info(details, details)

        # Sub-ordens detalhadas
        sub_orders = self.api.get_grid_bot_sub_orders(algo_id, 50)
        if sub_orders:
            self.logger.info(f"SUB-ORDENS ATIVAS ({len(sub_orders)}):")
            for i, order in enumerate(
                sub_orders[:10], 1
            ):  # Mostrar apenas as 10 primeiras
                side_emoji = "🟢" if order.get("side") == "buy" else "🔴"
                self.logger.info(
                    f"{i}. {side_emoji} {order.get('side', 'N/A').upper()}: "
                    f"{order.get('sz', '0')} @ {order.get('px', '0')}"
                )

            if len(sub_orders) > 10:
                self.logger.info(f"... e mais {len(sub_orders) - 10} ordens")

    def run_interactive_menu(self):
        """Executa menu interativo"""
        while True:
            print("\n" + "=" * 70)
            print("🤖 MONITOR DE GRID BOTS OKX")
            print("=" * 70)
            print("1. 📊 Monitorar bots ativos")
            print("2. 📚 Ver histórico de bots")
            print("3. 🔍 Buscar bot específico (detalhado)")
            print("4. 📤 Exportar dados para JSON")
            print("5. 🔄 Atualizar dados em tempo real")
            print("6. ❌ Sair")
            print("=" * 70)

            choice = input("Escolha uma opção (1-6): ").strip()

            if choice == "1":
                self.monitor_active_bots()
            elif choice == "2":
                limit = input(
                    "Quantos bots mostrar no histórico? (padrão: 5): "
                ).strip()
                limit = int(limit) if limit.isdigit() else 5
                self.show_bot_history(limit)
            elif choice == "3":
                algo_id = input("Digite o ID do grid bot: ").strip()
                if algo_id:
                    self.show_detailed_bot_info(algo_id)
            elif choice == "4":
                self.export_data_to_json()
            elif choice == "5":
                self.monitor_continuous()
            elif choice == "6":
                print("👋 Saindo...")
                break
            else:
                print("❌ Opção inválida")

            input("\nPressione Enter para continuar...")

    def monitor_continuous(self):
        """Monitora continuamente os bots"""
        import time

        self.logger.info("Monitoramento contínuo iniciado (Ctrl+C para parar)")
        self.logger.info("Atualizando a cada 30 segundos...")

        try:
            while True:
                self.logger.info(f"Atualização: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                self.monitor_active_bots()
                self.logger.info("Aguardando 30 segundos...")
                time.sleep(30)
        except KeyboardInterrupt:
            self.logger.info("Monitoramento contínuo interrompido")

    def export_data_to_json(self):
        """Exporta dados para arquivo JSON"""
        def export_data():
            self.logger.info("Exportando dados...")

            active_bots = self.api.get_active_grid_bots()
            history = self.api.get_grid_bot_history(20)

            # Obter detalhes completos dos bots ativos
            detailed_active_bots = []
            if active_bots:
                for bot in active_bots:
                    algo_id = bot.get("algoId")
                    if algo_id:
                        details = self.api.get_grid_bot_details(algo_id)
                        sub_orders = self.api.get_grid_bot_sub_orders(algo_id, 100)

                        bot_data = {
                            "basic_info": bot,
                            "details": details,
                            "sub_orders": sub_orders,
                        }
                        detailed_active_bots.append(bot_data)

            data = {
                "export_timestamp": datetime.now().isoformat(),
                "summary": {
                    "active_bots_count": len(active_bots) if active_bots else 0,
                    "history_count": len(history) if history else 0,
                },
                "active_bots": detailed_active_bots,
                "history": history or [],
            }

            filename = f"okx_grid_bots_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            with open(filename, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"Dados exportados para: {filename}")
            self.logger.info(f"Bots ativos: {len(detailed_active_bots)}")
            self.logger.info(f"Histórico: {len(history) if history else 0}")
            return True

        result = self.error_handler.execute_with_retry(export_data, "Exportar dados para JSON")
        if result is None:
            self.logger.warning("Falha ao exportar dados após várias tentativas")


def main():
    """Função principal"""
    logger = TradingLogger.get_logger(__name__)
    error_handler = ErrorHandler(logger_name=__name__, max_retries=3, retry_delay=2)
    
    def start_monitor():
        logger.info("Iniciando Monitor de Grid Bots OKX...")
        monitor = OKXGridBotMonitor()
        monitor.run_interactive_menu()
        return True

    try:
        result = error_handler.execute_with_retry(start_monitor, "Iniciar Monitor de Grid Bots OKX")
        if result is None:
            logger.error("Falha ao iniciar o monitor após várias tentativas")
            sys.exit(1)
    except KeyboardInterrupt:
        logger.info("Programa interrompido pelo usuário")
        sys.exit(0)


if __name__ == "__main__":
    main()
