import os
import re
from typing import Optional
from dotenv import load_dotenv
from telegram import Bo<PERSON>
from telegram.helpers import escape_markdown
from telegram.error import TelegramError

from utils.logger import TradingLogger

log = TradingLogger.get_logger(__name__)
load_dotenv()


class TelegramNotifier:
    _regex_pattern = re.compile(r"^\d{1,2}\/\d{1,2}\/\d{4}$")
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    async def create(cls) -> 'TelegramNotifier':
        """Factory method to create and initialize a TelegramNotifier instance asynchronously."""
        instance = cls()
        if not hasattr(instance, '_initialized') or not instance._initialized:
            await instance._initialize()
            instance._initialized = True
        return instance

    async def _initialize(self) -> None:
        """Initialize with Telegram configuration asynchronously."""
        self.chat_id = os.getenv("TELEGRAM_CHAT_ID")
        self.token = os.getenv("TELEGRAM_TOKEN")
        if not all([self.chat_id, self.token]):
            log.error("❌ Missing TELEGRAM_CHAT_ID or TELEGRAM_TOKEN")
            raise ValueError("Missing required Telegram configuration")
        if self.token is None:
            raise ValueError("Token cannot be None")
        # Configurar o bot com um pool de conexões maior e timeout ajustado
        from telegram.request import HTTPXRequest
        self.bot = Bot(token=self.token, request=HTTPXRequest(connection_pool_size=20, connect_timeout=30.0, read_timeout=30.0))

    async def send_message(self, message: str, bot_token: Optional[str] = None) -> bool:
        """
        Send a MarkdownV2-formatted message to the Telegram chat.

        Args:
            message: Markdown message to send
            bot_token: Telegram bot token (defaults to env token)

        Returns:
            bool: True if sent successfully, False otherwise
        """
        if not message or not isinstance(message, str):
            log.error("❌ Invalid or empty message")  # Fixed
            return False

        # Ensure the instance is initialized before accessing attributes
        if not hasattr(self, '_initialized') or not self._initialized:
            await self._initialize()
            self._initialized = True

        bot_token = bot_token or self.token
        if not bot_token:
            log.error("❌ Bot token not provided")  # Fixed
            return False

        if not self.chat_id:
            log.error("❌ Chat ID not provided")  # Fixed
            return False

        # Format message for MarkdownV2
        formatted_message = await self._to_markdown_v2(message)

        # Truncate to Telegram's max message length (4096 chars) while preserving formatting
        if len(formatted_message) >= 4096:
            log.warning("⚖️ Message truncated to 4096 characters")  # Fixed
            # Find a safe truncation point to avoid cutting formatting tags
            safe_cut = 4096
            for i in range(4096 - 100, 4096):
                if formatted_message[i] in [' ', '\n']:
                    safe_cut = i
                    break
            formatted_message = formatted_message[:safe_cut] + "\n... [mensagem truncada]"

        max_retries = 3
        for attempt in range(max_retries):
            try:
                bot = self.bot if bot_token == self.token else Bot(token=bot_token)
                # log.info(f"📤 Sending formatted message (length: {len(formatted_message)}): {formatted_message[:100]}...")  # Log for debugging
                await bot.send_message(
                    chat_id=str(self.chat_id),
                    text=formatted_message,
                    parse_mode="MarkdownV2",
                )
                # log.info(f"✅ Message sent to chat {self.chat_id}")  # Fixed (if uncommented)
                return True

            except TelegramError as e:
                log.error(f"❌ Failed to send message: {e}")  # Fixed
                if attempt == max_retries - 1:
                    return False
                log.info(f"Retrying... ({attempt+1}/{max_retries})")
            except (ConnectionError, TimeoutError) as e:
                log.error(f"❌ Network error sending message: {e}")  # Fixed
                if attempt == max_retries - 1:
                    return False
                log.info(f"Retrying... ({attempt+1}/{max_retries})")
            except Exception as e:  # pylint: disable=broad-except
                log.error(f"❌ Unexpected error: {e}")  # Fixed
                if attempt == max_retries - 1:
                    return False
                log.info(f"Retrying... ({attempt+1}/{max_retries})")
            # Aguarda antes de retry
            import asyncio
            await asyncio.sleep(2 ** attempt)
        return False

    async def _to_markdown_v2(self, message: str) -> str:
        """Format message for Telegram MarkdownV2 with robust entity handling asynchronously."""
        # Split message into parts to preserve formatting
        parts = []
        last_pos = 0
        # Match Markdown formatting to avoid escaping
        pattern = r"(\*[^*]*\*|_[^_]*_|`[^`]*`|```[\s\S]*?```|\[[^\]]*\]\([^\)]*\))"
        for match in re.finditer(pattern, message):
            start, end = match.span()
            # Escape non-formatted text
            non_formatted = message[last_pos:start]
            escaped_text = escape_markdown(non_formatted, version=2)
            parts.append(escaped_text)
            # Get the matched formatted text
            formatted_text = message[start:end]
            # Ensure closing tags for bold, italic, etc., and escape special chars inside if needed
            if formatted_text.startswith('*') and not formatted_text.endswith('*'):
                formatted_text = formatted_text.rstrip('*') + '*'
            elif formatted_text.startswith('_') and not formatted_text.endswith('_'):
                formatted_text = formatted_text.rstrip('_') + '_'
            elif formatted_text.startswith('`') and not formatted_text.endswith('`'):
                formatted_text = formatted_text.rstrip('`') + '`'
            elif formatted_text.startswith('```') and not formatted_text.endswith('```'):
                formatted_text = formatted_text.rstrip('```') + '```'
            parts.append(formatted_text)
            last_pos = end
        # Escape the remaining text
        final_text = message[last_pos:]
        escaped_final = escape_markdown(final_text, version=2)
        parts.append(escaped_final)
        return "".join(parts)

    # async def _to_markdown_v2(self, message: str) -> str:
    #     """Format message for Telegram MarkdownV2 with robust entity handling asynchronously."""
    #     # Split message into parts to preserve formatting
    #     parts = []
    #     last_pos = 0
    #     # Match Markdown formatting to avoid escaping
    #     pattern = r"(\*[^*]*\*|_[^_]*_|`[^`]*`|```[\s\S]*?```|\[[^\]]*\]\([^\)]*\))"
    #     for match in re.finditer(pattern, message):
    #         start, end = match.span()
    #         # Escape non-formatted text
    #         non_formatted = message[last_pos:start]
    #         escaped_text = escape_markdown(non_formatted, version=2)
    #         # Explicitly escape periods in non-formatted text
    #         escaped_text = escaped_text.replace(".", "\\.")
    #         parts.append(escaped_text)
    #         # Get the matched formatted text
    #         formatted_text = message[start:end]
    #         # Ensure closing tags for bold, italic, etc., and escape special chars inside if needed
    #         if formatted_text.startswith('*') and not formatted_text.endswith('*'):
    #             formatted_text = formatted_text.rstrip('*') + '*'
    #         elif formatted_text.startswith('_') and not formatted_text.endswith('_'):
    #             formatted_text = formatted_text.rstrip('_') + '_'
    #         elif formatted_text.startswith('`') and not formatted_text.endswith('`'):
    #             formatted_text = formatted_text.rstrip('`') + '`'
    #         elif formatted_text.startswith('```') and not formatted_text.endswith('```'):
    #             formatted_text = formatted_text.rstrip('```') + '```'
    #         # Escape periods in formatted text (e.g., inside bold or inline code)
    #         formatted_text = formatted_text.replace(".", "\\.")
    #         parts.append(formatted_text)
    #         last_pos = end
    #     # Escape the remaining text
    #     final_text = message[last_pos:]
    #     escaped_final = escape_markdown(final_text, version=2)
    #     escaped_final = escaped_final.replace(".", "\\.")
    #     parts.append(escaped_final)
    #     return "".join(parts)

if __name__ == "__main__":
    import asyncio

    print("\n=== TESTE DO TELEGRAM NOTIFIER ===")

    async def run_tests():
        try:
            notifier = await TelegramNotifier.create()

            # Example 1: Plain text message
            message = f"Test message at {os.times().elapsed:.2f}s"
            print(f"Sending plain text: '{message}'")
            result = await notifier.send_message(message)
            print(f"Result: {'Success' if result else 'Failure'}\n")

            # Example 2: Formatted MarkdownV2 message
            message = "Test with *bold*, _italic_, [link](https://example.com), and `inline code`"
            print(f"Sending formatted message: '{message}'")
            result = await notifier.send_message(message)
            print(f"Result: {'Success' if result else 'Failure'}\n")

            # Example 3: Message with code block
            message = (
                "Code example:\n```\ndef hello():\n    print('Hello, world!')\n```"
            )
            print(f"Sending code block message: '{message}'")
            result = await notifier.send_message(message)
            print(f"Result: {'Success' if result else 'Failure'}\n")

            # Example 4: Message with special characters
            message = "Special chars: _*[]()~`>#+-=|{}.! must be escaped"
            print(f"Sending message with special chars: '{message}'")
            result = await notifier.send_message(message)
            print(f"Result: {'Success' if result else 'Failure'}\n")

        except ValueError as e:
            print(f"Error: {e}")
            log.error(f"❌ Initialization failed: {e}")

    # Run the async tests
    asyncio.run(run_tests())
