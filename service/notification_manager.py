"""
Classe para gerenciar notificações sonoras e via Telegram.
"""

from typing import Optional
from utils.logger import TradingLogger
from service.service_alerts import SoundNotifications
from service.service_telegram import TelegramNotifier
from trading_config import BotConfig

class NotificationManager:
    """Classe para gerenciar notificações do bot de trading."""

    def __init__(self, config: BotConfig):
        self.config = config
        self.logger = TradingLogger.get_logger(__name__)
        self.sound_alert = self._initialize_sound_notifications()
        self.notifier = self._initialize_telegram_notifier()

    def _initialize_sound_notifications(self) -> Optional[SoundNotifications]:
        """Inicializa notificações sonoras se habilitadas."""
        if self.config.ENABLE_SOUND_NOTIFICATIONS:
            try:
                return SoundNotifications()
            except Exception as exc:
                self.logger.warning(
                    "Falha ao inicializar notificações sonoras: %s", str(exc)
                )
        return None

    def _initialize_telegram_notifier(self) -> Optional[TelegramNotifier]:
        """Inicializa notificador Telegram se habilitado."""
        if self.config.ENABLE_TELEGRAM_NOTIFICATIONS:
            try:
                return TelegramNotifier()
            except Exception as exc:
                self.logger.warning(
                    "Falha ao inicializar notificador Telegram: %s", str(exc)
                )
        return None

    async def play_alert(self, alert_type: str, volume: float = 0.5) -> None:
        """Toca som de alerta se disponível."""
        if self.sound_alert:
            try:
                self.sound_alert.play_notification(alert_type, volume=volume)
            except Exception as exc:
                self.logger.warning("Falha ao reproduzir alerta: %s", str(exc))

    async def send_telegram_notification(self, message: str) -> None:
        """Envia notificação Telegram se disponível."""
        if self.notifier:
            try:
                await self.notifier.send_message(message)
            except Exception as exc:
                self.logger.warning(
                    "Falha ao enviar notificação Telegram: %s", str(exc)
                )
