import os
import warnings
from typing import Dict, Optional
from pathlib import Path
import pygame
import threading

from utils.logger import TradingLogger

log = TradingLogger.get_logger(__name__)

warnings.filterwarnings("ignore", message="pkg_resources is deprecated as an API")

SOUNDS_DIR = Path(__file__).parent / "sounds"

SOUND_FILES: Dict[str, str] = {
    "start": "000_start.wav",
    "end": "end.wav",
    "success": "order_created.wav",
    "exit": "end.wav",
    "place_order": "clinking_coins.wav",
    "trade_executed": "clinking_coins.wav",
    "buy": "money_001.wav",
    "sell": "money_002.wav",
    "win": "000_win.wav",
    "loose": "000_loose.wav",
    "waiting": "waiting.wav",
    "alert": "alert_101.wav",
    "info": "info_003.wav",
    "error": "000_error.wav",
    "buy_signal": "notif_001.wav",
    "sell_signal": "notif_002.wav",
}


class SoundNotifications:
    """
    Singleton class to play sound notifications.

    Attributes
    ----------
    _instance : SoundNotifications
        The unique instance of the class.
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:  # Double-checked locking
                    cls._instance = super().__new__(cls)
                    cls._instance._initialize()
        return cls._instance

    def _initialize(self) -> None:
        """Initialize the mixer and validate sound files."""
        self.sounds: Dict[str, str] = {
            k: str(SOUNDS_DIR / v) for k, v in SOUND_FILES.items()
        }
        SOUNDS_DIR.mkdir(exist_ok=True)
        self._init_mixer()
        self._validate_sounds()

    def _init_mixer(self) -> None:
        """Initialize pygame mixer with error handling."""
        try:
            pygame.mixer.init(frequency=44100, size=-16, channels=2, buffer=512)
            # log.debug("✅ Pygame mixer initialized")
        except pygame.error as e:
            log.error(f"❌ Failed to initialize pygame.mixer: {e}")
            log.warning("⚠️ Sound notifications will be unavailable")

    def _validate_sounds(self) -> None:
        """Check if sound files exist and are in supported formats."""
        valid_extensions = {".wav", ".mp3", ".ogg"}
        for key, path in self.sounds.items():
            path_obj = Path(path)
            if not path_obj.exists():
                log.warning(f"⚠️ Sound file missing: {key} ({path})")
            elif path_obj.suffix.lower() not in valid_extensions:
                log.warning(f"⚠️ Unsupported file format: {key} ({path})")

    def play_notification(self, sound_key: str, volume: float = 1.0) -> bool:
        """Play a sound notification by key."""
        if not pygame.mixer.get_init():
            log.error("❌ Pygame mixer not initialized")
            return False

        if not sound_key or sound_key not in self.sounds:
            log.error(f"❌ Invalid or undefined sound key: {sound_key}")
            return False

        sound_file = self.sounds[sound_key]
        if not os.path.exists(sound_file):
            log.error(f"❌ Sound file not found: {sound_file}")
            return False

        try:
            sound = pygame.mixer.Sound(sound_file)
            sound.set_volume(max(0.0, min(1.0, volume)))
            sound.play()
            # log.debug(f"✅ Playing sound: {sound_key}")
            return True
        except pygame.error as e:
            log.error(f"❌ Error playing sound {sound_key}: {e}")
            return False

    def is_busy(self) -> bool:
        """Check if a sound is currently playing."""
        return pygame.mixer.get_init() and pygame.mixer.get_busy()

    def wait_until_done(self, timeout: Optional[float] = None) -> None:
        """Wait until no sounds are playing, with optional timeout."""
        if not pygame.mixer.get_init():
            return

        import time

        start_time = time.time()
        while self.is_busy():
            if timeout is not None and (time.time() - start_time) >= timeout:
                log.debug("⏰ Wait timeout reached")
                break
            time.sleep(0.1)

    def stop(self) -> None:
        """Stop all playing sounds."""
        if pygame.mixer.get_init():
            pygame.mixer.stop()
            log.debug("✅ All sounds stopped")

    def close(self) -> None:
        """Stop sounds and quit the mixer."""
        if pygame.mixer.get_init():
            self.stop()
            pygame.mixer.quit()
            log.debug("✅ Pygame mixer closed")


if __name__ == "__main__":
    notifier = SoundNotifications()

    print("Playing 'waiting' sound...")
    notifier.play_notification("waiting")
    notifier.wait_until_done()

    print("Playing 'info' sound at half volume...")
    notifier.play_notification("info", volume=0.5)
    notifier.wait_until_done()

    another_notifier = SoundNotifications()
    print(f"Same object? {notifier is another_notifier}")

    notifier.close()
    print("Test completed")
