from tqdm import tqdm
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading
import json
import os
import ccxt
import pandas as pd
from dotenv import load_dotenv
from datetime import datetime
import time
from typing import Dict, List, Optional
from pathlib import Path
import sys
from utils.logger import TradingLogger

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(BASE_DIR))

logger = TradingLogger.get_logger(__name__)

load_dotenv()

CONFIG = {
    'TIMEFRAME': ["15m", "1h"],
    'TOTAL_CANDLES': 70000,
    'LIMIT_PER_REQUEST': 200,
    'DATA_DIR': Path('data'),
    'RATE_LIMIT_BUFFER': 0.1,
    'DYNAMIC_RATE_LIMIT': True,
    'MAX_RETRIES': 3,
    'RETRY_DELAY': 2
}

def load_symbols_from_config():
    """Carrega os símbolos de trading do arquivo parameters.json"""
    try:
        with open("parameters.json", "r", encoding="utf-8") as f:
            config = json.load(f)
            symbols = config.get("general", {}).get("TRADING_SYMBOLS", [])
            if not symbols:
                logger.warning("Nenhum símbolo encontrado em parameters.json, usando lista padrão")
                return ["ETH/USDC", "BTC/USDC", "CRO/USDC", "SOL/USDC"]
            logger.info(f"Carregados {len(symbols)} símbolos de parameters.json")
            return symbols
    except Exception as e:
        logger.error(f"Erro ao carregar symbols de parameters.json: {e}")
        logger.warning("Usando lista padrão de símbolos")
        return ["ETH/USDC", "BTC/USDC", "CRO/USDC", "SOL/USDC"]

# Carregar os símbolos ao iniciar
CONFIG['SYMBOLS'] = load_symbols_from_config()

CONFIG['DATA_DIR'].mkdir(exist_ok=True)

class OKXDataFetcher:
    """Handles connection to OKX exchange and data retrieval."""
    
    def __init__(self):
        self.exchange = self._connect_okx()
        
    def _connect_okx(self) -> ccxt.Exchange:
        """Connects to OKX exchange with improved error handling."""
        required_vars = ['live_okx_apiKey', 'live_okx_secret', 'live_okx_password']
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if missing_vars:
            raise ValueError(f"Missing environment variables: {missing_vars}")
        
        try:
            exchange = ccxt.myokx({
                'apiKey': os.getenv('live_okx_apiKey'),
                'secret': os.getenv('live_okx_secret'),
                'password': os.getenv('live_okx_password'),
                'enableRateLimit': True,
                'sandbox': False,
                'timeout': 30000,
            })
            exchange.load_markets()
            logger.info("Connected to OKX successfully")
            return exchange
        except Exception as e:
            logger.error(f"Error connecting to OKX: {e}")
            raise
    
    def validate_symbol(self, symbol: str) -> bool:
        """Validates if the symbol exists on the exchange and is tradable."""
        try:
            return symbol in self.exchange.markets and self.exchange.markets[symbol]['active']
        except Exception as e:
            logger.warning(f"Failed to validate symbol {symbol}: {e}")
            return False

    def fetch_data_with_retry(self, symbol: str, timeframe: str, since: Optional[int] = None, limit: int = 300) -> List[List]:
        """Fetches OHLCV data with automatic retry on error, improved error handling."""
        for attempt in range(CONFIG['MAX_RETRIES']):
            if not self.validate_symbol(symbol):
                logger.error(f"Symbol {symbol} is not tradable, skipping.")
                return []
            try:
                if since:
                    data = self.exchange.fetch_ohlcv(symbol, timeframe, since=since, limit=limit)
                else:
                    data = self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
                return data
            except ccxt.BaseError as e:
                error_message = self.exchange.last_http_response if hasattr(self.exchange, 'last_http_response') else str(e)
                logger.error(f"API error: {error_message}")
                if hasattr(e, 'code') and e.code == 'rate_limit_exceeded':
                    # Implement dynamic rate limit handling
                    rate_limits = self.exchange.getratelimit_headers()
                    if rate_limits:
                        reset_time = max(rate_limits['x-ratelimit-reset'])
                        current_time = time.time()
                        wait_time = max(0, reset_time - current_time + 1.5)
                        logger.warning(f"Rate limit exceeded. Waiting {wait_time}s for reset...")
                        time.sleep(wait_time)
                else:
                    wait_time = CONFIG['RETRY_DELAY'] * (2 ** attempt)
                    logger.warning(f"Unhandled error: {e}, retrying in {wait_time}s...")
                    time.sleep(wait_time)
            except (ccxt.NetworkError, ccxt.ExchangeNotAvailable) as e:
                wait_time = CONFIG['RETRY_DELAY'] * (attempt + 1)
                logger.warning(f"Network error (attempt {attempt + 1}): {e}. Retrying in {wait_time}s...")
                time.sleep(wait_time)
            except Exception as e:
                logger.error(f"Unexpected error on attempt {attempt + 1}: {e}")
                if attempt == CONFIG['MAX_RETRIES'] - 1:
                    raise
                time.sleep(CONFIG['RETRY_DELAY'])
        return []

class DataProcessor:
    """Handles data processing and storage."""
    
    @staticmethod
    def timeframe_to_milliseconds(timeframe: str) -> int:
        """Converts timeframe to milliseconds with validation."""
        if not timeframe or len(timeframe) < 2:
            raise ValueError(f"Invalid timeframe: {timeframe}")
        
        try:
            unit = timeframe[-1].lower()
            number = int(timeframe[:-1])
        except ValueError:
            raise ValueError(f"Invalid timeframe format: {timeframe}")

        multipliers = {
            "s": 1000,
            "m": 60 * 1000,
            "h": 60 * 60 * 1000,
            "d": 24 * 60 * 60 * 1000,
            "w": 7 * 24 * 60 * 60 * 1000,
        }

        if unit not in multipliers:
            raise ValueError(f"Unsupported time unit: {unit}")
        
        return number * multipliers[unit]
    
    @staticmethod
    def save_to_csv(df: pd.DataFrame, symbol: str, timeframe: str) -> bool:
        """Saves DataFrame to CSV with optimized duplicate handling."""
        try:
            filename = f"data_{symbol.replace('/', '_').replace('-', '_')}_{timeframe}.csv"
            filepath = CONFIG['DATA_DIR'] / filename
            
            if filepath.exists():
                logger.info(f"Existing file found: {filepath}")
                existing_df = pd.read_csv(filepath)
                existing_df['timestamp'] = pd.to_datetime(existing_df['timestamp'])
                
                combined_df = pd.concat([existing_df, df], ignore_index=True)
                combined_df = combined_df.drop_duplicates(subset='timestamp', keep='last')
                combined_df = combined_df.sort_values('timestamp').reset_index(drop=True)
                
                if len(combined_df) > CONFIG['TOTAL_CANDLES']:
                    combined_df = combined_df.tail(CONFIG['TOTAL_CANDLES'])
                
                combined_df.to_csv(filepath, index=False)
                logger.info(f"File updated: {len(combined_df)} total records")
            else:
                df.to_csv(filepath, index=False)
                logger.info(f"New file created: {len(df)} records")
            
            return True
        except Exception as e:
            logger.error(f"Error saving CSV: {e}")
            return False
    
    @staticmethod
    def process_data(all_data: List[List], total_candles: int) -> pd.DataFrame:
        """Processes raw data into a cleaned DataFrame."""
        if not all_data:
            logger.warning("No data was collected")
            return pd.DataFrame()
        
        df = pd.DataFrame(
            all_data, 
            columns=["timestamp", "open", "high", "low", "close", "volume"]
        )
        
        df = df.drop_duplicates(subset="timestamp").sort_values("timestamp")
        df = df.tail(total_candles).reset_index(drop=True)
        df["timestamp"] = pd.to_datetime(df["timestamp"], unit="ms")
        
        if df.isnull().any().any():
            logger.warning("Data contains null values")
            df = df.dropna()
        
        logger.info(f"✅ Collection completed: {len(df)} candles")
        logger.info(f"📅 Period: {df['timestamp'].min()} until {df['timestamp'].max()}")
        
        return df

class DataCollectionManager:
    """Manages data collection for multiple symbols and timeframes."""
    
    def __init__(self):
        self.fetcher = OKXDataFetcher()
        self.results = {}
    
    def fetch_data_pagination(self, symbol: str, timeframe: str, total_candles: int = None, save_csv: bool = False) -> pd.DataFrame:
        """Fetches OHLCV data with optimized pagination."""
        if total_candles is None:
            total_candles = CONFIG['TOTAL_CANDLES']
        
        try:
            if not self.fetcher.validate_symbol(symbol):
                logger.error(f"Invalid symbol: {symbol}")
                return pd.DataFrame()
            
            timeframe_ms = DataProcessor.timeframe_to_milliseconds(timeframe)
        except Exception as e:
            logger.error(f"Initialization error: {e}")
            return pd.DataFrame()

        limit_per_request = CONFIG['LIMIT_PER_REQUEST']
        num_requests = (total_candles + limit_per_request - 1) // limit_per_request
        
        all_data = []
        seen_timestamps = set()
        since_timestamp = None
        rate_limit_delay = (self.fetcher.exchange.rateLimit / 1000) + CONFIG['RATE_LIMIT_BUFFER']
        
        try:
            with tqdm(
                total=total_candles, 
                desc=f"\033[94m{symbol} {timeframe}\033[0m",
                bar_format="{l_bar}\033[94m{bar}\033[0m| {n_fmt}/{total_fmt} [{elapsed}<{remaining}]",
                unit="candles"
            ) as pbar:
                
                for request_num in range(num_requests):
                    candles_remaining = total_candles - len(all_data)
                    if candles_remaining <= 0:
                        break
                        
                    current_limit = min(limit_per_request, candles_remaining)
                    
                    data = self.fetcher.fetch_data_with_retry(
                        symbol, timeframe, since_timestamp, current_limit
                    )
                    
                    if not data:
                        logger.warning(f"No data returned in request {request_num + 1}")
                        break
                    
                    new_data = []
                    for candle in data:
                        timestamp = candle[0]
                        if timestamp not in seen_timestamps:
                            new_data.append(candle)
                            seen_timestamps.add(timestamp)
                    
                    if not new_data:
                        logger.info("No more new data available in this batch")
                        if len(all_data) >= total_candles:
                            break
                        # Adjust since_timestamp to go further back in case no new data was found
                        if since_timestamp is None:
                            since_timestamp = int(time.time() * 1000) - (timeframe_ms * current_limit * (request_num + 1))
                        else:
                            since_timestamp = since_timestamp - (timeframe_ms * current_limit)
                        continue
                    
                    all_data.extend(new_data)
                    pbar.update(len(new_data))
                    
                    if request_num == 0:
                        oldest_timestamp = min(candle[0] for candle in new_data)
                        since_timestamp = oldest_timestamp - (timeframe_ms * current_limit)
                    else:
                        oldest_timestamp = min(candle[0] for candle in new_data)
                        since_timestamp = oldest_timestamp - (timeframe_ms * current_limit)
                    
                    if CONFIG['DYNAMIC_RATE_LIMIT']:
                        # Ajuste dinâmico do delay com base no feedback da API, se disponível
                        last_request_headers = getattr(self.fetcher.exchange, 'last_headers', {})
                        remaining_requests = last_request_headers.get('x-ratelimit-remaining', None)
                        reset_time = last_request_headers.get('x-ratelimit-reset', None)
                        if remaining_requests is not None and reset_time is not None:
                            if int(remaining_requests) < 5:
                                current_time = time.time()
                                wait_time = max(0, int(reset_time) - current_time)
                                logger.info(f"Rate limit quase atingido. Aguardando {wait_time}s até o reset.")
                                time.sleep(wait_time + 0.1)
                            else:
                                time.sleep(rate_limit_delay)
                        else:
                            time.sleep(rate_limit_delay)
                    else:
                        time.sleep(rate_limit_delay)
            
            df = DataProcessor.process_data(all_data, total_candles)
            if save_csv and not df.empty:
                DataProcessor.save_to_csv(df, symbol, timeframe)
            
            return df
            
        except KeyboardInterrupt:
            logger.info("Interrupted by user")
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error during collection: {e}")
            return pd.DataFrame()
    
    def collect_data(self):
        """Collects data for all configured symbols and timeframes using parallel processing."""
        logger.info(f"Starting data collection for {len(CONFIG['SYMBOLS'])} symbols")
        start_time = time.time()
        
        # Use ThreadPoolExecutor to parallelize data collection for different symbols, limitando workers para evitar rate limits
        max_workers = min(len(CONFIG['SYMBOLS']), 2)  # Limitar a 2 workers para evitar sobrecarga na API
        with ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="SymbolFetcher") as executor:
            # Dictionary to store futures for each symbol-timeframe pair
            futures = {}
            for symbol in CONFIG['SYMBOLS']:
                self.results[symbol] = {}
                for timeframe in CONFIG['TIMEFRAME']:
                    # Submit task for each symbol-timeframe pair without excessive logging
                    future = executor.submit(
                        self.fetch_data_pagination,
                        symbol, timeframe, CONFIG['TOTAL_CANDLES'], save_csv=True
                    )
                    futures[future] = (symbol, timeframe)
            
            # Use tqdm to show overall progress across all tasks
            with tqdm(total=len(futures), desc="Overall Progress", unit="tasks") as pbar:
                for future in as_completed(futures):
                    symbol, timeframe = futures[future]
                    try:
                        df_data = future.result()
                        if not df_data.empty:
                            self.results[symbol][timeframe] = {
                                'success': True,
                                'candles_count': len(df_data),
                                'period_start': str(df_data['timestamp'].min()),
                                'period_end': str(df_data['timestamp'].max())
                            }
                        else:
                            self.results[symbol][timeframe] = {'success': False}
                    except Exception as e:
                        logger.error(f"Error processing {symbol} {timeframe}: {e}")
                        self.results[symbol][timeframe] = {'success': False, 'error': str(e)}
                    finally:
                        pbar.update(1)
        
        total_time = time.time() - start_time
        logger.info("FINAL REPORT")
        logger.info(f"Total time: {total_time:.2f} seconds")
        
        report_file = CONFIG['DATA_DIR'] / f"collection_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        logger.info(f"Report saved to: {report_file}")

def main():
    """Main function to initiate data collection."""
    manager = DataCollectionManager()
    manager.collect_data()

if __name__ == "__main__":
    main()
