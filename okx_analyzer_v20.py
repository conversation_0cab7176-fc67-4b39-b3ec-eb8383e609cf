import ccxt
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import talib
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass
from enum import Enum
import os
from dotenv import load_dotenv
import functools

load_dotenv()
import warnings

warnings.filterwarnings("ignore")

# Configurações de estilo para os gráficos
plt.style.use("dark_background")
sns.set_palette("husl")


class OrderType(Enum):
    BUY = "buy"
    SELL = "sell"


class OrderStatus(Enum):
    OPEN = "open"
    CLOSED = "closed"
    CANCELED = "canceled"


@dataclass
class AnalysisResult:
    """Estrutura para armazenar resultados da análise."""

    total_orders: int
    win_rate: float
    profit_loss: float
    best_performer: str
    worst_performer: str
    risk_score: float
    recommendations: List[str]


class TradingLogger:
    """Logger personalizado para trading."""

    @staticmethod
    def get_logger(name: str) -> logging.Logger:
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger


class BotConfig:
    """Configurações de trading."""

    def __init__(self):
        # self.PREFIX = "demo"
        # self.SANDBOX_MODE = True  # Mude para False em produção
        self.PREFIX = "live"
        self.SANDBOX_MODE = False  # Mude para False em produção
        self.DEFAULT_TIMEFRAME = "1h"
        self.ANALYSIS_DAYS = 30


class ConfigurationError(Exception):
    """Exceção personalizada para erros de configuração."""

    pass


class OKXClient:
    """Cliente OKX otimizado com padrão Singleton para conexão com a exchange."""

    _instance = None
    _cache_size = 128  # Tamanho máximo do cache LRU

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if getattr(self, "_initialized", False):
            return
        self._initialized = True
        self.logger = TradingLogger.get_logger(__name__)
        self.config = BotConfig()
        self._initialize_exchange()

    def _get_credentials(self, prefix: str) -> Dict[str, str]:
        credentials = {
            "api_key": os.getenv(f"{prefix}_okx_apiKey"),
            "secret": os.getenv(f"{prefix}_okx_secret"),
            "password": os.getenv(f"{prefix}_okx_password"),
        }
        if not all(credentials.values()):
            self.logger.error("Credenciais incompletas para o prefixo '%s'", prefix)
            raise ConfigurationError(
                "Credenciais incompletas para o prefixo '%s'." % prefix
            )
        return credentials

    def _initialize_exchange(self) -> None:
        try:
            credentials = self._get_credentials(self.config.PREFIX)
            self.exchange = ccxt.myokx(
                {
                    "apiKey": credentials["api_key"],
                    "secret": credentials["secret"],
                    "password": credentials["password"],
                    "enableRateLimit": True,
                    "options": {"defaultType": "spot"},
                }
            )
            self.exchange.set_sandbox_mode(self.config.SANDBOX_MODE)
            self._log_initialization_status()
        except Exception as exc:
            self.logger.error("Erro ao inicializar a exchange: %s", exc)
            raise ConfigurationError(
                "Falha ao inicializar a exchange: %s" % exc
            ) from exc

    def _log_initialization_status(self) -> None:
        """Log do status de inicialização."""
        mode = "SANDBOX" if self.config.SANDBOX_MODE else "PRODUÇÃO"
        self.logger.info("Cliente OKX inicializado em modo %s", mode)

    @functools.lru_cache(maxsize=128)
    def get_balance(self) -> Dict:
        """Obtém saldo da conta."""
        try:
            return self.exchange.fetch_balance()
        except Exception as exc:
            self.logger.error("Erro ao obter saldo: %s", exc)
            return {}

    @functools.lru_cache(maxsize=128)
    def get_open_orders(self, symbol: Optional[str] = None) -> List[Dict]:
        """Obtém ordens abertas."""
        try:
            return self.exchange.fetch_open_orders(symbol)
        except Exception as exc:
            self.logger.error("Erro ao obter ordens abertas: %s", exc)
            return []

    @functools.lru_cache(maxsize=128)
    def get_closed_orders(
        self, symbol: Optional[str] = None, limit: int = 100
    ) -> List[Dict]:
        """Obtém ordens fechadas."""
        try:
            return self.exchange.fetch_closed_orders(symbol, limit=limit)
        except Exception as exc:
            self.logger.error("Erro ao obter ordens fechadas: %s", exc)
            return []

    @functools.lru_cache(maxsize=128)
    def get_trades(self, symbol: Optional[str] = None, limit: int = 100) -> List[Dict]:
        """Obtém histórico de trades."""
        try:
            return self.exchange.fetch_my_trades(symbol, limit=limit)
        except Exception as exc:
            self.logger.error("Erro ao obter trades: %s", exc)
            return []

    @functools.lru_cache(maxsize=128)
    def get_ohlcv(self, symbol: str, timeframe: str = "1h", limit: int = 100) -> List:
        """Obtém dados OHLCV."""
        try:
            return self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
        except Exception as exc:
            self.logger.error("Erro ao obter dados OHLCV: %s", exc)
            return []

    def has_market(self, symbol: str) -> bool:
        """Verifica se um mercado/símbolo existe na exchange."""
        try:
            markets = self.exchange.load_markets()
            return symbol in markets
        except Exception as exc:
            self.logger.error("Erro ao verificar mercado: %s", exc)
            return False


class OKXPortfolioAnalyzer:
    """Analisador de portfolio OKX com funcionalidades avançadas."""

    def __init__(self):
        self.client = OKXClient()
        self.logger = TradingLogger.get_logger(__name__)
        self.analysis_data = {}
        self.recommendations = []

    def collect_data(self) -> Dict:
        """Coleta todos os dados necessários da exchange."""
        self.logger.info("Iniciando coleta de dados...")

        data = {
            "balance": self.client.get_balance(),
            "open_orders": self.client.get_open_orders(),
            "closed_orders": self.client.get_closed_orders(limit=100),
            "trades": self.client.get_trades(limit=100),
        }

        self.analysis_data = data
        self.logger.info(
            "Dados coletados: %d trades, %d ordens",
            len(data["trades"]),
            len(data["closed_orders"]),
        )
        return data

    def analyze_trades_performance(self) -> Dict:
        """Analisa performance dos trades."""
        trades = self.analysis_data.get("trades", [])
        if not trades:
            return {}

        df_trades = pd.DataFrame(trades)

        # Calcular métricas básicas
        total_trades = len(df_trades)
        profitable_trades = len(df_trades[df_trades["cost"] > 0])
        win_rate = (profitable_trades / total_trades) * 100 if total_trades > 0 else 0

        # Análise por símbolo
        symbol_performance = (
            df_trades.groupby("symbol")
            .agg({"cost": ["sum", "count", "mean"], "amount": "sum"})
            .round(4)
        )

        # Análise temporal
        df_trades["datetime"] = pd.to_datetime(df_trades["timestamp"], unit="ms")
        df_trades["date"] = df_trades["datetime"].dt.date
        daily_pnl = df_trades.groupby("date")["cost"].sum()

        return {
            "total_trades": total_trades,
            "win_rate": win_rate,
            "symbol_performance": symbol_performance,
            "daily_pnl": daily_pnl,
            "best_day": daily_pnl.idxmax() if len(daily_pnl) > 0 else None,
            "worst_day": daily_pnl.idxmin() if len(daily_pnl) > 0 else None,
        }

    def analyze_order_patterns(self) -> Dict:
        """Analisa padrões de ordens."""
        orders = self.analysis_data.get("closed_orders", [])
        if not orders:
            return {}

        df_orders = pd.DataFrame(orders)

        # Análise de tipos de ordem
        order_types = df_orders["side"].value_counts()

        # Análise de execução
        filled_orders = df_orders[df_orders["status"] == "closed"]
        canceled_orders = df_orders[df_orders["status"] == "canceled"]

        execution_rate = (
            len(filled_orders) / len(df_orders) * 100 if len(df_orders) > 0 else 0
        )

        # Análise por símbolo
        symbol_orders = (
            df_orders.groupby("symbol")
            .agg({"amount": ["sum", "count"], "cost": "sum"})
            .round(4)
        )

        return {
            "total_orders": len(df_orders),
            "execution_rate": execution_rate,
            "order_types": order_types.to_dict(),
            "symbol_orders": symbol_orders,
            "canceled_orders": len(canceled_orders),
        }

    def analyze_risk_metrics(self) -> Dict:
        """Calcula métricas de risco."""
        trades = self.analysis_data.get("trades", [])
        if not trades:
            return {}

        df_trades = pd.DataFrame(trades)
        df_trades["pnl"] = df_trades["cost"]  # Assumindo cost como P&L

        # Métricas de risco
        returns = df_trades["pnl"]

        # Sharpe Ratio (simplificado)
        mean_return = returns.mean()
        std_return = returns.std()
        sharpe_ratio = mean_return / std_return if std_return != 0 else 0

        # Maximum Drawdown
        cumulative_returns = returns.cumsum()
        peak = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - peak) / peak
        max_drawdown = drawdown.min()

        # VaR (Value at Risk) - 95%
        var_95 = np.percentile(returns, 5)

        # Profit Factor
        gross_profit = returns[returns > 0].sum()
        gross_loss = abs(returns[returns < 0].sum())
        profit_factor = gross_profit / gross_loss if gross_loss != 0 else float("inf")

        return {
            "sharpe_ratio": round(sharpe_ratio, 4),
            "max_drawdown": round(max_drawdown * 100, 2),
            "var_95": round(var_95, 4),
            "profit_factor": round(profit_factor, 4),
            "volatility": round(std_return, 4),
        }

    def analyze_portfolio_composition(self) -> Dict:
        """Analisa composição do portfolio."""
        balance = self.analysis_data.get("balance", {})
        if not balance:
            return {}

        total_balance = balance.get("total", {})
        free_balance = balance.get("free", {})
        used_balance = balance.get("used", {})

        # Calcular diversificação
        assets = {k: v for k, v in total_balance.items() if v > 0}
        total_value = sum(assets.values())

        # Concentração de risco
        asset_weights = {k: (v / total_value) * 100 for k, v in assets.items()}
        concentration_risk = max(asset_weights.values()) if asset_weights else 0

        return {
            "total_assets": len(assets),
            "asset_weights": asset_weights,
            "concentration_risk": round(concentration_risk, 2),
            "total_portfolio_value": round(total_value, 4),
            "free_balance_pct": (
                round((sum(free_balance.values()) / total_value * 100), 2)
                if total_value > 0
                else 0
            ),
        }

    def generate_technical_analysis(self, symbol: str) -> Dict:
        """Gera análise técnica para um símbolo."""
        try:
            ohlcv = self.client.get_ohlcv(symbol, "1h", 100)
            if not ohlcv:
                return {}

            df = pd.DataFrame(
                ohlcv, columns=["timestamp", "open", "high", "low", "close", "volume"]
            )

            # Indicadores técnicos
            df["sma_20"] = talib.SMA(df["close"].values, timeperiod=20)
            df["sma_50"] = talib.SMA(df["close"].values, timeperiod=50)
            df["rsi"] = talib.RSI(df["close"].values, timeperiod=14)
            df["macd"], df["macd_signal"], df["macd_hist"] = talib.MACD(
                df["close"].values
            )

            # Sinais
            current_price = df["close"].iloc[-1]
            sma_20 = df["sma_20"].iloc[-1]
            sma_50 = df["sma_50"].iloc[-1]
            rsi = df["rsi"].iloc[-1]

            # Determinar tendência
            trend = (
                "ALTA"
                if current_price > sma_20 > sma_50
                else "BAIXA" if current_price < sma_20 < sma_50 else "LATERAL"
            )

            # Sinais de compra/venda
            signals = []
            if rsi < 30:
                signals.append("RSI oversold - possível compra")
            elif rsi > 70:
                signals.append("RSI overbought - possível venda")

            if current_price > sma_20:
                signals.append("Preço acima SMA 20 - tendência positiva")

            return {
                "symbol": symbol,
                "current_price": round(current_price, 6),
                "trend": trend,
                "rsi": round(rsi, 2),
                "signals": signals,
                "support_level": round(df["low"].tail(20).min(), 6),
                "resistance_level": round(df["high"].tail(20).max(), 6),
            }

        except Exception as e:
            self.logger.error("Erro na análise técnica para %s: %s", symbol, e)
            return {}

    def calculate_average_daily_return(self, performance: Dict) -> float:
        """Calcula a média diária de retorno do portfolio."""
        daily_pnl = performance.get("daily_pnl", pd.Series())
        if len(daily_pnl) == 0:
            return 0.0

        # Calcular o retorno diário
        daily_returns = daily_pnl.pct_change().fillna(0)
        average_daily_return = daily_returns.mean()

        return average_daily_return

    def generate_recommendations(self, analysis_results: Dict) -> List[str]:
        """Gera recomendações baseadas na análise."""
        recommendations = []

        # Análise de performance
        performance = analysis_results.get("performance", {})
        if performance.get("win_rate", 0) < 50:
            recommendations.append(
                "⚠️ Win rate baixo (<50%). Revisar estratégia de entrada/saída"
            )

        # Análise de risco
        risk = analysis_results.get("risk", {})
        if risk.get("max_drawdown", 0) < -20:
            recommendations.append(
                "🔴 Drawdown alto (>20%). Implementar melhor gestão de risco"
            )

        if risk.get("sharpe_ratio", 0) < 1:
            recommendations.append(
                "📊 Sharpe ratio baixo. Otimizar relação risco/retorno"
            )

        # Análise de portfolio
        portfolio = analysis_results.get("portfolio", {})
        if portfolio.get("concentration_risk", 0) > 50:
            recommendations.append(
                "⚖️ Alta concentração em um ativo (>50%). Diversificar portfolio"
            )

        if portfolio.get("free_balance_pct", 0) < 10:
            recommendations.append(
                "💰 Saldo livre baixo (<10%). Manter reserva para oportunidades"
            )

        # Análise de ordens
        orders = analysis_results.get("orders", {})
        if orders.get("execution_rate", 0) < 80:
            recommendations.append(
                "📋 Taxa de execução baixa (<80%). Revisar preços de ordens"
            )

        return recommendations

    def run_complete_analysis(self) -> AnalysisResult:
        """Executa análise completa do portfolio."""
        self.logger.info("Iniciando análise completa...")

        # Coletar dados
        self.collect_data()

        # Executar análises
        performance = self.analyze_trades_performance()
        orders = self.analyze_order_patterns()
        risk = self.analyze_risk_metrics()
        portfolio = self.analyze_portfolio_composition()
        average_daily_return = self.calculate_average_daily_return(performance)

        analysis_results = {
            "performance": performance,
            "orders": orders,
            "risk": risk,
            "portfolio": portfolio,
            "average_daily_return": average_daily_return,
        }

        # Gerar recomendações
        recommendations = self.generate_recommendations(analysis_results)

        # Preparar resultado final
        result = AnalysisResult(
            total_orders=orders.get("total_orders", 0),
            win_rate=performance.get("win_rate", 0),
            profit_loss=sum(performance.get("daily_pnl", [])),
            best_performer=str(
                performance.get("symbol_performance", {}).index[0]
                if len(performance.get("symbol_performance", {})) > 0
                else "N/A"
            ),
            worst_performer=str(
                performance.get("symbol_performance", {}).index[-1]
                if len(performance.get("symbol_performance", {})) > 0
                else "N/A"
            ),
            risk_score=abs(risk.get("max_drawdown", 0)),
            recommendations=recommendations,
        )

        return result, analysis_results

    def analyze_last_trades_per_symbol(self, n: int = 10) -> dict:
        """
        Analisa detalhadamente as últimas N operações completas por símbolo.
        Retorna um dicionário com métricas por símbolo.
        """
        trades = self.analysis_data.get("trades", [])
        if not trades:
            return {}

        df = pd.DataFrame(trades)
        if "symbol" not in df.columns or df.empty:
            return {}

        result = {}
        for symbol, group in df.groupby("symbol"):
            last_trades = group.sort_values("timestamp", ascending=False).head(n)
            if last_trades.empty:
                continue

            pnl = last_trades["cost"]
            win_rate = (pnl > 0).mean() * 100
            avg_pnl = pnl.mean()
            total_pnl = pnl.sum()
            max_drawdown = (pnl.cumsum() - pnl.cumsum().cummax()).min()
            avg_duration = (
                last_trades["timestamp"].diff().abs().mean() / 1000 / 60
                if len(last_trades) > 1
                else 0
            )
            best_trade = pnl.max()
            worst_trade = pnl.min()

            result[symbol] = {
                "total_trades": len(last_trades),
                "win_rate": round(win_rate, 2),
                "total_pnl": round(total_pnl, 4),
                "avg_pnl": round(avg_pnl, 4),
                "max_drawdown": round(max_drawdown, 4),
                "avg_duration_min": round(avg_duration, 2),
                "best_trade": round(best_trade, 4),
                "worst_trade": round(worst_trade, 4),
                "last_trade_time": pd.to_datetime(
                    last_trades["timestamp"].iloc[0], unit="ms"
                ),
            }
        return result


class OKXVisualizationEngine:
    """Engine de visualizações para análise do portfolio."""

    def __init__(self, analyzer: OKXPortfolioAnalyzer):
        self.analyzer = analyzer
        self.logger = TradingLogger.get_logger(__name__)

    def plot_performance_dashboard(self, analysis_results: Dict) -> None:
        """Cria dashboard de performance."""
        try:
            fig, axes = plt.subplots(2, 3, figsize=(20, 12))
            fig.suptitle(
                "OKX Portfolio Analysis Dashboard", fontsize=16, fontweight="bold"
            )

            self.plot_daily_pnl(axes[0, 0], analysis_results)
            self.plot_pnl_distribution(axes[0, 1], analysis_results)
            self.plot_symbol_performance(axes[0, 2], analysis_results)
            self.plot_portfolio_composition(axes[1, 0], analysis_results)
            self.plot_risk_metrics(axes[1, 1], analysis_results)
            self.plot_order_distribution(axes[1, 2], analysis_results)

            plt.tight_layout()
            plt.subplots_adjust(top=0.93)
            plt.show()
        except Exception as e:
            self.logger.error("Erro ao plotar dashboard de performance: %s", e)
            print("Erro ao plotar dashboard de performance: %s" % e)

    def plot_daily_pnl(self, ax, analysis_results: Dict) -> None:
        """Plota P&L diário."""
        performance = analysis_results.get("performance", {})
        daily_pnl = performance.get("daily_pnl", pd.Series())

        if len(daily_pnl) > 0:
            ax.plot(daily_pnl.index, daily_pnl.values, marker="o", linewidth=2)
            ax.axhline(y=0, color="red", linestyle="--", alpha=0.7)
            ax.set_title("P&L Diário")
            ax.set_xlabel("Data")
            ax.set_ylabel("P&L")
            ax.grid(True, alpha=0.3)

            # Colorir barras positivas/negativas
            colors = ["green" if x > 0 else "red" for x in daily_pnl.values]
            ax.bar(daily_pnl.index, daily_pnl.values, color=colors, alpha=0.7)

    def plot_pnl_distribution(self, ax, analysis_results: Dict) -> None:
        """Plota distribuição de P&L por trade."""
        trades = self.analyzer.analysis_data.get("trades", [])
        if trades:
            df_trades = pd.DataFrame(trades)
            pnl_values = df_trades["cost"].values

            ax.hist(pnl_values, bins=30, color="skyblue", alpha=0.7, edgecolor="black")
            ax.axvline(x=0, color="red", linestyle="--", alpha=0.7)
            ax.set_title("Distribuição de P&L por Trade")
            ax.set_xlabel("P&L")
            ax.set_ylabel("Frequência")
            ax.grid(True, alpha=0.3)

    def plot_symbol_performance(self, ax, analysis_results: Dict) -> None:
        """Plota performance por símbolo (Top 10)."""
        performance = analysis_results.get("performance", {})
        symbol_perf = performance.get("symbol_performance", pd.DataFrame())
        if not symbol_perf.empty:
            top_symbols = symbol_perf[("cost", "sum")].head(10)

            colors = ["green" if x > 0 else "red" for x in top_symbols.values]
            ax.barh(
                range(len(top_symbols)), top_symbols.values, color=colors, alpha=0.7
            )
            ax.set_yticks(range(len(top_symbols)))
            ax.set_yticklabels(top_symbols.index, fontsize=8)
            ax.set_title("Performance por Símbolo (Top 10)")
            ax.set_xlabel("P&L Total")
            ax.grid(True, alpha=0.3)

    def plot_portfolio_composition(self, ax, analysis_results: Dict) -> None:
        """Plota composição do portfolio (Top 10)."""
        portfolio = analysis_results.get("portfolio", {})
        asset_weights = portfolio.get("asset_weights", {})

        if asset_weights:
            # Mostrar apenas top 10 ativos
            top_assets = dict(
                sorted(asset_weights.items(), key=lambda x: x[1], reverse=True)[:10]
            )

            wedges, texts, autotexts = ax.pie(
                top_assets.values(),
                labels=top_assets.keys(),
                autopct="%1.1f%%",
                startangle=90,
            )
            ax.set_title("Composição do Portfolio (Top 10)")

            # Melhorar legibilidade
            for autotext in autotexts:
                autotext.set_color("white")
                autotext.set_fontweight("bold")

    def plot_risk_metrics(self, ax, analysis_results: Dict) -> None:
        """Plota métricas de risco."""
        risk = analysis_results.get("risk", {})
        risk_metrics = {
            "Sharpe Ratio": risk.get("sharpe_ratio", 0),
            "Max Drawdown (%)": abs(risk.get("max_drawdown", 0)),
            "Profit Factor": min(
                risk.get("profit_factor", 0), 10
            ),  # Cap para visualização
            "Volatility": risk.get("volatility", 0),
        }

        metrics_names = list(risk_metrics.keys())
        metrics_values = list(risk_metrics.values())

        colors_risk = [
            "green" if i % 2 == 0 else "orange" for i in range(len(metrics_names))
        ]
        bars = ax.bar(metrics_names, metrics_values, color=colors_risk, alpha=0.7)
        ax.set_title("Métricas de Risco")
        ax.set_ylabel("Valor")
        ax.tick_params(axis="x", rotation=45)
        ax.grid(True, alpha=0.3)

        # Adicionar valores nas barras
        for bar, value in zip(bars, metrics_values):
            height = bar.get_height()
            ax.text(
                bar.get_x() + bar.get_width() / 2.0,
                height + height * 0.05,
                f"{value:.2f}",
                ha="center",
                va="bottom",
                fontweight="bold",
            )

    def plot_order_distribution(self, ax, analysis_results: Dict) -> None:
        """Plota distribuição de ordens (Buy/Sell)."""
        orders = analysis_results.get("orders", {})
        order_types = orders.get("order_types", {})

        if order_types:
            ax.pie(order_types.values(), labels=order_types.keys(), autopct="%1.1f%%")
            ax.set_title("Distribuição de Ordens (Buy/Sell)")

    def plot_technical_analysis(self, symbol: str) -> None:
        """Plota análise técnica para um símbolo."""
        try:
            ohlcv = self.analyzer.client.get_ohlcv(symbol, "1h", 100)
            if not ohlcv:
                print(f"Dados não disponíveis para {symbol}")
                return

            df = pd.DataFrame(
                ohlcv, columns=["timestamp", "open", "high", "low", "close", "volume"]
            )
            df["datetime"] = pd.to_datetime(df["timestamp"], unit="ms")

            # Calcular indicadores
            df["sma_20"] = talib.SMA(df["close"].values, timeperiod=20)
            df["sma_50"] = talib.SMA(df["close"].values, timeperiod=50)
            df["rsi"] = talib.RSI(df["close"].values, timeperiod=14)
            df["macd"], df["macd_signal"], df["macd_hist"] = talib.MACD(
                df["close"].values
            )

            fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12))
            fig.suptitle(f"Análise Técnica - {symbol}", fontsize=16, fontweight="bold")

            # Gráfico de preço com médias móveis
            ax1.plot(
                df["datetime"], df["close"], label="Preço", linewidth=2, color="white"
            )
            ax1.plot(
                df["datetime"], df["sma_20"], label="SMA 20", alpha=0.7, color="orange"
            )
            ax1.plot(
                df["datetime"], df["sma_50"], label="SMA 50", alpha=0.7, color="blue"
            )
            ax1.fill_between(
                df["datetime"],
                df["sma_20"],
                df["sma_50"],
                where=(df["sma_20"] > df["sma_50"]),
                alpha=0.2,
                color="green",
                label="Tendência Alta",
            )
            ax1.fill_between(
                df["datetime"],
                df["sma_20"],
                df["sma_50"],
                where=(df["sma_20"] <= df["sma_50"]),
                alpha=0.2,
                color="red",
                label="Tendência Baixa",
            )
            ax1.set_title("Preço e Médias Móveis")
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            ax1.set_ylabel("Preço")

            # RSI
            ax2.plot(df["datetime"], df["rsi"], color="purple", linewidth=2)
            ax2.axhline(
                y=70, color="red", linestyle="--", alpha=0.7, label="Overbought (70)"
            )
            ax2.axhline(
                y=30, color="green", linestyle="--", alpha=0.7, label="Oversold (30)"
            )
            ax2.axhline(y=50, color="yellow", linestyle="-", alpha=0.5, label="Neutro")
            ax2.fill_between(df["datetime"], 70, 100, alpha=0.2, color="red")
            ax2.fill_between(df["datetime"], 0, 30, alpha=0.2, color="green")
            ax2.set_title("RSI (14)")
            ax2.set_ylim(0, 100)
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            ax2.set_ylabel("RSI")

            # MACD
            ax3.plot(
                df["datetime"], df["macd"], label="MACD", color="blue", linewidth=2
            )
            ax3.plot(
                df["datetime"],
                df["macd_signal"],
                label="Signal",
                color="red",
                linewidth=2,
            )

            # Colorir histograma baseado na direção
            colors = ["green" if x > 0 else "red" for x in df["macd_hist"]]
            ax3.bar(
                df["datetime"],
                df["macd_hist"],
                label="Histogram",
                alpha=0.7,
                color=colors,
            )
            ax3.axhline(y=0, color="white", linestyle="-", alpha=0.5)
            ax3.set_title("MACD")
            ax3.legend()
            ax3.grid(True, alpha=0.3)
            ax3.set_ylabel("MACD")
            ax3.set_xlabel("Data")

            plt.tight_layout()
            plt.subplots_adjust(top=0.93)
            plt.show()

        except Exception as e:
            self.logger.error("Erro ao plotar análise técnica para %s: %s", symbol, e)
            print("Erro ao gerar gráfico para %s: %s" % (symbol, e))

    def create_portfolio_heatmap(self, analysis_results: Dict) -> None:
        """Cria heatmap de performance dos ativos."""
        try:
            performance = analysis_results.get("performance", {})
            symbol_perf = performance.get("symbol_performance", pd.DataFrame())

            if symbol_perf.empty:
                print("Dados insuficientes para criar heatmap")
                return

            # Preparar dados para heatmap
            heatmap_data = symbol_perf[("cost", "sum")].head(20)  # Top 20 símbolos

            # Converter para matriz para heatmap
            n_cols = 5
            n_rows = int(np.ceil(len(heatmap_data) / n_cols))

            # Preencher com zeros se necessário
            padded_data = list(heatmap_data.values) + [0] * (
                n_rows * n_cols - len(heatmap_data)
            )
            matrix_data = np.array(padded_data).reshape(n_rows, n_cols)

            # Labels
            labels = list(heatmap_data.index) + [""] * (
                n_rows * n_cols - len(heatmap_data)
            )
            label_matrix = np.array(labels).reshape(n_rows, n_cols)

            plt.figure(figsize=(12, 8))

            # Criar heatmap
            sns.heatmap(
                matrix_data,
                annot=label_matrix,
                fmt="s",
                cmap="RdYlGn",
                center=0,
                cbar_kws={"label": "P&L Total"},
                square=True,
            )

            plt.title(
                "Heatmap de Performance por Ativo", fontsize=16, fontweight="bold"
            )
            plt.xlabel("")
            plt.ylabel("")
            plt.xticks([])
            plt.yticks([])
            plt.tight_layout()
            plt.show()

        except Exception as e:
            self.logger.error("Erro ao criar heatmap: %s", e)
            print("Erro ao criar heatmap: %s" % e)

    def generate_report_summary(
        self, result: AnalysisResult, analysis_results: Dict
    ) -> None:
        """Gera relatório resumido em texto."""
        print("\n" + "=" * 80)
        print("                         RELATÓRIO DE ANÁLISE OKX")
        print("-" * 50)

        # Seção de Overview
        print(f"\n📊 OVERVIEW GERAL")
        print(f"{'─'*50}")
        print(f"Total de Ordens: {result.total_orders:,}")
        print(f"Win Rate: {result.win_rate:.2f}%")
        print(f"P&L Total: {result.profit_loss:+.4f}")
        print(f"Risk Score: {result.risk_score:.2f}%")

        # Seção de Performance
        print(f"\n🎯 PERFORMANCE")
        print(f"{'─'*50}")
        performance = analysis_results.get("performance", {})
        if performance:
            print(f"Melhor Ativo: {result.best_performer}")
            print(f"Pior Ativo: {result.worst_performer}")

            daily_pnl = performance.get("daily_pnl", pd.Series())
            if len(daily_pnl) > 0:
                print(
                    f"Melhor Dia: {performance.get('best_day', 'N/A')} (+{daily_pnl.max():+.4f})"
                )
                print(
                    f"Pior Dia: {performance.get('worst_day', 'N/A')} ({daily_pnl.min():+.4f})"
                )

        # Seção de Risco
        print(f"\n⚠️  ANÁLISE DE RISCO")
        print(f"{'─'*50}")
        risk = analysis_results.get("risk", {})
        if risk:
            print(f"Sharpe Ratio: {risk.get('sharpe_ratio', 0):.4f}")
            print(f"Max Drawdown: {abs(risk.get('max_drawdown', 0)):.2f}%")
            print(f"Profit Factor: {risk.get('profit_factor', 0):.4f}")
            print(f"VaR 95%: {risk.get('var_95', 0):.4f}")
            print(f"Volatilidade: {risk.get('volatility', 0):.4f}")

        # Seção de Portfolio
        print(f"\n💼 COMPOSIÇÃO DO PORTFOLIO")
        print(f"{'─'*50}")
        portfolio = analysis_results.get("portfolio", {})
        if portfolio:
            print(f"Total de Ativos: {portfolio.get('total_assets', 0)}")
            print(f"Valor Total: {portfolio.get('total_portfolio_value', 0):.4f}")
            print(
                f"Concentração de Risco: {portfolio.get('concentration_risk', 0):.2f}%"
            )
            print(f"Saldo Livre: {portfolio.get('free_balance_pct', 0):.2f}%")

        # Seção de Ordens
        print(f"\n📋 ANÁLISE DE ORDENS")
        print(f"{'─'*50}")
        orders = analysis_results.get("orders", {})
        if orders:
            print(f"Taxa de Execução: {orders.get('execution_rate', 0):.2f}%")
            print(f"Ordens Canceladas: {orders.get('canceled_orders', 0)}")

            order_types = orders.get("order_types", {})
            for order_type, count in order_types.items():
                print(f"Ordens {order_type.title()}: {count}")

        # Seção de Recomendações
        print(f"\n💡 RECOMENDAÇÕES")
        print(f"{'─'*50}")
        if result.recommendations:
            for i, rec in enumerate(result.recommendations, 1):
                print(f"{i}. {rec}")
        else:
            print("✅ Portfolio está operando dentro dos parâmetros ideais!")

        # Rodapé
        print(f"\n{'='*80}")
        print(f"Relatório gerado em: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
        print(f"{'='*80}")


# Exemplo de uso completo
if __name__ == "__main__":
    print("🚀 Iniciando OKX Portfolio Analyzer...")

    # Inicializar analisador
    print("📊 Inicializando analisador...")
    analyzer = OKXPortfolioAnalyzer()

    print("\n🔎 Análise detalhada das últimas 10 operações por símbolo:")
    last_trades_analysis = analyzer.analyze_last_trades_per_symbol(n=10)
    for symbol, metrics in last_trades_analysis.items():
        print(f"\n--- {symbol} ---")
        for k, v in metrics.items():
            print(f"{k}: {v}")

    try:

        # Executar análise completa
        print("🔍 Executando análise completa do portfolio...")
        result, detailed_analysis = analyzer.run_complete_analysis()

        # Inicializar engine de visualização
        print("📈 Preparando visualizações...")
        viz_engine = OKXVisualizationEngine(analyzer)

        # Exibir relatório resumido
        viz_engine.generate_report_summary(result, detailed_analysis)

        # Gerar dashboard principal
        print("\n📊 Gerando dashboard de performance...")
        viz_engine.plot_performance_dashboard(detailed_analysis)

        # Criar heatmap de performance
        print("🔥 Criando heatmap de performance...")
        viz_engine.create_portfolio_heatmap(detailed_analysis)

        # Análise técnica dos principais ativos
        portfolio = detailed_analysis.get("portfolio", {})
        asset_weights = portfolio.get("asset_weights", {})

        if asset_weights:
            # Analisar os top 3 ativos por peso
            top_assets = sorted(
                asset_weights.items(), key=lambda x: x[1], reverse=True
            )[:3]

            print(f"\n📈 Executando análise técnica dos principais ativos...")

            for asset, weight in top_assets:
                if (
                    asset != "USDC" and weight > 5
                ):  # Ignorar USDC e ativos com peso < 5%
                    symbol = f"{asset}/USDC"
                    print(f"\n🔍 Analisando {symbol} (Peso: {weight:.1f}%)")

                    # Verificar se o mercado existe
                    if analyzer.client.has_market(symbol):
                        # Análise técnica básica
                        tech_analysis = analyzer.generate_technical_analysis(symbol)
                        if tech_analysis:
                            print(f"Preço Atual: {tech_analysis['current_price']}")
                            print(f"Tendência: {tech_analysis['trend']}")
                            print(f"RSI: {tech_analysis['rsi']}")
                            print(f"Suporte: {tech_analysis['support_level']}")
                            print(f"Resistência: {tech_analysis['resistance_level']}")

                            if tech_analysis["signals"]:
                                print("Sinais:")
                                for signal in tech_analysis["signals"]:
                                    print(f"• {signal}")

                        # Plotar gráfico técnico
                        print(f"📊 Gerando gráfico técnico para {symbol}...")
                        viz_engine.plot_technical_analysis(symbol)
                    else:
                        print(f"⚠️ Mercado {symbol} não disponível na exchange.")

        # Estatísticas finais
        print(f"\n✅ Análise concluída com sucesso!")
        print(
            f"📊 Total de gráficos gerados: {2 + len([a for a, w in top_assets if a != 'USDC' and w > 5])}"
        )
        print(f"⏱️  Tempo de execução: Análise completa realizada")

        # Salvar dados para uso posterior
        import json
        import os

        # Preparar dados para exportação
        analysis_data_for_export = {
            "result": {
                "total_orders": result.total_orders,
                "win_rate": result.win_rate,
                "profit_loss": result.profit_loss,
                "best_performer": result.best_performer,
                "worst_performer": result.worst_performer,
                "risk_score": result.risk_score,
                "recommendations": result.recommendations,
            },
            "analysis_results": {
                "performance": {
                    "total_trades": detailed_analysis["performance"].get(
                        "total_trades", 0
                    ),
                    "win_rate": detailed_analysis["performance"].get("win_rate", 0),
                    "daily_pnl": detailed_analysis["performance"]
                    .get("daily_pnl", {})
                    .to_dict(),
                    "symbol_performance": {
                        k: v.to_dict()
                        for k, v in detailed_analysis["performance"]
                        .get("symbol_performance", {})
                        .items()
                    },
                },
                "orders": {
                    "total_orders": detailed_analysis["orders"].get("total_orders", 0),
                    "execution_rate": detailed_analysis["orders"].get(
                        "execution_rate", 0
                    ),
                    "order_types": detailed_analysis["orders"].get("order_types", {}),
                    "canceled_orders": detailed_analysis["orders"].get(
                        "canceled_orders", 0
                    ),
                },
                "risk": detailed_analysis["risk"],
                "portfolio": {
                    "total_assets": detailed_analysis["portfolio"].get(
                        "total_assets", 0
                    ),
                    "asset_weights": detailed_analysis["portfolio"].get(
                        "asset_weights", {}
                    ),
                    "concentration_risk": detailed_analysis["portfolio"].get(
                        "concentration_risk", 0
                    ),
                    "total_portfolio_value": detailed_analysis["portfolio"].get(
                        "total_portfolio_value", 0
                    ),
                    "free_balance_pct": detailed_analysis["portfolio"].get(
                        "free_balance_pct", 0
                    ),
                },
            },
            "analysis_data": {"trades": analyzer.analysis_data.get("trades", [])},
        }

        # Criar diretório dashboard se não existir
        os.makedirs("dashboard", exist_ok=True)

        # Salvar dados em JSON
        with open("dashboard/analysis_data.json", "w") as f:
            json.dump(analysis_data_for_export, f, indent=2)

        print(f"\n💾 Dados de análise salvos em 'dashboard/analysis_data.json'")

    except ConfigurationError as e:
        print(f"❌ Erro de configuração: {e}")
        print("💡 Verifique suas credenciais da API OKX")

    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        print("💡 Verifique sua conexão e tente novamente")

    finally:
        print(f"\n🏁 Finalizando análise...")
        print(f"{'='*60}")
        print(f"  OKX Portfolio Analyzer - Desenvolvido para traders")
        print(f"{'='*60}")
