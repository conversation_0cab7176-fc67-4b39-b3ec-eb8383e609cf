import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings

warnings.filterwarnings("ignore")


def load_symbol_data(folder_path):
    """
    Carrega dados de múltiplos símbolos de uma pasta
    """
    symbol_data = {}

    # Verifica se a pasta existe
    if not os.path.exists(folder_path):
        print(f"Pasta {folder_path} não encontrada. Criando dados de exemplo...")
        return exit()
        
    # Lista arquivos CSV na pasta, apenas com timeframe 1h
    files = [f for f in os.listdir(folder_path) if f.endswith(".csv") and "1h" in f]
    
    for file in files:
        try:
            # Extract symbol from filename formatted as 'data_SYMBOL_1h.csv'
            parts = file.split('_')
            if len(parts) >= 3:
                symbol = parts[1] + '_' + parts[2].split('.')[0]
            else:
                symbol = file.replace('.csv', '')
            df = pd.read_csv(os.path.join(folder_path, file))

            # Identifica colunas de timestamp
            timestamp_cols = ["timestamp", "date", "datetime", "time"]
            timestamp_col = None

            for col in timestamp_cols:
                if col in df.columns:
                    timestamp_col = col
                    break

            if timestamp_col is None:
                print(f"Coluna de timestamp não encontrada em {file}")
                continue

            # Converte timestamp
            df["timestamp"] = pd.to_datetime(df[timestamp_col])

            # Identifica coluna de preço
            price_cols = ["close", "price", "Close", "Price"]
            price_col = None

            for col in price_cols:
                if col in df.columns:
                    price_col = col
                    break

            if price_col is None:
                print(f"Coluna de preço não encontrada em {file}")
                continue

            # Armazena dados limpos
            symbol_data[symbol] = (
                df[["timestamp", price_col]]
                .rename(columns={price_col: "price"})
                .sort_values("timestamp")
            )

        except Exception as e:
            print(f"Erro ao carregar {file}: {e}")

    return symbol_data


def calculate_hourly_volatility(symbol_data):
    """
    Calcula volatilidade por hora para cada símbolo
    """
    volatility_results = {}
    daily_volatility = {}

    for symbol, df in symbol_data.items():
        # Calcula retornos
        df["returns"] = df["price"].pct_change()

        # Extrai hora do timestamp
        df["hour"] = df["timestamp"].dt.hour

        # Calcula volatilidade por hora (desvio padrão dos retornos)
        hourly_vol = (
            df.groupby("hour")["returns"]
            .agg(
                [
                    (
                        "volatility",
                        lambda x: np.std(x) * np.sqrt(24),
                    ),  # Volatilidade diária
                    ("mean_return", "mean"),
                    ("count", "count"),
                ]
            )
            .reset_index()
        )

        # Calcula volatilidade média diária
        daily_avg_vol = np.std(df["returns"]) * np.sqrt(24) if not df["returns"].empty else 0

        volatility_results[symbol] = hourly_vol
        daily_volatility[symbol] = daily_avg_vol
    
    return volatility_results, daily_volatility


def analyze_peak_volatility_hours(volatility_results):
    """
    Identifica horários de maior volatilidade
    """
    analysis = {}

    for symbol, vol_df in volatility_results.items():
        # Top 3 horários com maior volatilidade
        top_hours = vol_df.nlargest(3, "volatility")[["hour", "volatility"]]

        # Estatísticas gerais
        avg_volatility = vol_df["volatility"].mean()
        max_volatility = vol_df["volatility"].max()
        min_volatility = vol_df["volatility"].min()

        analysis[symbol] = {
            "top_volatile_hours": top_hours.to_dict("records"),
            "avg_volatility": avg_volatility,
            "max_volatility": max_volatility,
            "min_volatility": min_volatility,
            "volatility_range": max_volatility - min_volatility,
        }

    return analysis


def create_volatility_heatmap(volatility_results):
    """
    Cria heatmap de volatilidade por símbolo e hora
    """
    # Combina dados de todos os símbolos
    combined_data = []

    for symbol, vol_df in volatility_results.items():
        vol_df["symbol"] = symbol
        combined_data.append(vol_df[["symbol", "hour", "volatility"]])

    df_combined = pd.concat(combined_data, ignore_index=True)

    # Cria pivot table para heatmap
    pivot_data = df_combined.pivot(index="symbol", columns="hour", values="volatility")

    # Converte valores para percentagem para o heatmap
    pivot_data_percent = pivot_data * 100

    # Cria o heatmap
    plt.figure(figsize=(15, 8))
    sns.heatmap(
        pivot_data_percent,
        annot=True,
        fmt=".2f",
        cmap="YlOrRd",
        cbar_kws={"label": "Volatilidade Diária (%)"},
    )
    plt.title("Heatmap de Volatilidade por Símbolo e Hora (UTC)", fontsize=16)
    plt.xlabel("Hora do Dia (UTC)", fontsize=12)
    plt.ylabel("Símbolo", fontsize=12)
    plt.tight_layout()
    plt.show()


def format_volatility_classification(volatility):
    """
    Classifica o nível de volatilidade
    """
    if volatility < 0.015:
        return "📈 BAIXA"
    elif volatility < 0.030:
        return "📊 MODERADA"
    elif volatility < 0.050:
        return "📈 ALTA"
    else:
        return "🚨 MUITO ALTA"


def main():
    """
    Função principal que executa toda a análise
    """
    print("=== ANÁLISE DE VOLATILIDADE HORÁRIA ===\n")

    # 1. Carrega dados
    folder_path = "data"
    symbol_data = load_symbol_data(folder_path)

    if not symbol_data:
        print("Nenhum dado foi carregado.")
        return

    print(f"Dados carregados para {len(symbol_data)} símbolos:")
    for symbol, df in symbol_data.items():
        print(f"  - {symbol}: {len(df)} registros")
        print()

    # 2. Calcula volatilidade horária e diária
    print("Calculando volatilidade por hora e diária...")
    volatility_results, daily_volatility = calculate_hourly_volatility(symbol_data)
    
    # 3. Análise de picos de volatilidade
    print("Analisando horários de maior volatilidade...")
    analysis = analyze_peak_volatility_hours(volatility_results)

    # 4. Exibe resultados com formatação de percentagens
    print("\n=== RESULTADOS DA ANÁLISE ===\n")

    for symbol, data in analysis.items():
        print(f"📊 {symbol}:")
        
        # Formatação usando f-strings com especificador de percentagem
        print(
            f"  Volatilidade média horária: {data['avg_volatility']:.2%} {format_volatility_classification(data['avg_volatility'])}"
        )
        print(
            f"  Volatilidade média diária: {daily_volatility[symbol]:.2%} {format_volatility_classification(daily_volatility[symbol])}"
        )
        print(
            f"  Volatilidade máxima horária: {data['max_volatility']:.2%} {format_volatility_classification(data['max_volatility'])}"
        )
        print(f"  Volatilidade mínima horária: {data['min_volatility']:.2%}")
        print(f"  Range de volatilidade horária: {data['volatility_range']:.2%}")

        print("  🕐 Top 3 horários mais voláteis:")

        for i, hour_data in enumerate(data["top_volatile_hours"], 1):
            # Usando .format() para formatação de percentagem
            volatility_percent = "{:.2%}".format(hour_data["volatility"])
            classification = format_volatility_classification(hour_data["volatility"])
            print(
                f"    {i}. {hour_data['hour']:02d}:00 UTC - "
                f"Volatilidade: {volatility_percent} {classification}"
            )
        print()

    # 5. Cria visualização
    print("Gerando heatmap de volatilidade...")
    create_volatility_heatmap(volatility_results)

    # 6. Resumo geral com formatação melhorada
    all_volatilities = []
    for symbol, vol_df in volatility_results.items():
        all_volatilities.extend(vol_df["volatility"].tolist())

    print(f"\n=== RESUMO GERAL ===")
    # Usando format() para percentagens
    print(f"Volatilidade média geral: {format(np.mean(all_volatilities), '.2%')}")
    print(f"Volatilidade máxima observada: {format(np.max(all_volatilities), '.2%')}")
    print(f"Desvio padrão das volatilidades: {format(np.std(all_volatilities), '.2%')}")

    # 7. Análise adicional por classificação
    print(f"\n=== DISTRIBUIÇÃO POR NÍVEL DE VOLATILIDADE ===")

    classification_counts = {"BAIXA": 0, "MODERADA": 0, "ALTA": 0, "MUITO ALTA": 0}

    for vol in all_volatilities:
        if vol < 0.015:
            classification_counts["BAIXA"] += 1
        elif vol < 0.030:
            classification_counts["MODERADA"] += 1
        elif vol < 0.050:
            classification_counts["ALTA"] += 1
        else:
            classification_counts["MUITO ALTA"] += 1

    total_observations = len(all_volatilities)

    for level, count in classification_counts.items():
        percentage = count / total_observations
        print(f"{level}: {count} observações ({percentage:.1%})")


# Executa o script
if __name__ == "__main__":
    main()
