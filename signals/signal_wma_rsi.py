"""
Classe para verificar sinais de entrada baseados em WMA e RSI com sistema de scoring avançado.
Implementa parâmetros otimizados: RSI(21), WMA(34), timeframe 1h, minimum signal strength 70/100.
"""

from typing import Dict, Optional
from utils.logger import TradingLogger
from utils.error_handler import <PERSON><PERSON>r<PERSON>and<PERSON>
from indicators.indicators import TechnicalIndicator


class SinaisWmaRsi:
    """
    Classe para verificar sinais de entrada baseados em WMA e RSI com sistema de scoring avançado.

    Parâmetros otimizados:
    - RSI Period: 21 (mais suave que 14)
    - WMA Period: 34 (<PERSON><PERSON><PERSON><PERSON>, mais responsivo)
    - Timeframe Principal: 1h (bom equilíbrio)
    - Minimum Signal Strength: 70/100
    """

    def __init__(self, indicator: TechnicalIndicator, client):
        self.indicator = indicator
        self.client = client
        self.logger = TradingLogger.get_logger(__name__)
        self.error_handler = <PERSON>rrorHandler(logger_name=__name__)

        # Parâmetros otimizados
        self.RSI_PERIOD = 21  # Mais suave que 14
        self.WMA_PERIOD = 34  # <PERSON><PERSON><PERSON><PERSON>, mais responsivo
        self.MINIMUM_SIGNAL_STRENGTH = 70  # Threshold para sinais de qualidade

    def calculate_signal_strength(
        self,
        symbol: str,
        timeframe: str = None,
        tickers: Optional[Dict] = None,
    ) -> float:
        """
        Calcula força do sinal (0-100) baseado em múltiplos fatores.

        Sistema de scoring:
        - RSI Score (40 pontos): Oversold bounce e momentum
        - WMA Score (30 pontos): Posição relativa ao preço
        - Trend Score (20 pontos): Alinhamento de tendência
        - Volume Score (10 pontos): Confirmação de volume

        Returns:
            float: Score de 0-100, onde >=70 indica sinal de alta qualidade
        """
        if timeframe is None:
            timeframe = "1h"  # Timeframe principal otimizado

        try:
            if tickers and symbol in tickers:
                ticker = tickers[symbol]
            else:
                ticker = self.client.get_ticker(symbol)
            if not ticker or "last" not in ticker:
                return 0.0

            price = ticker["last"]

            # Calcular indicadores com parâmetros otimizados
            rsi = self.indicator.calculate_rsi(symbol, timeframe, period=self.RSI_PERIOD)
            wma = self.indicator.calculate_wma(symbol, timeframe, period=self.WMA_PERIOD)

            if rsi is None or wma is None:
                return 0.0

            score = 0.0

            # 1. RSI Score (40 pontos) - Oversold bounce é mais lucrativo
            if rsi < 30:  # Oversold forte
                score += 40
            elif rsi < 40:  # Oversold moderado
                score += 30
            elif 40 <= rsi <= 60:  # Zona neutra com momentum
                score += 25
            elif rsi > 50:  # Condição original (menos pontos)
                score += 15

            # 2. WMA Score (30 pontos) - Proximidade e crossover
            price_vs_wma_pct = ((price - wma) / wma) * 100
            if -1 <= price_vs_wma_pct <= 2:  # Próximo da WMA (ideal para entrada)
                score += 30
            elif price_vs_wma_pct > 2:  # Acima da WMA
                score += 20
            elif price_vs_wma_pct > 0:  # Ligeiramente acima
                score += 15

            # 3. Trend Score (20 pontos) - Alinhamento de tendência
            try:
                sma_50 = self.indicator.calculate_sma(symbol, timeframe, period=50)
                sma_200 = self.indicator.calculate_sma(symbol, timeframe, period=200)
                if sma_50 and sma_200:
                    if sma_50 > sma_200:  # Tendência de alta
                        score += 20
                    elif price > sma_50:  # Pelo menos acima da SMA50
                        score += 10
            except:
                pass  # Se não conseguir calcular, não adiciona pontos

            # 4. Volume Score (10 pontos) - Confirmação de volume
            try:
                current_volume = ticker.get("baseVolume", 0)
                if current_volume:  # Se tiver dados de volume
                    score += 10  # Bonus por ter volume
            except:
                pass  # Se não conseguir calcular volume, não adiciona pontos

            return min(score, 100.0)  # Máximo 100 pontos

        except Exception as exc:
            self.logger.error(f"Erro ao calcular força do sinal para {symbol}: {exc}")
            return 0.0

    def check_entry_signal(
        self,
        symbol: str,
        timeframe: str = None,
        tickers: Optional[Dict] = None,
    ) -> bool:
        """
        Sinal de compra otimizado com sistema de scoring.

        Nova lógica:
        - Calcula signal strength (0-100)
        - Requer minimum signal strength >= 70
        - Usa parâmetros otimizados: RSI(21), WMA(34), timeframe 1h
        """
        try:
            # Usar timeframe otimizado se não especificado
            if timeframe is None:
                timeframe = "1h"  # Timeframe principal otimizado

            signal_strength = self.calculate_signal_strength(symbol, timeframe, tickers)

            # Requer signal strength mínimo de 70/100
            if signal_strength >= self.MINIMUM_SIGNAL_STRENGTH:
                self.logger.info(f"Sinal de entrada detectado para {symbol} - Força: {signal_strength:.1f}/100")
                return True

            self.logger.debug(f"Sinal fraco para {symbol} - Força: {signal_strength:.1f}/100 (mín: {self.MINIMUM_SIGNAL_STRENGTH})")
            return False

        except Exception as exc:
            self.logger.error(f"Erro ao verificar sinal para {symbol}: {exc}")
            return False

    def print_signal(
        self,
        symbol: str,
        timeframe: str = None,
        tickers: Optional[Dict] = None,
    ) -> None:
        """
        Imprime o sinal de trading otimizado: BUY ou HOLD, usando WMA(34) e RSI(21) com sistema de scoring.
        Mostra a força do sinal (0-100) e detalhes dos indicadores.
        """
        if timeframe is None:
            timeframe = "1h"  # Timeframe principal otimizado

        try:
            if tickers and symbol in tickers:
                ticker = tickers[symbol]
            else:
                ticker = self.client.get_ticker(symbol)
            if not ticker or "last" not in ticker:
                print(f"\033[91m • {symbol}: Dados de preço indisponíveis\033[0m")
                print()
                return

            price = ticker["last"]

            # Calcular força do sinal
            signal_strength = self.calculate_signal_strength(symbol, timeframe, tickers)

            # Calcular indicadores para exibição
            rsi = self.indicator.calculate_rsi(symbol, timeframe, period=self.RSI_PERIOD)
            wma = self.indicator.calculate_wma(symbol, timeframe, period=self.WMA_PERIOD)

            if rsi is None or wma is None:
                print(f"\033[91m • {symbol}: Indicadores indisponíveis\033[0m")
                print()
                return

            # Determinar sinal baseado na força
            if signal_strength >= self.MINIMUM_SIGNAL_STRENGTH:
                signal = "BUY"
                color = "\033[92m"  # Verde
                strength_color = "\033[92m"  # Verde para força alta
            elif signal_strength >= 50:
                signal = "HOLD"
                color = "\033[93m"  # Amarelo
                strength_color = "\033[93m"  # Amarelo para força média
            else:
                signal = "HOLD"
                color = "\033[91m"  # Vermelho
                strength_color = "\033[91m"  # Vermelho para força baixa

            print()
            print(f"{color} • {symbol}: SINAL = {signal}\033[0m")
            print(f"   Preço: ${price:.2f}")
            print(f"{strength_color}   Força do Sinal: {signal_strength:.1f}/100\033[0m")
            print(f"   RSI({self.RSI_PERIOD}): {rsi:.1f}")
            print(f"   WMA({self.WMA_PERIOD}): ${wma:.2f}")

            # Mostrar distância da WMA
            if wma:
                price_vs_wma_pct = ((price - wma) / wma) * 100
                print(f"   Distância WMA: {price_vs_wma_pct:+.2f}%")

            print()

        except Exception as exc:
            self.logger.error(f"Erro ao imprimir sinal para {symbol}: {exc}")
            print(f"\033[91m • {symbol}: Erro ao processar sinal\033[0m")
            print()
