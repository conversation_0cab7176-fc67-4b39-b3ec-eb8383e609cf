"""
Módulo comum para o cliente OKX, contendo a classe OKXClient com métodos compartilhados
para conexão e operações com a exchange OKX.
"""

import os
import shutil
from decimal import Decimal, ROUND_HALF_UP
from typing import Dict, Optional, Tuple
import ccxt
from utils.logger import TradingLogger
from utils.error_handler import ErrorHandler
from utils.exceptions import ConfigurationError, ExchangeConnectionError
from dotenv import load_dotenv
import json

load_dotenv()
FIAT_CURRENCIES = ["EUR", "USD"]


def load_parameters():
    """Carrega os parâmetros do arquivo parameters.json."""
    with open("parameters.json", "r") as f:
        return json.load(f)


class OKXClient:
    """Cliente OKX otimizado com padrão Singleton para conexão com a exchange."""

    _instance = None

    def __new__(cls, config=None):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, config=None):
        if getattr(self, "_initialized", False):
            return
        self._initialized = True
        self.logger = TradingLogger.get_logger(__name__)
        self.config = config if config else self._default_config()
        self.error_handler = ErrorHandler(
            logger_name=__name__, max_retries=3, retry_delay=2
        )
        self._initialize_exchange()
        self._initialize_cache()

    def _initialize_cache(self):
        """Inicializa o cache para dados frequentemente acessados."""
        self.cache_duration = 30  # Duração do cache em segundos
        self.tickers_cache = {}
        self.balance_cache = None
        self.last_tickers_updated = {}
        self.last_balance_updated = 0

    def _default_config(self):
        """Retorna uma configuração padrão se nenhuma for fornecida."""

        class DefaultConfig:
            params = load_parameters()

            SANDBOX_MODE = True
            PREFIX = "demo" if SANDBOX_MODE else "live"
            TRADING_SYMBOL = "BTC/USDC"
            GRID_SYMBOLS = ["BTC/USDC"]
            STABLECOINS = ["USDC", "USDT", "BUSD", "FDUSD"]
            FIAT_DECIMAL_PLACES = 6
            CRYPTO_DECIMAL_PLACES = 8
            TRADING_SYMBOLS = params["general"]["TRADING_SYMBOLS"]
            TIMEFRAME = params["general"]["TIMEFRAME"]
            RSI_PERIOD: int = params["general"]["RSI_PERIOD"]
            WMA_PERIOD: int = params["general"]["WMA_PERIOD"]
            OHLCV_LIMIT: int = params["general"]["OHLCV_LIMIT"]
            GRID_COUNT = 10
            RANGE_MULTIPLIER = 3.0
            MIN_ORDER_MULTIPLIER = 40
            SL_MULTIPLIER = 1.55
            ATR_MULTIPLIER = 1.75
            RSI_MIN = 30
            MAKER_FEE = 0.0008
            TAKER_FEE = 0.001
            ENABLE_SOUND_NOTIFICATIONS = True
            ENABLE_TELEGRAM_NOTIFICATIONS = True

        return DefaultConfig()

    def _initialize_exchange(self) -> None:
        def initialize_exchange_func():
            credentials = self._get_credentials(self.config.PREFIX)
            self.exchange = ccxt.myokx(
                {
                    "apiKey": credentials["api_key"],
                    "secret": credentials["secret"],
                    "password": credentials["password"],
                    "enableRateLimit": True,
                    "options": {"defaultType": "spot"},
                }
            )
            self.exchange.set_sandbox_mode(self.config.SANDBOX_MODE)
            self._log_initialization_status()
            return self.exchange

        result = self.error_handler.execute_with_retry(
            initialize_exchange_func, "Inicializar exchange"
        )
        if result is None:
            raise ConfigurationError(
                "Falha ao inicializar a exchange após várias tentativas"
            )

    def _get_credentials(self, prefix: str) -> Dict[str, str]:
        credentials = {
            "api_key": os.getenv(f"{prefix}_okx_apiKey"),
            "secret": os.getenv(f"{prefix}_okx_secret"),
            "password": os.getenv(f"{prefix}_okx_password"),
        }
        if not all(credentials.values()):
            self.logger.error("Credenciais incompletas para o prefixo '%s'", prefix)
            raise ConfigurationError(
                f"Credenciais incompletas para o prefixo '{prefix}'."
            )
        return credentials

    def _log_initialization_status(self) -> None:
        mode = "SANDBOX" if self.config.SANDBOX_MODE else "LIVE"
        self.logger.info("Cliente OKX inicializado no modo %s", mode)

    def _print_mode_message(self) -> None:
        def print_mode_message_func():
            columns = shutil.get_terminal_size().columns
            msg = (
                "🔴 DEMO TRADING 🔴"
                if self.config.SANDBOX_MODE
                else "🟢 LIVE TRADING 🟢"
            )
            color = "\033[1;37;43m" if self.config.SANDBOX_MODE else "\033[1;37;47m"
            reset = "\033[0;0m"
            self.logger.info(f"{color}{msg.center(columns)}{reset}")
            return True

        result = self.error_handler.execute_with_retry(
            print_mode_message_func, "Imprimir mensagem de modo"
        )
        if result is None:
            self.logger.warning(
                "Falha ao imprimir mensagem de modo após várias tentativas"
            )

    def format_price_with_precision(self, symbol: str, price: float) -> str:
        if price <= 0:
            raise ValueError("Price must be positive")

        def format_price_func():
            decimal_price = Decimal(str(price))
            formatted_price = self.exchange.price_to_precision(
                symbol, float(decimal_price)
            )
            return str(formatted_price)

        result = self.error_handler.execute_with_retry(
            format_price_func, f"Formatar preço para {symbol}"
        )
        if result is None:
            self.logger.warning(
                "Falha ao formatar preço para %s após várias tentativas", symbol
            )
            return self._fallback_price_format(price)
        return result

    def format_amount_with_precision(self, symbol: str, amount: float) -> str:
        if amount <= 0:
            self.logger.debug(
                "Quantidade para %s é menor ou igual a zero: %s", symbol, amount
            )
            return "0"

        def format_amount_func():
            try:
                decimal_amount = Decimal(str(amount))
                formatted_amount = self.exchange.amount_to_precision(
                    symbol, float(decimal_amount)
                )
                return str(formatted_amount)
            except Exception as e:
                if "must be greater than minimum amount precision" in str(e):
                    self.logger.debug(
                        "Quantidade para %s está abaixo da precisão mínima: %s",
                        symbol,
                        amount,
                    )
                    return "0"
                raise

        result = self.error_handler.execute_with_retry(
            format_amount_func, f"Formatar quantidade para {symbol}"
        )
        if result is None:
            self.logger.warning(
                "Falha ao formatar quantidade para %s após várias tentativas", symbol
            )
            return self._fallback_amount_format(amount)
        return result

    def _fallback_price_format(self, price: float) -> str:
        decimal_price = Decimal(str(price))
        formatted = f"{decimal_price:.{self.config.CRYPTO_DECIMAL_PLACES}f}"
        return formatted.rstrip("0").rstrip(".")

    def _fallback_amount_format(self, amount: float) -> str:
        decimal_amount = Decimal(str(amount))
        formatted = f"{decimal_amount:.{self.config.CRYPTO_DECIMAL_PLACES}f}"
        return formatted.rstrip("0").rstrip(".")

    def format_fiat_amount(self, amount: float, decimals: int = None) -> str:
        def format_fiat_func():
            decimals = decimals or self.config.FIAT_DECIMAL_PLACES
            decimal_amount = Decimal(str(amount))
            rounded = decimal_amount.quantize(
                Decimal("0." + "0" * decimals), rounding=ROUND_HALF_UP
            )
            formatted = f"{rounded:.{decimals}f}".rstrip("0").rstrip(".")
            return formatted if formatted else "0"

        result = self.error_handler.execute_with_retry(
            format_fiat_func, "Formatar quantidade fiat"
        )
        if result is None:
            self.logger.warning(
                "Falha ao formatar quantidade fiat após várias tentativas"
            )
            return f"{amount:.{decimals if decimals else 2}f}"
        return result

    def get_balance(self) -> Optional[Dict]:
        """Obtém o saldo da conta, usando cache se disponível e atualizado."""
        import time

        if (
            self.balance_cache
            and time.time() - self.last_balance_updated < self.cache_duration
        ):
            self.logger.debug("Usando saldo do cache")
            return self.balance_cache

        def fetch_balance():
            return self.exchange.fetch_balance()

        balance = self.error_handler.execute_with_retry(fetch_balance, "Obter saldo")
        if balance is None:
            self.logger.error("Falha ao obter saldo após várias tentativas")
            raise ExchangeConnectionError("Falha ao obter saldo após várias tentativas")
        self.logger.debug("Saldo obtido com sucesso")
        self.balance_cache = balance
        self.last_balance_updated = time.time()
        # self.logger.info("Cache de saldo atualizado")
        return balance

    def get_ticker(self, symbol: str) -> Optional[Dict]:
        """Obtém o ticker de um símbolo, usando cache se disponível e atualizado."""
        import time

        if symbol in self.tickers_cache and symbol in self.last_tickers_updated:
            if time.time() - self.last_tickers_updated[symbol] < self.cache_duration:
                # self.logger.info("Usando ticker do cache para %s", symbol)
                return self.tickers_cache[symbol]

        def fetch_ticker():
            return self.exchange.fetch_ticker(symbol)

        ticker = self.error_handler.execute_with_retry(
            fetch_ticker, f"Obter Ticker para {symbol}"
        )
        if ticker is None:
            self.logger.error(
                f"Falha ao obter ticker para {symbol} após várias tentativas"
            )
            raise ConfigurationError(
                f"Falha ao obter ticker para {symbol} após várias tentativas"
            )
        self.logger.debug("Ticker obtido para %s", symbol)
        self.tickers_cache[symbol] = ticker
        self.last_tickers_updated[symbol] = time.time()
        # self.logger.info("Cache de ticker atualizado para %s", symbol)
        return ticker

    def get_best_bid(self, symbol: str) -> Optional[float]:
        def fetch_best_bid():
            ticker = self.get_ticker(symbol)
            if ticker and "bid" in ticker and ticker["bid"]:
                return float(ticker["bid"])
            orderbook = self.exchange.fetch_order_book(symbol, limit=1)
            if orderbook and orderbook["bids"] and len(orderbook["bids"]) > 0:
                return float(orderbook["bids"][0][0])
            return None

        result = self.error_handler.execute_with_retry(
            fetch_best_bid, f"Obter melhor bid para {symbol}"
        )
        if result is None:
            self.logger.warning(
                f"Falha ao obter melhor bid para {symbol} após várias tentativas"
            )
        return result

    def get_best_ask(self, symbol: str) -> Optional[float]:
        def fetch_best_ask():
            ticker = self.get_ticker(symbol)
            if ticker and "ask" in ticker and ticker["ask"]:
                return float(ticker["ask"])
            orderbook = self.exchange.fetch_order_book(symbol, limit=1)
            if orderbook and orderbook["asks"] and len(orderbook["asks"]) > 0:
                return float(orderbook["asks"][0][0])
            return None

        result = self.error_handler.execute_with_retry(
            fetch_best_ask, f"Obter melhor ask para {symbol}"
        )
        if result is None:
            self.logger.warning(
                f"Falha ao obter melhor ask para {symbol} após várias tentativas"
            )
        return result

    def display_balance_with_precision(self) -> None:
        def display_balance_func():
            balance = self.get_balance()
            if not self._validate_balance(balance):
                return
            # self._print_balance_header()
            self._process_balance_assets(balance["total"])
            ten_percent_balance = self.calculate_ten_percent_balance(balance["total"])
            if ten_percent_balance:
                # print("\n📊 10% do Saldo Disponível:")
                # print("=" * 50)
                for asset, amount in ten_percent_balance.items():
                    formatter = self._get_asset_formatter(asset)
                    formatted_amount, emoji = formatter(asset, amount)
                    # print(f" {emoji} {asset}: {formatted_amount}")
            return True

        result = self.error_handler.execute_with_retry(
            display_balance_func, "Exibir saldo"
        )
        if result is None:
            self.logger.error("Falha ao exibir saldo após várias tentativas")

    def _validate_balance(self, balance: Optional[Dict]) -> bool:
        if not balance or not balance.get("total"):
            print("❌ Nenhuma informação de saldo disponível")
            return False
        return True

        print("💰 Saldo Disponível:")
        print("=" * 50)

    def _process_balance_assets(self, total_balance: Dict[str, float]) -> None:
        for asset, amount in total_balance.items():
            if amount > 0:
                self._display_single_asset(asset, amount)

    def _display_single_asset(self, asset: str, amount: float) -> None:
        try:
            formatter = self._get_asset_formatter(asset)
            formatted_amount, emoji = formatter(asset, amount)
            print(f" {emoji} {asset}: {formatted_amount}")
        except Exception as exc:
            self.logger.warning("Falha ao formatar %s: %s", asset, str(exc))
            self._display_fallback_asset(asset, amount)

    def _get_asset_formatter(self, asset: str) -> callable:
        if asset in ["BTC", "ETH"]:
            return self._format_crypto_asset
        elif hasattr(self.config, "STABLECOINS") and asset in self.config.STABLECOINS:
            return self._format_stablecoin_asset
        elif asset in FIAT_CURRENCIES:
            return self._format_fiat_asset
        elif asset in self._default_config().STABLECOINS:
            return self._format_stablecoin_asset
        else:
            return self._format_other_asset

    def _format_crypto_asset(self, asset: str, amount: float) -> Tuple[str, str]:
        symbol = f"{asset}/USDC"
        formatted_amount = self.format_amount_with_precision(symbol, amount)
        return formatted_amount, "🪙"

    def _format_stablecoin_asset(self, asset: str, amount: float) -> Tuple[str, str]:
        formatted_amount = f"{float(amount):.2f}"
        return formatted_amount, "💵"

    def _format_fiat_asset(self, asset: str, amount: float) -> Tuple[str, str]:
        formatted_amount = f"{float(amount):.2f}"
        return formatted_amount, "💴"

    def _format_other_asset(self, asset: str, amount: float) -> Tuple[str, str]:
        try:
            symbol = f"{asset}/USDC"
            formatted_amount = self.format_amount_with_precision(symbol, amount)
            return formatted_amount, "🔹"
        except Exception:
            formatted_amount = self._fallback_amount_format(amount)
            return formatted_amount, "⚠️"

    def _display_fallback_asset(self, asset: str, amount: float) -> None:
        if (
            asset in FIAT_CURRENCIES
            or (
                hasattr(self.config, "STABLECOINS") and asset in self.config.STABLECOINS
            )
            or asset in self._default_config().STABLECOINS
        ):
            formatted_fallback = f"{float(amount):.2f}"
            emoji = "💴" if asset in FIAT_CURRENCIES else "💵"
        else:
            formatted_fallback = f"{amount:.8f}".rstrip("0").rstrip(".")
            emoji = "⚠️"
        print(f" {emoji} {asset}: {formatted_fallback}")

    def display_prices_with_precision(self) -> None:
        """Exibe preços de mercado para todos os símbolos, usando batch para reduzir chamadas à API."""
        import time

        def fetch_and_display_prices():
            # self.logger.info("Preços Atuais do Mercado:")
            # self.logger.info("=" * 50)
            tickers = self.exchange.fetch_tickers(self.config.TRADING_SYMBOLS)
            self.logger.debug("Tickers obtidos para %s", self.config.TRADING_SYMBOLS)
            for symbol in self.config.TRADING_SYMBOLS:
                ticker = tickers.get(symbol)
                if not ticker or not ticker.get("last"):
                    self.logger.warning(f"{symbol}: Dados de ticker indisponíveis")
                    continue
                # self._print_ticker_info(symbol, ticker)
                # Atualiza o cache com os dados obtidos
                self.tickers_cache[symbol] = ticker
                self.last_tickers_updated[symbol] = time.time()
            # self.logger.info("Cache de tickers atualizado para todos os símbolos")
            return True

        result = self.error_handler.execute_with_retry(
            fetch_and_display_prices, "Exibir preços de mercado"
        )
        if result is None:
            self.logger.error("Falha ao obter preços de mercado após várias tentativas")
            for symbol in self.config.TRADING_SYMBOLS:
                self._display_single_price(symbol)

    def _display_single_price(self, symbol: str) -> None:
        def fetch_single_price():
            ticker = self.get_ticker(symbol)
            if not ticker or not ticker.get("last"):
                self.logger.warning(f"{symbol}: No ticker data available")
                return False
            self._print_ticker_info(symbol, ticker)
            return True

        result = self.error_handler.execute_with_retry(
            fetch_single_price, f"Obter preço para {symbol}"
        )
        if result is None or not result:
            self.logger.warning(
                f"Falha ao obter preço para {symbol} após várias tentativas"
            )

    def _print_ticker_info(self, symbol: str, ticker: Dict) -> None:
        formatted_price = self.format_price_with_precision(symbol, ticker["last"])
        volume = ticker.get("baseVolume", 0)
        change_percent = ticker.get("percentage", 0)
        change_emoji = "📈" if change_percent >= 0 else "📉"
        print(f" {change_emoji} {symbol}")
        print(f"   Price: ${formatted_price}")
        if volume > 0:
            formatted_volume = self.format_amount_with_precision(symbol, volume)
            print(f"   Volume: {formatted_volume}")
        if change_percent != 0:
            print(f"   Change: {change_percent:+.2f}%")
        print()

    def calculate_ten_percent_balance(
        self, balance: Dict[str, float]
    ) -> Dict[str, float]:
        ten_percent_balance = {}
        for asset, amount in balance.items():
            if amount > 0:
                ten_percent_balance[asset] = amount * 0.1
        return ten_percent_balance

    def display_order_status_summary(self) -> None:
        """
        Exibe um resumo consolidado do status das ordens para todos os símbolos de trading.
        """
        from utils.check_orders import (
            check_regular_orders,
            check_oco_orders,
            check_trailing_stop_orders,
        )

        print("\n📊 Resumo do Status das Ordens:")
        print("=" * 50)
        for symbol in self.config.TRADING_SYMBOLS:
            try:
                regular_count = len(check_regular_orders(self, symbol))
                oco_count = len(check_oco_orders(self, symbol))
                trailing_count = len(check_trailing_stop_orders(self, symbol))
                total_orders = regular_count + oco_count + trailing_count

                if total_orders > 0:
                    status_emoji = "🔴" if trailing_count > 0 else "🟡"
                    print(f" {status_emoji} {symbol}:")
                    print(f"• Ordens Regulares: {regular_count}")
                    print(f"• Ordens OCO: {oco_count}")
                    print(f"• Trailing Stops: {trailing_count}")
                    print(f"• Total: {total_orders}")
                else:
                    print(f" 🟢 {symbol}: Sem ordens ativas")
            except Exception as exc:
                print(f" ❌ {symbol}: Erro ao verificar status - {exc}")
        print()
