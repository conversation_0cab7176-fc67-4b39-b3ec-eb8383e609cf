"""
Base bot class for trading bots to reduce code duplication and improve modularity.
This module defines TradingBotBase, which encapsulates shared functionality for initialization,
database handling, notifications, and the core trading loop.
"""

import os
import asyncio
import sqlite3
import pandas as pd
import signal
from typing import Optional, Dict, List, Any, Tuple
from utils.logger import TradingLogger
from utils.database import TradingDatabase
from utils.metrics import MetricsCalculator
from utils.error_handler import ErrorHandler
from service.service_alerts import SoundNotifications
from service.service_telegram import TelegramNotifier
from core.config import BotConfig, OrderCache
from core.okx_client import OKXClient
from indicators.indicators import TechnicalIndicator
from utils.exceptions import ConfigurationError, ExchangeConnectionError
from utils.common import send_telegram_notification, play_alert
from utils.notification_utils import build_startup_message


class TradingBotBase:
    """Base class for trading bots with shared functionality."""

    def __init__(self, config: Optional[BotConfig] = None):
        """
        Initialize the base trading bot.
        
        Args:
            config: Bot configuration object. If None, a default BotConfig is created.
        """
        self.config = config or BotConfig()
        self.logger = TradingLogger.get_logger(__name__)
        self.error_handler = ErrorHandler(logger_name=__name__)
        self.sound_alert = self._initialize_sound_notifications()
        self.notifier = self._initialize_telegram_notifier()
        self.created_orders = []
        self.db = self._initialize_database()
        self.metrics_calculator = self._initialize_metrics_calculator()
        self.order_cache = OrderCache(cache_duration=30)
        
        # Daily loss limit tracking
        self.daily_loss_limit = 0.05  # 5% of starting balance
        self.daily_starting_balance = None
        self.daily_max_loss = None

    def _initialize_sound_notifications(self) -> Optional[SoundNotifications]:
        """Initialize sound notifications if enabled in config."""
        if self.config.ENABLE_SOUND_NOTIFICATIONS:
            try:
                return SoundNotifications()
            except Exception as exc:
                self.logger.warning("Falha ao inicializar notificações sonoras: %s", str(exc))
        return None

    def _initialize_telegram_notifier(self) -> Optional[TelegramNotifier]:
        """Initialize Telegram notifier if enabled in config."""
        if self.config.ENABLE_TELEGRAM_NOTIFICATIONS:
            try:
                return TelegramNotifier()
            except Exception as exc:
                self.logger.warning("Falha ao inicializar notificador Telegram: %s", str(exc))
        return None

    def _initialize_database(self) -> TradingDatabase:
        """Initialize the database for order persistence."""
        db_name = "sandbox_trades.db" if self.config.SANDBOX_MODE else "live_trades.db"
        db_path = os.path.join("trades", db_name)
        try:
            db = TradingDatabase(db_path)
            # Create table for tracking notified orders if it doesn't exist
            db.create_notified_orders_table()
            return db
        except Exception as exc:
            self.logger.error("Falha ao inicializar banco de dados: %s", str(exc))
            raise

    def _initialize_metrics_calculator(self) -> MetricsCalculator:
        """Initialize the metrics calculator for performance analysis."""
        db_name = "sandbox_trades.db" if self.config.SANDBOX_MODE else "live_trades.db"
        db_path = os.path.join("trades", db_name)
        try:
            return MetricsCalculator(db_path)
        except Exception as exc:
            self.logger.warning("Falha ao inicializar calculador de métricas: %s", str(exc))
            return None

    async def play_alert(self, alert_type: str, volume: float = 0.5) -> None:
        """Play a sound alert if available."""
        await play_alert(self.sound_alert, alert_type, volume)

    async def send_telegram_notification(self, message: str) -> None:
        """Send a Telegram notification if available."""
        await send_telegram_notification(self.notifier, message)

    def save_order_to_db(self, order: Dict) -> None:
        """Save an order to the database."""
        try:
            self.db.save_order(order)
            self.logger.debug("Ordem salva no banco de dados: %s", order.get("id", "N/A"))
        except Exception as exc:
            self.logger.error("Erro ao salvar ordem no banco de dados: %s", str(exc))

    def sync_orders(self, client: OKXClient, symbol: str) -> None:
        """
        Synchronize local orders with exchange orders to ensure consistency.
        
        Args:
            client: Instance of OKXClient to interact with the exchange.
            symbol: Trading pair symbol to synchronize orders for.
        """
        try:
            exchange_orders = client.exchange.fetch_open_orders(symbol)
            local_orders = [o for o in self.db.get_orders(symbol) if o.get("status") != "closed"]

            for order in exchange_orders:
                if order["id"] not in [o["id"] for o in local_orders]:
                    try:
                        self.db.save_order(order)
                    except Exception as e:
                        self.logger.error(
                            "Erro ao adicionar ordem %s ao banco de dados: %s",
                            order.get("id", "N/A"),
                            str(e),
                        )

            self.logger.debug("Ordens sincronizadas para %s", symbol)
        except Exception as exc:
            self.logger.error("Erro ao sincronizar ordens para %s: %s", symbol, str(exc))
            raise

    async def check_closed_orders(self, client: OKXClient, symbol: str, strategy_type: str = "OCO") -> None:
        """
        Check for closed orders and send notifications if not already notified.
        
        Args:
            client: Instance of OKXClient to fetch closed orders.
            symbol: Trading pair symbol to check orders for.
            strategy_type: Type of strategy for notification formatting (e.g., 'OCO', 'TRAIL', 'SWING').
        """
        try:
            closed_orders = client.exchange.fetch_closed_orders(symbol, limit=5)
            # Update cache with closed orders
            open_orders = client.exchange.fetch_open_orders(symbol)
            self.order_cache.update_orders(symbol, open_orders, closed_orders)
            # Synchronize orders with local database
            self.sync_orders(client, symbol)

            for order in closed_orders:
                order_id = order.get("id", "N/A")
                # Check if order has already been notified to avoid duplicates
                if not self.db.is_order_notified(order_id):
                    if order.get("side") == "sell" and order.get("status") == "closed":
                        buy_order = self.db.get_matching_buy_order(order_id, symbol)
                        if buy_order:
                            try:
                                buy_price = float(buy_order.get("price", 0.0) or 0.0)
                                sell_price = float(order.get("price", 0.0) or 0.0)
                                amount = min(
                                    float(buy_order.get("amount", 0.0) or 0.0),
                                    float(order.get("amount", 0.0) or 0.0),
                                )
                                pnl = (sell_price - buy_price) * amount
                                # Update PnL in database for the sell order
                                order_with_pnl = order.copy()
                                order_with_pnl["pnl"] = pnl
                                self.save_order_to_db(order_with_pnl)
                                # Log to trade history
                                trade_record = {
                                    "symbol": symbol,
                                    "buy_order_id": buy_order.get("id"),
                                    "sell_order_id": order_id,
                                    "buy_price": buy_price,
                                    "sell_price": sell_price,
                                    "amount": amount,
                                    "pnl": pnl,
                                    "open_timestamp": buy_order.get("timestamp"),
                                }
                                with sqlite3.connect(self.db.db_path) as conn:
                                    cursor = conn.cursor()
                                    cursor.execute(
                                        """
                                        INSERT INTO trade_history 
                                        (symbol, buy_order_id, sell_order_id, buy_price, sell_price, amount, pnl, open_timestamp)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                                        """,
                                        (
                                            trade_record["symbol"],
                                            trade_record["buy_order_id"],
                                            trade_record["sell_order_id"],
                                            trade_record["buy_price"],
                                            trade_record["sell_price"],
                                            trade_record["amount"],
                                            trade_record["pnl"],
                                            trade_record["open_timestamp"],
                                        ),
                                    )
                                    conn.commit()
                                    self.logger.debug(
                                        "Registro de trade salvo no histórico: %s", order_id
                                    )

                                if self.metrics_calculator:
                                    try:
                                        metrics = self.metrics_calculator.get_trade_metrics(
                                            symbol, buy_price, sell_price, amount
                                        )
                                        win_rate = metrics.get("win_rate", 0.0)
                                        message = f"""📉 *{strategy_type.upper()} \\- POSIÇÃO FECHADA \\- {symbol}*
• *Bot*: {self.config.SCRIPT_NAME}
• *Tipo*: Venda
• *Quantidade*: {amount}
• *Preço de Compra*: ${buy_price:.2f}
• *Preço de Venda*: ${sell_price:.2f}
• *PnL*: ${pnl:.2f}
• *Taxa de Acerto*: {win_rate*100:.1f}%"""
                                    except Exception as e:
                                        self.logger.error(f"Erro ao calcular métricas para {symbol}: {str(e)}")
                                        message = f"""📉 *{strategy_type.upper()} \\- POSIÇÃO FECHADA \\- {symbol}*
• *Bot*: {self.config.SCRIPT_NAME}
• *Tipo*: Venda
• *Quantidade*: {amount}
• *Preço de Compra*: ${buy_price:.2f}
• *Preço de Venda*: ${sell_price:.2f}
• *PnL*: ${pnl:.2f}
• *Nota*: Erro ao calcular métricas"""
                                else:
                                    message = f"""📉 *{strategy_type.upper()} \\- POSIÇÃO FECHADA \\- {symbol}*
• *Bot*: {self.config.SCRIPT_NAME}
• *Tipo*: Venda
• *Quantidade*: {amount}
• *Preço de Compra*: ${buy_price:.2f}
• *Preço de Venda*: ${sell_price:.2f}
• *PnL*: ${pnl:.2f}"""
                                await self.send_telegram_notification(message)
                                await self.play_alert(
                                    "win" if sell_price > buy_price else "loose",
                                    volume=0.7,
                                )
                                self.db.mark_order_as_notified(order_id)
                            except (ValueError, TypeError) as e:
                                self.logger.warning(
                                    "Erro ao processar ordem %s de %s: %s", order_id, symbol, str(e)
                                )
                                message = f"""📉 *{strategy_type.upper()} \\- POSIÇÃO FECHADA \\- {symbol}*
• *Bot*: {self.config.SCRIPT_NAME}
• *Tipo*: Venda
• *Quantidade*: {order.get("amount", 0.0)}
• *Preço de Venda*: ${order.get("price", 0.0) if order.get("price") else 0.0:.2f}
• *Nota*: Erro ao processar dados"""
                                await self.send_telegram_notification(message)
                                self.db.mark_order_as_notified(order_id)
                        else:
                            self.logger.warning(
                                "Ordem de venda sem correspondência de compra para %s: ID=%s", symbol, order_id
                            )
                            sell_price = float(order.get("price", 0.0) or 0.0)
                            message = f"""📉 *{strategy_type.upper()} \\- POSIÇÃO FECHADA \\- {symbol}*
• *Bot*: {self.config.SCRIPT_NAME}
• *Tipo*: Venda
• *Quantidade*: {order.get("amount", 0.0)}
• *Preço de Venda*: ${sell_price:.2f}
• *PnL*: N/A (Sem ordem de compra correspondente)
• *ID da Ordem*: {order_id}"""
                            await self.send_telegram_notification(message)
                            self.db.mark_order_as_notified(order_id)
                    elif order.get("status") == "closed" and order.get("side") == "buy":
                        message = f"""📈 *{strategy_type.upper()} \\- POSIÇÃO ABERTA \\- {symbol}*
• *Bot*: {self.config.SCRIPT_NAME}
• *Tipo*: Compra
• *Quantidade*: {order.get("amount", 0.0)}
• *Preço*: ${order.get("price", 0.0):.2f}"""
                        await self.send_telegram_notification(message)
                        self.db.mark_order_as_notified(order_id)
        except Exception as exc:
            self.logger.error("Erro ao verificar ordens fechadas para %s: %s", symbol, str(exc))

    async def run_trading_loop(self, client: OKXClient, indicator: TechnicalIndicator, 
                              signal_checker: Any, order_manager: Any, strategy_type: str) -> None:
        """
        Core trading loop for the bot. This method should be called by derived classes.
        
        Args:
            client: Instance of OKXClient for exchange interactions.
            indicator: TechnicalIndicator instance for calculating indicators.
            signal_checker: Object for checking entry signals specific to the strategy.
            order_manager: Object for managing order placement specific to the strategy.
            strategy_type: Type of strategy for notifications (e.g., 'OCO', 'TRAIL', 'SWING').
        """
        self.logger.debug("Starting trading loop for strategy: %s", strategy_type)
        cycle_count = 0

        def handle_sigstp(signum, frame):
            self.logger.info("Bot interrompido manualmente pelo usuário (SIGTSTP).")
            print("\n🛑 Bot interrompido manualmente (Ctrl+Z).")
            print("A finalizar o bot. Até breve!")
            exit(0)

        signal.signal(signal.SIGTSTP, handle_sigstp)

        while True:
            cycle_count += 1
            if cycle_count == 1:
                client._print_mode_message()
            print(f"\n🔄 Ciclo #{cycle_count} - {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")

            # Check/reset daily loss tracking at start of new day
            current_date = pd.Timestamp.now().date()
            if (self.daily_starting_balance is None or 
                self.daily_starting_balance[0] != current_date):
                balance = client.get_balance()
                total_balance = sum(balance["total"].values())
                self.daily_starting_balance = (current_date, total_balance)
                self.daily_max_loss = 0
                self.logger.info(f"Reset daily tracking - Starting balance: ${total_balance:.2f}")

            client.display_prices_with_precision()
            client.display_balance_with_precision()

            # Check daily loss limit
            current_balance = sum(client.get_balance()["total"].values())
            daily_loss = (self.daily_starting_balance[1] - current_balance) / self.daily_starting_balance[1] if self.daily_starting_balance[1] > 0 else 0
            self.daily_max_loss = max(self.daily_max_loss, daily_loss)

            self.logger.info(
                f"Daily balance check - Starting Balance: ${self.daily_starting_balance[1]:.2f}, "
                f"Current Balance: ${current_balance:.2f}, "
                f"Change: {daily_loss*100:.1f}%"
            )

            if daily_loss >= self.daily_loss_limit:
                self.logger.warning(
                    f"Daily loss limit reached! Current loss: {daily_loss*100:.1f}% "
                    f"(Max allowed: {self.daily_loss_limit*100:.0f}%)"
                )
                await self.send_telegram_notification(
                    f"⚠️ *DAILY LOSS LIMIT REACHED*\n"
                    f"• Current Loss: {daily_loss*100:.1f}%\n"
                    f"• Starting Balance: ${self.daily_starting_balance[1]:.2f}\n"
                    f"• Current Balance: ${current_balance:.2f}\n"
                    f"• Max Allowed: {self.daily_loss_limit*100:.0f}%\n"
                    f"• Trading paused until next day"
                )
                await self.play_alert("error", volume=0.8)
                # Skip trading for the rest of the day
                await asyncio.sleep(86400)  # Sleep for 24 hours
                continue

            # Check closed orders and send notifications
            for symbol in self.config.TRADING_SYMBOLS:
                await self.check_closed_orders(client, symbol, strategy_type)

            # Strategy-specific trading logic (to be implemented by derived classes)
            await self._execute_strategy(client, indicator, signal_checker, order_manager, strategy_type)

            print(f"\n⏰ Aguardando próximo ciclo (60 segundos)...")
            await asyncio.sleep(60)

    async def _execute_strategy(self, client: OKXClient, indicator: TechnicalIndicator, 
                               signal_checker: Any, order_manager: Any, strategy_type: str) -> None:
        """
        Placeholder for strategy-specific trading logic. Derived classes must implement this method.
        
        Args:
            client: Instance of OKXClient for exchange interactions.
            indicator: TechnicalIndicator instance for calculating indicators.
            signal_checker: Object for checking entry signals specific to the strategy.
            order_manager: Object for managing order placement specific to the strategy.
            strategy_type: Type of strategy for notifications.
        """
        raise NotImplementedError("Derived classes must implement _execute_strategy method")


async def initialize_bot_components(strategy: str) -> Tuple[OKXClient, Any, 'TradingBotBase']:
    """
    Initialize bot components with the specified strategy.
    
    Args:
        strategy: Strategy identifier for configuration (e.g., 'OCO_wma_rsi', 'signals_wma_rsi', 'swing').
    
    Returns:
        Tuple of (OKXClient, OrderManager, TradingBot) instances.
    """
    try:
        config = BotConfig(strategy=strategy)
        client = OKXClient(config)
        # Ensure TRADING_SYMBOLS from config are used in client
        client.config.TRADING_SYMBOLS = config.TRADING_SYMBOLS
        # Order manager and bot instances to be set by derived strategy
        order_manager = None
        bot = TradingBotBase(config)
        return client, order_manager, bot
    except Exception as exc:
        logger = TradingLogger.get_logger(__name__)
        logger.error("Falha ao inicializar componentes do bot: %s", str(exc))
        raise ConfigurationError("Falha na inicialização do bot") from exc


async def send_startup_notification(client: OKXClient, bot: 'TradingBotBase') -> None:
    """
    Send startup notification for the bot.
    
    Args:
        client: Instance of OKXClient to fetch balance.
        bot: Instance of TradingBotBase for sending notifications.
    """
    if not bot.config.ENABLE_TELEGRAM_NOTIFICATIONS:
        return
    try:
        balance = client.get_balance()
        message = await build_startup_message(balance, bot.config)
        await bot.send_telegram_notification(message)
    except Exception as exc:
        logger = TradingLogger.get_logger(__name__)
        logger.error("Erro ao enviar notificação de inicialização: %s", str(exc))
