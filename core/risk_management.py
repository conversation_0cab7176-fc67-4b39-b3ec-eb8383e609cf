"""
Módulo de gestão de risco para trading automatizado.
Contém classes especializadas para cálculo de volatilidade, tamanho de posição,
níveis de stop loss/take profit e gerenciamento de risco geral.
"""

import time
import numpy as np
import talib
from typing import Dict, List, Optional, Tuple, Any
from utils.logger import TradingLogger


class VolatilityCalculator:
    """Calculadora otimizada de volatilidade com cache para melhor performance."""

    def __init__(self, lookback_periods: int = 100, cache_ttl: int = 300):
        self.lookback_periods = lookback_periods
        self.cache_ttl = cache_ttl  # 5 minutos de TTL
        self._cache = {}
        self._cache_timestamps = {}
        self.logger = TradingLogger.get_logger(__name__)

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Verifica se a entrada do cache ainda é válida."""
        if cache_key not in self._cache_timestamps:
            return False
        return time.time() - self._cache_timestamps[cache_key] < self.cache_ttl

    def _update_cache(self, cache_key: str, value: float):
        """Atualiza o cache com novo valor."""
        self._cache[cache_key] = value
        self._cache_timestamps[cache_key] = time.time()

    def calculate_weighted_volatility(self, symbol: str, closes: np.ndarray, atr_values: np.ndarray = None) -> Dict[str, float]:
        """
        Calcular volatilidade ponderada de forma mais eficiente.

        Args:
            symbol: Símbolo do par de trading
            closes: Array de preços de fechamento
            atr_values: Array de valores ATR (opcional)

        Returns:
            Dict com diferentes medidas de volatilidade
        """
        cache_key = f"vol_{symbol}_{len(closes)}_{hash(closes.tobytes())}"

        if self._is_cache_valid(cache_key):
            self.logger.debug(f"Usando volatilidade do cache para {symbol}")
            return self._cache[cache_key]

        try:
            # 1. Volatilidade histórica (desvio padrão dos retornos)
            returns = np.diff(closes) / closes[:-1]
            hist_volatility = np.std(returns) * np.sqrt(365)

            # 2. Volatilidade baseada em ATR se fornecida
            atr_volatility = 0.05  # Default
            if atr_values is not None and len(atr_values) >= 30:
                atr_volatility = np.mean(atr_values[-30:]) / closes[-1]

            # 3. Volatilidade ponderada (70% histórica + 30% ATR)
            weighted_vol = 0.7 * hist_volatility + 0.3 * atr_volatility

            # 4. Volatilidade de curto prazo (últimos 20 períodos)
            short_term_returns = returns[-20:] if len(returns) >= 20 else returns
            short_term_vol = np.std(short_term_returns) * np.sqrt(365)

            result = {
                'historical': hist_volatility,
                'atr_based': atr_volatility,
                'weighted': weighted_vol,
                'short_term': short_term_vol
            }

            self._update_cache(cache_key, result)
            self.logger.debug(f"Volatilidade calculada e armazenada no cache para {symbol}")
            return result

        except Exception as e:
            self.logger.error(f"Erro ao calcular volatilidade para {symbol}: {e}")
            return {
                'historical': 0.05,
                'atr_based': 0.05,
                'weighted': 0.05,
                'short_term': 0.05
            }

    def calculate_correlation_factor(self, symbol: str, returns: np.ndarray, btc_returns: np.ndarray) -> float:
        """
        Calcular fator de correlação com BTC de forma otimizada.

        Args:
            symbol: Símbolo do par
            returns: Retornos do símbolo
            btc_returns: Retornos do BTC

        Returns:
            Fator de correlação ajustado
        """
        cache_key = f"corr_{symbol}_{len(returns)}_{hash(returns.tobytes())}"

        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]

        try:
            if symbol == "BTC/USDC":
                correlation_factor = 1.0
            else:
                # Ajustar tamanhos dos arrays
                min_len = min(len(returns), len(btc_returns))
                if min_len < 10:  # Dados insuficientes
                    correlation_factor = 1.0
                else:
                    corr_matrix = np.corrcoef(returns[:min_len], btc_returns[:min_len])
                    correlation = corr_matrix[0, 1] if not np.isnan(corr_matrix[0, 1]) else 0
                    # Limitar fator de correlação entre 0.5 e 1.5
                    correlation_factor = min(1.5, max(0.5, 1 + correlation))

            self._update_cache(cache_key, correlation_factor)
            return correlation_factor

        except Exception as e:
            self.logger.error(f"Erro ao calcular correlação para {symbol}: {e}")
            return 1.0

    def cleanup_expired_cache(self):
        """Remove entradas expiradas do cache."""
        current_time = time.time()
        expired_keys = [
            key for key, timestamp in self._cache_timestamps.items()
            if current_time - timestamp > self.cache_ttl
        ]

        for key in expired_keys:
            self._cache.pop(key, None)
            self._cache_timestamps.pop(key, None)

        if expired_keys:
            self.logger.debug(f"Removidas {len(expired_keys)} entradas expiradas do cache de volatilidade")


class PositionSizeCalculator:
    """Calculadora otimizada de tamanho de posição baseada em risco."""

    def __init__(self, config):
        self.config = config
        self.logger = TradingLogger.get_logger(__name__)

    def calculate_dynamic_position_size(
        self,
        volatility_metrics: Dict[str, float],
        btc_correlation: float,
        max_risk_per_trade: float = 0.02,
        min_position_size: float = 0.01
    ) -> float:
        """
        Calcula tamanho de posição dinâmico baseado em volatilidade e correlação.

        Args:
            volatility_metrics: Métricas de volatilidade calculadas
            btc_correlation: Fator de correlação com BTC
            max_risk_per_trade: Risco máximo por trade (2%)
            min_position_size: Tamanho mínimo de posição (1%)

        Returns:
            Tamanho de posição calculado
        """
        try:
            weighted_vol = volatility_metrics.get('weighted', 0.05)

            # Cálculo final do tamanho da posição
            risk_adjusted_size = min(
                (max_risk_per_trade / (weighted_vol * btc_correlation + 0.0001)),
                getattr(self.config, 'POSITION_SIZE_MAX', 0.15)
            )
            position_size = max(risk_adjusted_size, min_position_size)

            self.logger.info(
                f"Risco calculado:\n"
                f"- Hist Vol: {volatility_metrics.get('historical', 0):.4f}\n"
                f"- ATR Vol: {volatility_metrics.get('atr_based', 0):.4f}\n"
                f"- Weighted: {weighted_vol:.4f}\n"
                f"- BTC Corr: {btc_correlation:.2f}\n"
                f"- Pos Size: {position_size:.2%}"
            )

            return position_size

        except Exception as e:
            self.logger.error(f"Erro ao calcular tamanho de posição: {e}")
            return getattr(self.config, 'POSITION_SIZE_BASE', 0.1)

    def calculate_volatility_adjusted_position(
        self,
        symbol: str,
        atr: float,
        atr_mean: float,
        base_position_size: float = 0.1
    ) -> float:
        """
        Ajusta tamanho de posição baseado na volatilidade atual vs. histórica.

        Args:
            symbol: Símbolo do par
            atr: ATR atual
            atr_mean: ATR médio histórico
            base_position_size: Tamanho base de posição

        Returns:
            Tamanho de posição ajustado
        """
        try:
            volatility_ratio = atr / atr_mean if atr_mean > 0 else 1.0

            high_threshold = getattr(self.config, "VOLATILITY_THRESHOLD_HIGH", 1.2)
            low_threshold = getattr(self.config, "VOLATILITY_THRESHOLD_LOW", 0.8)

            if volatility_ratio > high_threshold:
                position_size = getattr(self.config, "POSITION_SIZE_MIN", 0.05)
                self.logger.info(
                    f"Alta volatilidade detectada para {symbol}, "
                    f"reduzindo tamanho da posição para {position_size * 100:.1f}%"
                )
            elif volatility_ratio < low_threshold:
                position_size = getattr(self.config, "POSITION_SIZE_MAX", 0.15)
                self.logger.info(
                    f"Baixa volatilidade detectada para {symbol}, "
                    f"aumentando tamanho da posição para {position_size * 100:.1f}%"
                )
            else:
                position_size = base_position_size
                self.logger.info(
                    f"Volatilidade normal para {symbol}, "
                    f"tamanho da posição padrão {position_size * 100:.1f}%"
                )

            return position_size

        except Exception as e:
            self.logger.error(f"Erro ao ajustar posição por volatilidade: {e}")
            return base_position_size


class ATRCalculator:
    """Calculadora otimizada de ATR com cache para múltiplos períodos."""

    def __init__(self, cache_ttl: int = 300):
        self.cache_ttl = cache_ttl
        self._cache = {}
        self._cache_timestamps = {}
        self.logger = TradingLogger.get_logger(__name__)

    def calculate_atr_series(self, ohlcv_data: List, period: int = 14) -> np.ndarray:
        """
        Calcula série de ATR de forma otimizada.

        Args:
            ohlcv_data: Dados OHLCV
            period: Período para ATR

        Returns:
            Array de valores ATR
        """
        cache_key = f"atr_series_{len(ohlcv_data)}_{period}_{hash(str(ohlcv_data[-1]))}"

        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]

        try:
            highs = np.array([x[2] for x in ohlcv_data])
            lows = np.array([x[3] for x in ohlcv_data])
            closes = np.array([x[4] for x in ohlcv_data])

            atr_values = talib.ATR(highs, lows, closes, timeperiod=period)

            self._update_cache(cache_key, atr_values)
            return atr_values

        except Exception as e:
            self.logger.error(f"Erro ao calcular série ATR: {e}")
            return np.array([])

    def calculate_atr_statistics(self, ohlcv_data: List, period: int = 14, lookback: int = 20) -> Dict[str, float]:
        """
        Calcula estatísticas de ATR de forma otimizada.

        Args:
            ohlcv_data: Dados OHLCV
            period: Período para ATR
            lookback: Períodos para média histórica

        Returns:
            Dict com estatísticas de ATR
        """
        try:
            atr_values = self.calculate_atr_series(ohlcv_data, period)

            if len(atr_values) == 0:
                return {'current': 0.0, 'mean': 0.0, 'ratio': 1.0}

            current_atr = atr_values[-1] if not np.isnan(atr_values[-1]) else 0.0

            # Calcular média dos últimos períodos
            valid_values = atr_values[~np.isnan(atr_values)]
            if len(valid_values) >= lookback:
                atr_mean = np.mean(valid_values[-lookback:])
            else:
                atr_mean = np.mean(valid_values) if len(valid_values) > 0 else current_atr

            volatility_ratio = current_atr / atr_mean if atr_mean > 0 else 1.0

            return {
                'current': float(current_atr),
                'mean': float(atr_mean),
                'ratio': float(volatility_ratio)
            }

        except Exception as e:
            self.logger.error(f"Erro ao calcular estatísticas ATR: {e}")
            return {'current': 0.0, 'mean': 0.0, 'ratio': 1.0}

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Verifica se a entrada do cache ainda é válida."""
        if cache_key not in self._cache_timestamps:
            return False
        return time.time() - self._cache_timestamps[cache_key] < self.cache_ttl

    def _update_cache(self, cache_key: str, value):
        """Atualiza o cache com novo valor."""
        self._cache[cache_key] = value
        self._cache_timestamps[cache_key] = time.time()

    def cleanup_expired_cache(self):
        """Remove entradas expiradas do cache."""
        current_time = time.time()
        expired_keys = [
            key for key, timestamp in self._cache_timestamps.items()
            if current_time - timestamp > self.cache_ttl
        ]

        for key in expired_keys:
            self._cache.pop(key, None)
            self._cache_timestamps.pop(key, None)

        if expired_keys:
            self.logger.debug(f"Removidas {len(expired_keys)} entradas expiradas do cache ATR")


class MarketDataProcessor:
    """Processador otimizado de dados de mercado para validação de tendência."""

    def __init__(self, client):
        self.client = client
        self.logger = TradingLogger.get_logger(__name__)

    def prepare_market_data(
        self,
        symbol: str,
        indicator: 'TechnicalIndicator',
        timeframe: str = "15m",
        tickers: Optional[Dict] = None
    ) -> Optional[Dict]:
        """
        Prepara dados de mercado otimizados para validação de tendência.

        Args:
            symbol: Símbolo do par de trading
            indicator: Instância do TechnicalIndicator
            timeframe: Timeframe para cálculos
            tickers: Dados bulk de tickers (opcional)

        Returns:
            Dict com dados de mercado processados ou None se erro
        """
        try:
            # Obter preço atual usando dados bulk ou cache
            if tickers and symbol in tickers:
                ticker = tickers[symbol]
                current_close = ticker.get("last", 0.0)
            else:
                ticker = self.client.get_ticker(symbol)
                current_close = ticker.get("last", 0.0) if ticker else 0.0

            if current_close <= 0.0:
                self.logger.error(f"Preço atual inválido para {symbol}")
                return None

            # Calcular indicadores usando cache
            wma_period = getattr(self.client.config, 'WMA_PERIOD', 20)
            rsi_period = getattr(self.client.config, 'RSI_PERIOD', 14)

            wma = indicator.calculate_wma(symbol, timeframe=timeframe, period=wma_period)
            rsi = indicator.calculate_rsi(symbol, timeframe=timeframe, period=rsi_period)

            # Validar indicadores
            if wma is None or rsi is None:
                self.logger.error(f"Não foi possível calcular indicadores para {symbol}")
                return None

            # Garantir que são valores únicos (não listas)
            current_wma = wma if isinstance(wma, (int, float)) else wma[-1]
            current_rsi = rsi if isinstance(rsi, (int, float)) else rsi[-1]

            # Calcular tendência WMA se temos dados históricos
            wma_trend_up = False
            if isinstance(wma, list) and len(wma) >= 3:
                wma_trend_up = wma[-1] > wma[-2] and wma[-2] > wma[-3]

            # Preparar dados estruturados
            market_data = {
                'close': float(current_close),
                'wma': float(current_wma),
                'rsi': float(current_rsi),
                'wma_trend_up': wma_trend_up,
                'timestamp': time.time()
            }

            self.logger.debug(f"Dados de mercado preparados para {symbol}: {market_data}")
            return market_data

        except Exception as e:
            self.logger.error(f"Erro ao preparar dados de mercado para {symbol}: {e}")
            return None

    def calculate_wma_trend(self, wma_values: List[float], periods: int = 3) -> bool:
        """
        Calcula se a WMA está em tendência de alta.

        Args:
            wma_values: Lista de valores WMA
            periods: Número de períodos para comparação

        Returns:
            True se WMA está em tendência de alta
        """
        if len(wma_values) < periods:
            return False

        # Verificar se cada valor é maior que o anterior
        for i in range(len(wma_values) - periods + 1, len(wma_values)):
            if wma_values[i] <= wma_values[i - 1]:
                return False

        return True


class TrendValidator:
    """Validador otimizado de tendências com cache de decisões."""

    def __init__(self, config):
        self.config = config
        self.logger = TradingLogger.get_logger(__name__)
        self._decision_cache = {}
        self._cache_timestamps = {}
        self._cache_ttl = 30  # 30 segundos para decisões de tendência

    def validate_with_cache(self, symbol: str, market_data: Dict) -> bool:
        """
        Valida tendência com cache de decisões para evitar recálculos.

        Args:
            symbol: Símbolo do par
            market_data: Dados de mercado

        Returns:
            True se tendência favorável
        """
        # Criar chave de cache baseada nos dados
        cache_key = f"trend_{symbol}_{market_data['close']:.2f}_{market_data['wma']:.2f}_{market_data['rsi']:.2f}"

        # Verificar cache
        if self._is_cache_valid(cache_key):
            self.logger.debug(f"Usando decisão de tendência do cache para {symbol}")
            return self._decision_cache[cache_key]

        # Calcular nova decisão
        result = self._validate_trend_logic(symbol, market_data)

        # Armazenar no cache
        self._update_cache(cache_key, result)

        return result

    def _validate_trend_logic(self, symbol: str, market_data: Dict) -> bool:
        """Lógica principal de validação de tendência."""
        try:
            current_close = market_data['close']
            current_wma = market_data['wma']
            current_rsi = market_data['rsi']
            wma_trend_up = market_data.get('wma_trend_up', False)

            # Modo estrito - verificação rápida
            if not getattr(self.config, 'ENABLE_FLEXIBLE_ENTRY', True):
                return current_close > current_wma and current_rsi > 50

            # Cálculos otimizados
            price_vs_wma_pct = ((current_close - current_wma) / current_wma) * 100
            wma_tolerance_pct = getattr(self.config, 'WMA_TOLERANCE_PCT', 0.5)
            rsi_min_threshold = getattr(self.config, 'RSI_MIN_THRESHOLD', 45)

            # Condições otimizadas com early return
            conditions = [
                (current_close > current_wma and current_rsi > 50, "traditional_bullish"),
                (abs(price_vs_wma_pct) <= wma_tolerance_pct and wma_trend_up, "wma_trend_breakout"),
                (current_rsi >= rsi_min_threshold and price_vs_wma_pct > -1.0, "rsi_support"),
                (current_rsi > 60 and price_vs_wma_pct > -0.75, "rsi_momentum")
            ]

            for condition, reason in conditions:
                if condition:
                    self.logger.info(f"✅ Trade approved for {symbol} - Reason: {reason}")
                    return True

            self.logger.info(f"❌ Trade rejected for {symbol} - No conditions met")
            return False

        except Exception as e:
            self.logger.error(f"Erro na validação de tendência para {symbol}: {e}")
            return False

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Verifica se decisão em cache ainda é válida."""
        if cache_key not in self._cache_timestamps:
            return False
        return time.time() - self._cache_timestamps[cache_key] < self._cache_ttl

    def _update_cache(self, cache_key: str, value: bool):
        """Atualiza cache de decisões."""
        self._decision_cache[cache_key] = value
        self._cache_timestamps[cache_key] = time.time()

    def cleanup_expired_cache(self):
        """Remove decisões expiradas do cache."""
        current_time = time.time()
        expired_keys = [
            key for key, timestamp in self._cache_timestamps.items()
            if current_time - timestamp > self._cache_ttl
        ]

        for key in expired_keys:
            self._decision_cache.pop(key, None)
            self._cache_timestamps.pop(key, None)

        if expired_keys:
            self.logger.debug(f"Removidas {len(expired_keys)} decisões expiradas do cache")


class PositionSizer:
    """Calculadora otimizada de tamanho de posição com cache e configuração flexível."""

    def __init__(self, config):
        self.config = config
        self.logger = TradingLogger.get_logger(__name__)
        self.volatility_cache = {}
        self.position_cache = {}
        self.cache_timestamps = {}
        self.cache_ttl = 120  # 2 minutos para decisões de posição

    def calculate_position_size(self, symbol: str, volatility_data: Dict, balance_data: Dict = None) -> Dict[str, float]:
        """
        Cálculo otimizado de tamanho de posição com cache.

        Args:
            symbol: Símbolo do par de trading
            volatility_data: Dados de volatilidade pré-calculados
            balance_data: Dados de saldo (opcional)

        Returns:
            Dict com informações de posição calculadas
        """
        # Verificar cache primeiro
        cache_key = f"pos_{symbol}_{volatility_data.get('weighted', 0):.4f}"

        if self._is_cache_valid(cache_key):
            self.logger.debug(f"Usando tamanho de posição do cache para {symbol}")
            return self.position_cache[cache_key]

        try:
            # Verificar se position sizing dinâmico está habilitado
            if not getattr(self.config, 'ENABLE_DYNAMIC_POSITION_SIZING', False):
                default_size = getattr(self.config, 'DEFAULT_POSITION_SIZE', 0.1)
                result = {
                    'position_size': default_size,
                    'risk_level': 'fixed',
                    'volatility_adjusted': False,
                    'reason': 'dynamic_sizing_disabled'
                }
                self._update_cache(cache_key, result)
                return result

            # Usar dados pré-calculados de volatilidade
            weighted_vol = volatility_data.get('weighted', 0.05)
            btc_corr = volatility_data.get('btc_correlation', 1.0)
            atr_vol = volatility_data.get('atr_based', 0.05)
            hist_vol = volatility_data.get('historical', 0.05)

            # Parâmetros de risco configuráveis
            max_risk_per_trade = getattr(self.config, 'MAX_RISK_PER_TRADE', 0.02)
            min_position_size = getattr(self.config, 'MIN_POSITION_SIZE', 0.01)
            max_position_size = getattr(self.config, 'POSITION_SIZE_MAX', 0.15)

            # Cálculo principal de tamanho de posição
            risk_adjusted_size = min(
                (max_risk_per_trade / (weighted_vol * btc_corr + 0.0001)),
                max_position_size
            )

            base_position_size = max(risk_adjusted_size, min_position_size)

            # Ajustes adicionais baseados em volatilidade
            volatility_adjustment = self._calculate_volatility_adjustment(
                weighted_vol, hist_vol, atr_vol
            )

            final_position_size = base_position_size * volatility_adjustment
            final_position_size = max(min_position_size, min(final_position_size, max_position_size))

            # Determinar nível de risco
            risk_level = self._determine_risk_level(weighted_vol, btc_corr)

            result = {
                'position_size': final_position_size,
                'base_size': base_position_size,
                'volatility_adjustment': volatility_adjustment,
                'risk_level': risk_level,
                'weighted_volatility': weighted_vol,
                'btc_correlation': btc_corr,
                'volatility_adjusted': True,
                'reason': 'dynamic_calculation'
            }

            self.logger.info(
                f"Position sizing for {symbol}:\n"
                f"  • Base Size: {base_position_size:.2%}\n"
                f"  • Volatility Adj: {volatility_adjustment:.2f}x\n"
                f"  • Final Size: {final_position_size:.2%}\n"
                f"  • Risk Level: {risk_level}\n"
                f"  • Weighted Vol: {weighted_vol:.4f}\n"
                f"  • BTC Corr: {btc_corr:.2f}"
            )

            self._update_cache(cache_key, result)
            return result

        except Exception as e:
            self.logger.error(f"Erro ao calcular tamanho de posição para {symbol}: {e}")
            # Fallback para tamanho padrão
            fallback_result = {
                'position_size': getattr(self.config, 'DEFAULT_POSITION_SIZE', 0.1),
                'risk_level': 'fallback',
                'volatility_adjusted': False,
                'reason': 'calculation_error'
            }
            return fallback_result

    def _calculate_volatility_adjustment(self, weighted_vol: float, hist_vol: float, atr_vol: float) -> float:
        """
        Calcula ajuste de volatilidade para o tamanho da posição.

        Args:
            weighted_vol: Volatilidade ponderada
            hist_vol: Volatilidade histórica
            atr_vol: Volatilidade baseada em ATR

        Returns:
            Fator de ajuste (0.5 a 1.5)
        """
        try:
            # Volatilidade de referência (média do mercado)
            reference_vol = 0.05  # 5% como referência

            # Calcular desvio da volatilidade de referência
            vol_ratio = weighted_vol / reference_vol

            # Ajuste baseado na volatilidade
            if vol_ratio > 1.5:  # Alta volatilidade
                adjustment = 0.6  # Reduzir posição
            elif vol_ratio > 1.2:  # Volatilidade moderadamente alta
                adjustment = 0.8
            elif vol_ratio < 0.7:  # Baixa volatilidade
                adjustment = 1.3  # Aumentar posição
            elif vol_ratio < 0.9:  # Volatilidade moderadamente baixa
                adjustment = 1.1
            else:  # Volatilidade normal
                adjustment = 1.0

            # Limitar ajuste entre 0.5 e 1.5
            return max(0.5, min(1.5, adjustment))

        except Exception as e:
            self.logger.error(f"Erro ao calcular ajuste de volatilidade: {e}")
            return 1.0

    def _determine_risk_level(self, weighted_vol: float, btc_corr: float) -> str:
        """
        Determina o nível de risco baseado em volatilidade e correlação.

        Args:
            weighted_vol: Volatilidade ponderada
            btc_corr: Correlação com BTC

        Returns:
            Nível de risco: 'low', 'medium', 'high', 'very_high'
        """
        try:
            # Calcular score de risco
            risk_score = weighted_vol * btc_corr

            if risk_score > 0.08:
                return 'very_high'
            elif risk_score > 0.06:
                return 'high'
            elif risk_score > 0.04:
                return 'medium'
            else:
                return 'low'

        except Exception as e:
            self.logger.error(f"Erro ao determinar nível de risco: {e}")
            return 'medium'

    def calculate_amount_from_balance(self, position_size: float, balance: float, entry_price: float) -> Dict[str, float]:
        """
        Calcula quantidade da ordem baseada no saldo e tamanho de posição.

        Args:
            position_size: Tamanho da posição (percentual)
            balance: Saldo disponível
            entry_price: Preço de entrada

        Returns:
            Dict com informações da quantidade calculada
        """
        try:
            balance_to_use = balance * position_size
            amount = balance_to_use / entry_price

            return {
                'amount': amount,
                'balance_used': balance_to_use,
                'position_percentage': position_size * 100,
                'entry_price': entry_price,
                'total_value': amount * entry_price
            }

        except Exception as e:
            self.logger.error(f"Erro ao calcular quantidade da ordem: {e}")
            return {
                'amount': 0.0,
                'balance_used': 0.0,
                'position_percentage': 0.0,
                'entry_price': entry_price,
                'total_value': 0.0
            }

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Verifica se entrada do cache ainda é válida."""
        if cache_key not in self.cache_timestamps:
            return False
        return time.time() - self.cache_timestamps[cache_key] < self.cache_ttl

    def _update_cache(self, cache_key: str, value: Dict):
        """Atualiza cache com novo valor."""
        self.position_cache[cache_key] = value
        self.cache_timestamps[cache_key] = time.time()

    def cleanup_expired_cache(self):
        """Remove entradas expiradas do cache."""
        current_time = time.time()
        expired_keys = [
            key for key, timestamp in self.cache_timestamps.items()
            if current_time - timestamp > self.cache_ttl
        ]

        for key in expired_keys:
            self.position_cache.pop(key, None)
            self.cache_timestamps.pop(key, None)

        if expired_keys:
            self.logger.debug(f"Removidas {len(expired_keys)} entradas expiradas do cache de posições")


class RiskManager:
    """Gerenciador otimizado de risco para cálculos de SL/TP."""

    def __init__(self, config):
        self.config = config
        self.logger = TradingLogger.get_logger(__name__)
        self.risk_cache = {}
        self.cache_timestamps = {}
        self.cache_ttl = 180  # 3 minutos para cálculos de risco

    def calculate_sl_tp_levels(
        self,
        symbol: str,
        entry_price: float,
        atr: float,
        volatility_data: Dict = None
    ) -> Dict[str, float]:
        """
        Calcula níveis de Stop Loss e Take Profit otimizados.

        Args:
            symbol: Símbolo do par
            entry_price: Preço de entrada
            atr: Valor ATR atual
            volatility_data: Dados de volatilidade (opcional)

        Returns:
            Dict com níveis de SL/TP calculados
        """
        cache_key = f"risk_{symbol}_{entry_price:.2f}_{atr:.6f}"

        if self._is_cache_valid(cache_key):
            self.logger.debug(f"Usando níveis SL/TP do cache para {symbol}")
            return self.risk_cache[cache_key]

        try:
            # Parâmetros base configuráveis
            callback_ratio = getattr(self.config, "CALLBACK_RATIO", 2.75)
            base_sl_factor = getattr(self.config, "ATR_SL_FACTOR", 3.5)
            base_tp_factor = getattr(self.config, "ATR_TP_FACTOR", 4.75)

            # Ajustar multiplicadores baseado em volatilidade se disponível
            sl_factor, tp_factor = self._adjust_multipliers_for_volatility(
                base_sl_factor, base_tp_factor, volatility_data
            )

            # Calcular níveis
            sl_trigger = entry_price - (atr * callback_ratio)
            sl_price = sl_trigger
            tp_trigger = entry_price + (atr * callback_ratio)
            tp_price = tp_trigger

            # Calcular risk/reward ratio
            risk_amount = entry_price - sl_price
            reward_amount = tp_price - entry_price
            risk_reward_ratio = reward_amount / risk_amount if risk_amount > 0 else 0

            result = {
                'sl_trigger': sl_trigger,
                'sl_price': sl_price,
                'tp_trigger': tp_trigger,
                'tp_price': tp_price,
                'sl_factor': sl_factor,
                'tp_factor': tp_factor,
                'risk_reward_ratio': risk_reward_ratio,
                'atr_used': atr,
                'callback_ratio': callback_ratio
            }

            self.logger.info(
                f"Risk levels for {symbol}:\n"
                f"  • Entry: ${entry_price:.2f}\n"
                f"  • SL: ${sl_price:.2f} (Factor: {sl_factor:.2f})\n"
                f"  • TP: ${tp_price:.2f} (Factor: {tp_factor:.2f})\n"
                f"  • Risk/Reward: 1:{risk_reward_ratio:.2f}\n"
                f"  • ATR: {atr:.6f}"
            )

            self._update_cache(cache_key, result)
            return result

        except Exception as e:
            self.logger.error(f"Erro ao calcular níveis SL/TP para {symbol}: {e}")
            # Fallback para cálculo simples
            return {
                'sl_trigger': entry_price - (atr * 2.0),
                'sl_price': entry_price - (atr * 2.0),
                'tp_trigger': entry_price + (atr * 2.0),
                'tp_price': entry_price + (atr * 2.0),
                'sl_factor': 2.0,
                'tp_factor': 2.0,
                'risk_reward_ratio': 1.0,
                'atr_used': atr,
                'callback_ratio': 2.0
            }

    def _adjust_multipliers_for_volatility(
        self,
        base_sl_factor: float,
        base_tp_factor: float,
        volatility_data: Dict = None
    ) -> Tuple[float, float]:
        """
        Ajusta multiplicadores de SL/TP baseado em volatilidade.

        Args:
            base_sl_factor: Fator base para SL
            base_tp_factor: Fator base para TP
            volatility_data: Dados de volatilidade

        Returns:
            Tuple com fatores ajustados (sl_factor, tp_factor)
        """
        if not volatility_data:
            return base_sl_factor, base_tp_factor

        try:
            # Usar volatilidade ponderada para ajuste
            weighted_vol = volatility_data.get('weighted', 0.05)

            # Volatilidade de referência
            reference_vol = 0.05  # 5%
            vol_ratio = weighted_vol / reference_vol

            # Ajustar multiplicadores baseado na volatilidade
            if vol_ratio > 1.3:  # Alta volatilidade
                sl_factor = base_sl_factor * 1.2  # Aumentar SL
                tp_factor = base_tp_factor * 1.1  # Aumentar TP ligeiramente
            elif vol_ratio > 1.1:  # Volatilidade moderadamente alta
                sl_factor = base_sl_factor * 1.1
                tp_factor = base_tp_factor * 1.05
            elif vol_ratio < 0.7:  # Baixa volatilidade
                sl_factor = base_sl_factor * 0.8  # Reduzir SL
                tp_factor = base_tp_factor * 0.9  # Reduzir TP
            elif vol_ratio < 0.9:  # Volatilidade moderadamente baixa
                sl_factor = base_sl_factor * 0.9
                tp_factor = base_tp_factor * 0.95
            else:  # Volatilidade normal
                sl_factor = base_sl_factor
                tp_factor = base_tp_factor

            # Limitar fatores dentro de ranges razoáveis
            sl_factor = max(1.5, min(5.0, sl_factor))
            tp_factor = max(2.0, min(6.0, tp_factor))

            return sl_factor, tp_factor

        except Exception as e:
            self.logger.error(f"Erro ao ajustar multiplicadores: {e}")
            return base_sl_factor, base_tp_factor

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Verifica se entrada do cache ainda é válida."""
        if cache_key not in self.cache_timestamps:
            return False
        return time.time() - self.cache_timestamps[cache_key] < self.cache_ttl

    def _update_cache(self, cache_key: str, value: Dict):
        """Atualiza cache com novo valor."""
        self.risk_cache[cache_key] = value
        self.cache_timestamps[cache_key] = time.time()

    def cleanup_expired_cache(self):
        """Remove entradas expiradas do cache."""
        current_time = time.time()
        expired_keys = [
            key for key, timestamp in self.cache_timestamps.items()
            if current_time - timestamp > self.cache_ttl
        ]

        for key in expired_keys:
            self.risk_cache.pop(key, None)
            self.cache_timestamps.pop(key, None)

        if expired_keys:
            self.logger.debug(f"Removidas {len(expired_keys)} entradas expiradas do cache de risco")
