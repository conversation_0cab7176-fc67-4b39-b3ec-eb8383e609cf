Atua como um engenheiro de top da OKX, em equipa com o um trader profissional de sucesso e profitable e cria um projeto de trading automatizado.

#OKX-Trading-Bot
# projeto de trading automatizado.

## informações principais do projeto
— modelar e cada módulo servir vários scripts ou ser reutilizado
— um ficheiro .env com credenciais e configurações
— evitar chamadas desnecessárias à exchange - singleton?
— credenciais (telegram, live_okx_api, demo_okx_api,…binance) devem ser armazenadas em .env
- as ordens devem ser sempre que possível limit orders
— o bot deve ser capaz de operar em vários pares de moedas e em vários timeframes
- o bot deve esecutar as ordens com o melhor preço possível (bid/ask)
— notificações sonoras e telegram ao iniciar, terminar e realizar operações
— gravar detalhes das operações via SQLite, tabelas por script ? (associar compras limite com trailing stop, associar ordens filhas <PERSON>CO, etc…)
— operar apenas com risk-reward que justifique
— operar apenas 10% do capital em cada operação ou, risco calculado com base no ATR, o que for mais vantajoso
— uma classe base abstrata BaseStrategy em /strategies/base_strategy.py para padronizar métodos como execute, calc
— uma classe base abstrata BaseOrder em /strategies/base_order.py para padronizar métodos como execute, calc
— Use redis ou lru_cache para armazenar dados históricos frequentemente acessados, evitando chamadas repetitivas à exchange
— calcular stop loss e take profit ou trailing stop
A) com base no ATR
B) com base na variação média dos últimos 7 dias
C) últimos x candles
— calcular o risco de cada operação
— pasta data com historical - data_symbol_timeframe.csv
— pasta logs com logs de operações e logs de erros


## Scripts
— **strategy_market** - Col market orders
— **strategy_trailing** - main para realizar ordens com trailing_stop, com estratégia escolhida 
— **stragegy_oco** - com ordens OCO
— **fetch_data_exchange** - obter data da exchange - definida numa variável na class BotConfig, array de timeframes
— **open_orders** atualizados a cada 5 minutos
- **balance** a cada 5 minutos
— **dashboard** com ordens abertas e pnl atual, estatísticas gerais, gráfico de evolução do pnl, gráfico evolução do saldo,  estratégias mais lucrativas, analise por par  (streamlit, dash??) - resultados e análises dos últimos 30 dias
— **signals**, para analisar os symbols definidos no configuração em diferentes timeframe definidos na classe strategyAttr e resultados da análise dos signals/rsi_wma, signals/rsi_macd, …) e enviar notificações telegram quando detectar um sinal
ulate_position e log_trad
— **fetch_data_exchange.py**, implemente busca em lote para múltiplos símbolos/timeframes, reduzindo chamadas à exchange
- **database_manager** - para armazenar ordens detalhadas e estatísticas de operações
- **utils/logger.py** - com rotação e separação por tipo com cores
— **utils/error_handler.py** - geral para usar em todos os scripts
— **utils/decorators.py** - para decorar funções
— **utils/utils.py** - funções úteis


## Módulos:
— core/exchange_client.py conectar e inicializar exchange (buscar atributos na class StrategyAttr)
— service/service_sounds.py (sons na pasta sounds)
— service/service_telegram.py (credenciais em .env)
— service/sounds/…wav
— balance/_balance (obter saldos, formatar com precisão de acordo com exchange)
— balance/_print_balance .py
— orders/get_orders (regular, OCO, Trailing Stop)
— database/sandbox_orders.db
— database/live_orders.db
— database/_print_orders - com tabulate??
— utils/logger.py com rotação e separação por tipo com cores
— usar decoration, database…
— utils/error_handler.py geral para usar em todos os scripts
— signals/rsi_wma.py
— signals/Rsi_wma_optuna.py
— indicators/indicators.py (calculados com ta-lib, usando atributos definidos em config.json por grupos como nome igual ao signals ou estratégias)
— ai/sklearn
— ai/machine
— signals/top_50_signals (top 50 da CoinGecko analisadas pelos signals) 
— risk/risk_manager.py para calcular gestão de risco e valores a aplicar em nas operações

## Outras informações:
- Automatizar a criação de estratégias e sinais via templates CLI ou script

- Padroniza retorno de sinais como Dict[symbol: str, Dict[timeframe: str, SignalResult]]
 - Usa strategyAttr.json para parametrizar análise dos sinais.
- Estratégias tipo signals/rsi_wma devem importar os indicadores de indicators/.
- Otimização via Optuna: isola parâmetros otimizáveis em ficheiros YAML e regista os trials.
- testes/fixtures_database_xxx


## bibliotecas python
- pandas
- ta-lib
- scikit-learn
- optuna
- streamlit
- redis
- lru_cache
- tabulate
- colorama
- python-dotenv
- python-telegram-bot
- outras