import asyncio
import ccxt
import requests
import numpy as np
import talib
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import json
import time
from indicators.indicators import TechnicalIndicator
from service.service_telegram import TelegramNotifier
from utils.error_handler import ErrorHandler
from utils.logger import TradingLogger

@dataclass
class BotConfig:
    """Configurações centralizadas para o bot de trading."""
    SYMBOLS = ["top 50"]
    TIMEFRAME = "1h"
    RSI_PERIOD: int = 30
    WMA_PERIDOD: int = 34
    ADX_PERIOD: int = 14
    OHLCV_LIMIT: int = 200
    
    # Limiares para sinais
    RSI_OVERSOLD: float = 30.0
    RSI_OVERBOUGHT: float = 48
    LOOP_INTERVAL: int = 3600  # 1 hora em segundos


class CoinGeckoAPI:
    """Cliente para API do CoinGecko."""
    
    def __init__(self):
        self.base_url = "https://api.coingecko.com/api/v3"
        self.logger = TradingLogger.get_logger(__name__)
    
    def get_top_50_coins(self) -> List[Dict]:
        """Obtém as top 50 criptomoedas por market cap."""
        
        error_handler = ErrorHandler(__name__)
        
        def fetch_coins():
            url = f"{self.base_url}/coins/markets"
            params = {
                'vs_currency': 'usd',
                'order': 'market_cap_desc',
                'per_page': 50,
                'page': 1,
                'sparkline': False,
                'price_change_percentage': '1h,24h,7d'
            }
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            coins = response.json()
            self.logger.info(f"✅ Obtidas {len(coins)} moedas do CoinGecko")
            return coins
            
        return error_handler.execute_with_retry(fetch_coins, "Obter Top 50 Moedas do CoinGecko") or []


from core.okx_client import OKXClient

class CryptoExchangeClient:
    """Cliente simplificado para simular dados OHLCV."""
    
    def __init__(self, config: BotConfig):
        self.config = config
        self.logger = TradingLogger.get_logger(__name__)
        self.exchange = ccxt.binance({
            'enableRateLimit': True,
            'sandbox': False,
        })
        self.okx_client = OKXClient()  # Utilizando métodos de formatação robustos do OKXClient
    
    def get_symbol_ohlcv(self, symbol: str, timeframe: str = "1h", limit: int = 200) -> Optional[np.ndarray]:
        """Obtém dados OHLCV para um símbolo."""
        
        error_handler = ErrorHandler(__name__)
        
        def fetch_ohlcv():
            binance_symbol = self._convert_to_binance_symbol(symbol)
            if not binance_symbol:
                return None
            ohlcv = self.exchange.fetch_ohlcv(binance_symbol, timeframe, limit=limit)
            if not ohlcv:
                return None
            return np.array(ohlcv)
            
        return error_handler.execute_with_retry(fetch_ohlcv, f"Obter OHLCV para {symbol}")
    
    def _convert_to_binance_symbol(self, coingecko_id: str) -> Optional[str]:
        """Converte ID do CoinGecko para símbolo da Binance."""
        symbol_map = {
            'bitcoin': 'BTC/USDT',
            'ethereum': 'ETH/USDT',
            'tether': 'USDT/BUSD',
            'binancecoin': 'BNB/USDT',
            'ripple': 'XRP/USDT',
            'solana': 'SOL/USDT',
            'cardano': 'ADA/USDT',
            'dogecoin': 'DOGE/USDT',
            'avalanche-2': 'AVAX/USDT',
            'polkadot': 'DOT/USDT',
            'polygon': 'MATIC/USDT',
            'chainlink': 'LINK/USDT',
            'uniswap': 'UNI/USDT',
            'litecoin': 'LTC/USDT',
            'bitcoin-cash': 'BCH/USDT',
            'algorand': 'ALGO/USDT',
            'stellar': 'XLM/USDT',
            'vechain': 'VET/USDT',
            'filecoin': 'FIL/USDT',
            'cosmos': 'ATOM/USDT',
            'tron': 'TRX/USDT',
            'ethereum-classic': 'ETC/USDT',
            'monero': 'XMR/USDT',
            'eos': 'EOS/USDT',
            'aave': 'AAVE/USDT',
            'pancakeswap-token': 'CAKE/USDT',
            'compound-coin': 'COMP/USDT',
            'maker': 'MKR/USDT',
            'terra-luna': 'LUNA/USDT',
            'fantom': 'FTM/USDT',
            'near': 'NEAR/USDT',
            'the-sandbox': 'SAND/USDT',
            'decentraland': 'MANA/USDT',
            'axie-infinity': 'AXS/USDT',
            'zcash': 'ZEC/USDT',
            'dash': 'DASH/USDT',
            'compound-governance-token': 'COMP/USDT',
            'sushi': 'SUSHI/USDT',
            'yearn-finance': 'YFI/USDT',
            'curve-dao-token': 'CRV/USDT'
        }
        
        # Lista de pares a serem ignorados na análise
        ignored_pairs = ['USDT/BUSD']
        
        symbol = symbol_map.get(coingecko_id)
        if symbol in ignored_pairs:
            self.logger.info(f"⛔ Par {symbol} ignorado na análise conforme configuração.")
            return None
            
        return symbol


class IndicatorCalculator:
    """Responsável pelo cálculo de indicadores técnicos."""
    
    def __init__(self, config: BotConfig, exchange_client: CryptoExchangeClient):
        self.config = config
        self.exchange_client = exchange_client
        self.logger = TradingLogger.get_logger(__name__)
    
    def calculate_indicators(self, coin_id: str, timeframe: str, limit: int) -> Optional[Dict]:
        """Calcula todos os indicadores técnicos para uma moeda."""
        
        error_handler = ErrorHandler(__name__)
        
        ohlcv_data = self.exchange_client.get_symbol_ohlcv(coin_id, timeframe, limit)
        if ohlcv_data is None:
            self.logger.warning(f"⚠️ Dados OHLCV não disponíveis para {coin_id}")
            return None
        
        def calculate():
            return {
                'rsi': self._calculate_rsi(ohlcv_data),
                'wma': self._calculate_wma(ohlcv_data)
            }
            
        result = error_handler.execute_with_retry(calculate, f"Calcular Indicadores para {coin_id}")
        del ohlcv_data  # Ensure ohlcv_data is not kept in memory
        return result
    
    def _calculate_rsi(self, ohlcv_data: np.ndarray) -> float:
        """Calcula RSI."""
        closes = ohlcv_data[:, 4]
        rsi_values = talib.RSI(closes, timeperiod=self.config.RSI_PERIOD)
        return float(rsi_values[-1]) if not np.isnan(rsi_values[-1]) else 50.0
    
    def _calculate_wma(self, ohlcv_data: np.ndarray) -> float:
        """Calcula WMA."""
        closes = ohlcv_data[:, 4]
        wma_values = talib.WMA(closes, timeperiod=self.config.WMA_PERIDOD)
        return float(wma_values[-1]) if not np.isnan(wma_values[-1]) else closes[-1]
    
class SignalAnalyzer:
    """Responsável pela análise de indicadores para determinar sinais de trading."""
    
    def __init__(self, config: BotConfig):
        self.config = config
        self.logger = TradingLogger.get_logger(__name__)
    
    def analyze_indicators(self, indicators: Dict, price: float) -> Tuple[str, str]:
        """Analisa indicadores e determina o sinal de trading."""
        rsi = indicators['rsi']
        wma = indicators['wma']
        price_vs_wma = self._compare_price_to_wma(price, wma)
        signal = self._determine_signal(rsi, price_vs_wma)
        analysis_detail = f"RSI: {rsi:.1f}, WMA: {wma:.4f}, Preço vs WMA: {price_vs_wma}"
        return signal, analysis_detail
    
    def _compare_price_to_wma(self, price: float, wma: float) -> str:
        """Compara preço atual com WMA."""
        if price > wma * 1.02:  # 2% acima da WMA
            return 'ACIMA'
        elif price < wma * 0.98:  # 2% abaixo da WMA
            return 'ABAIXO'
        else:
            return 'PRÓXIMO'
    
    def _determine_signal(self, rsi: float, price_vs_wma: str) -> str:
        """Determina sinal final baseado nos indicadores."""
        buy_signals = 0
        sell_signals = 0
        
        # Análise RSI
        if rsi < self.config.RSI_OVERSOLD:
            buy_signals += 2
        elif rsi > self.config.RSI_OVERBOUGHT:
            sell_signals += 2
        
        
        # Análise Preço vs WMA
        if price_vs_wma == 'ACIMA':
            buy_signals += 1
        elif price_vs_wma == 'ABAIXO':
            sell_signals += 1
        
        # Decisão final
        if buy_signals >= 3:
            return 'BUY'
        elif sell_signals >= 3:
            return 'SELL'
        else:
            return 'HOLD'

class ResultFormatter:
    """Responsável pela formatação dos resultados da análise."""
    
    def __init__(self):
        self.logger = TradingLogger.get_logger(__name__)
    
    def format_result(self, coin_data: Dict, signal: str, analysis: str) -> Dict:
        """Formata o resultado da análise em um dicionário estruturado."""
        return {
            'id': coin_data['id'],
            'symbol': coin_data['symbol'].upper(),
            'name': coin_data['name'],
            'price': coin_data['current_price'],
            'market_cap_rank': coin_data['market_cap_rank'],
            'price_change_24h': coin_data.get('price_change_percentage_24h', 0),
            'signal': signal,
            'analysis': analysis,
            'timestamp': datetime.now().isoformat()
        }

class TradingSignalAnalyzer:
    """Coordena a análise de sinais de trading, delegando responsabilidades específicas."""
    
    def __init__(self, config: BotConfig):
        self.config = config
        self.logger = TradingLogger.get_logger(__name__)
        self.exchange_client = CryptoExchangeClient(config)
        self.indicator_calculator = IndicatorCalculator(config, self.exchange_client)
        self.signal_analyzer = SignalAnalyzer(config)
        self.result_formatter = ResultFormatter()
    
    def analyze_coin(self, coin_data: Dict) -> Dict:
        """Analisa uma moeda e retorna sinais de trading."""
        symbol = coin_data['symbol'].upper()
        
        error_handler = ErrorHandler(__name__)
        
        def analyze():
            indicators = self.indicator_calculator.calculate_indicators(
                coin_data['id'],
                self.config.TIMEFRAME,
                self.config.OHLCV_LIMIT
            )
            if indicators is None:
                return self.result_formatter.format_result(coin_data, 'HOLD', 'Dados insuficientes')
            signal, analysis_detail = self.signal_analyzer.analyze_indicators(indicators, coin_data['current_price'])
            return self.result_formatter.format_result(coin_data, signal, analysis_detail)
            
        result = error_handler.execute_with_retry(analyze, f"Analisar {symbol}")
        return result if result else self.result_formatter.format_result(coin_data, 'HOLD', 'Erro na análise')


class CryptoTop50Analyzer:
    """Analisador principal para as top 50 criptomoedas."""
    
    def __init__(self):
        self.config = BotConfig()
        self.logger = TradingLogger.get_logger(__name__)
        self.coingecko = CoinGeckoAPI()
        self.signal_analyzer = TradingSignalAnalyzer(self.config)
        self.telegram_notifier = self._initialize_telegram()
        
        # Storage para rastrear mudanças de sinal
        self.previous_signals: Dict[str, str] = {}
        
    def _initialize_telegram(self) -> Optional[TelegramNotifier]:
        """Inicializa notificador Telegram."""
        
        error_handler = ErrorHandler(__name__)
        
        def init_telegram():
            return TelegramNotifier()
            
        return error_handler.execute_with_retry(init_telegram, "Inicializar Telegram")
    
    async def run_analysis(self) -> List[Dict]:
        """Executa análise completa das top 50 moedas usando processamento paralelo."""
        self.logger.info("🚀 Iniciando análise paralela das top 50 criptomoedas...")
        
        # Obtém dados das top 50 moedas
        coins = self.coingecko.get_top_50_coins()
        if not coins:
            self.logger.error("❌ Falha ao obter dados do CoinGecko")
            return []
        
        # Analisa cada moeda em paralelo
        results = []
        buy_signals = []
        sell_signals = []
        
        # Usa asyncio para processar moedas em paralelo
        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            loop = asyncio.get_event_loop()
            tasks = [loop.run_in_executor(executor, self.signal_analyzer.analyze_coin, coin) for coin in coins]
            results = await asyncio.gather(*tasks)
        
        # Processa resultados para rastrear mudanças de sinal
        for analysis in results:
            coin_id = analysis['id']
            current_signal = analysis['signal']
            previous_signal = self.previous_signals.get(coin_id, 'HOLD')
            
            # Verifica mudanças significativas
            if self._is_signal_change_significant(previous_signal, current_signal):
                if current_signal == 'BUY':
                    buy_signals.append(analysis)
                elif current_signal == 'SELL':
                    sell_signals.append(analysis)
            
            # Atualiza sinal anterior
            self.previous_signals[coin_id] = current_signal
        
        # Limita o tamanho do cache de sinais anteriores
        self._prune_previous_signals(max_size=100)
        
        # Envia notificações de mudanças
        await self._send_signal_notifications(buy_signals, sell_signals)
        
        # Log dos resultados
        self._log_analysis_summary(results)
        
        return results
    
    def _is_signal_change_significant(self, previous: str, current: str) -> bool:
        """Verifica se mudança de sinal é significativa."""
        significant_changes = [
            ('HOLD', 'BUY'),
            ('HOLD', 'SELL'),
            ('SELL', 'BUY'),
            ('BUY', 'SELL')
        ]
        return (previous, current) in significant_changes
    
    async def _send_signal_notifications(self, buy_signals: List[Dict], sell_signals: List[Dict]):
        """Envia notificações de mudanças de sinal."""
        if not self.telegram_notifier:
            return
        
        # Notificações de BUY
        if buy_signals:
            message = "🟢 *SINAIS DE COMPRA DETECTADOS*\n\n"
            for signal in buy_signals[:5]:  # Máximo 5 por mensagem
                message += f"• *{signal['symbol']}* ({signal['name']})\n"
                message += f"  💰 Preço: ${signal['price']:.4f}\n"
                message += f"  📊 {signal['analysis']}\n\n"
            
            await self.telegram_notifier.send_message(message)
        
        # Notificações de SELL
        if sell_signals:
            message = "🔴 *Sinais de Venda Detectados*\n\n"
            for signal in sell_signals[:5]:  # Máximo 5 por mensagem
                message += f"• *{signal['symbol']}* ({signal['name']})\n"
                message += f"  💰 Preço: ${signal['price']:.4f}\n"
                message += f"  📊 {signal['analysis']}\n\n"
            
            await self.telegram_notifier.send_message(message)
    
    def _log_analysis_summary(self, results: List[Dict]):
        """Log do resumo da análise."""
        total = len(results)
        buy_count = sum(1 for r in results if r['signal'] == 'BUY')
        sell_count = sum(1 for r in results if r['signal'] == 'SELL')
        hold_count = sum(1 for r in results if r['signal'] == 'HOLD')
        
        self.logger.info(f"📊 Análise concluída - Total: {total}")
        self.logger.info(f"🟢 Comprar: {buy_count} | 🔴 Vender: {sell_count} | ⚪ Aguardar: {hold_count}")
    
    def _prune_previous_signals(self, max_size: int):
        """Remove entradas antigas do cache de sinais anteriores para evitar crescimento indefinido."""
        if len(self.previous_signals) > max_size:
            # Remove as entradas mais antigas mantendo apenas as mais recentes
            keys_to_remove = list(self.previous_signals.keys())[:len(self.previous_signals) - max_size]
            for key in keys_to_remove:
                del self.previous_signals[key]
            self.logger.info(f"🧹 Cache de sinais anteriores reduzido para {max_size} entradas")
    
    async def run_continuous_analysis(self):
        """Executa análise contínua em loop."""
        self.logger.info(f"🔄 Iniciando análise contínua (intervalo: {self.config.LOOP_INTERVAL}s)")
        
        while True:
            try:
                await self.run_analysis()
                
                # Aguarda próxima iteração com contagem decrescente
                remaining_seconds = self.config.LOOP_INTERVAL
                while remaining_seconds > 0:
                    minutes = remaining_seconds // 60
                    seconds = remaining_seconds % 60
                    self.logger.info(f"😴 Aguardando {minutes:02d}:{seconds:02d} para próxima análise...")
                    await asyncio.sleep(60 if remaining_seconds >= 60 else remaining_seconds)
                    remaining_seconds -= 60 if remaining_seconds >= 60 else remaining_seconds
                
            except KeyboardInterrupt:
                self.logger.info("⏹️ Análise interrompida pelo usuário")
                break
            except Exception as e:
                self.logger.error(f"❌ Erro na análise contínua: {e}")
                await asyncio.sleep(60)  # Aguarda 1 minuto em caso de erro


async def main():
    """Função principal."""
    analyzer = CryptoTop50Analyzer()
    
    # Executa análise única ou contínua
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == '--single':
        # Análise única
        results = await analyzer.run_analysis()
        
        # Exibe resultados
        print("\n" + "="*80)
        print("ANÁLISE DAS TOP 50 CRIPTOMOEDAS")
        print("="*80)
        
        for result in results:
            status_emoji = "🟢" if result['signal'] == 'BUY' else "🔴" if result['signal'] == 'SELL' else "⚪"
            print(f"{status_emoji} {result['symbol']:8} | ${result['price']:>10.4f} | {result['signal']:4} | {result['analysis']}")
    else:
        # Análise contínua
        await analyzer.run_continuous_analysis()


if __name__ == "__main__":
    asyncio.run(main())
