"""
<PERSON><PERSON><PERSON> to initialize or repair the database schema for the trading bot.
This script ensures that all necessary tables are created in the database.
"""

import os
import sqlite3
from utils.logger import TradingLogger

def initialize_database(db_path):
    """
    Initialize the database with the required schema.
    
    Args:
        db_path: Path to the SQLite database file.
    """
    logger = TradingLogger.get_logger(__name__)
    logger.info("Initializing database at: %s", db_path)
    
    # Ensure the directory exists
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # Create orders table
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS orders (
                    id TEXT PRIMARY KEY,
                    symbol TEXT NOT NULL,
                    order_type TEXT NOT NULL,
                    side TEXT NOT NULL,
                    price REAL,
                    amount REAL NOT NULL,
                    status TEXT,
                    pnl REAL,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    additional_info TEXT
                )
                """
            )
            
            # Create indexes for orders table
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_symbol ON orders (symbol)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON orders (timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_status ON orders (status)')
            
            # Create trade_history table
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS trade_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    buy_order_id TEXT,
                    sell_order_id TEXT,
                    buy_price REAL,
                    sell_price REAL,
                    amount REAL,
                    pnl REAL,
                    open_timestamp DATETIME,
                    close_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (buy_order_id) REFERENCES orders (id),
                    FOREIGN KEY (sell_order_id) REFERENCES orders (id)
                )
                """
            )
            
            # Create indexes for trade_history table
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_trade_symbol ON trade_history (symbol)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_trade_close_timestamp ON trade_history (close_timestamp)')
            
            # Create trailing_stops table
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS trailing_stops (
                    algo_id TEXT PRIMARY KEY,
                    order_id TEXT,
                    symbol TEXT NOT NULL,
                    callback_ratio REAL,
                    status TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (order_id) REFERENCES orders (id)
                )
                """
            )
            
            # Create indexes for trailing_stops table
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_trailing_symbol ON trailing_stops (symbol)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_trailing_status ON trailing_stops (status)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_trailing_timestamp ON trailing_stops (timestamp)')
            
            # Create oco_orders table
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS oco_orders (
                    oco_id TEXT PRIMARY KEY,
                    order_id TEXT,
                    symbol TEXT NOT NULL,
                    sl_trigger REAL,
                    sl_price REAL,
                    tp_trigger REAL,
                    tp_price REAL,
                    status TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (order_id) REFERENCES orders (id)
                )
                """
            )
            
            # Create indexes for oco_orders table
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_oco_symbol ON oco_orders (symbol)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_oco_status ON oco_orders (status)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_oco_timestamp ON oco_orders (timestamp)')
            
            # Create notified_orders table
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS notified_orders (
                    order_id TEXT PRIMARY KEY,
                    notified_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
                """
            )
            
            # Check if 'pnl' column exists in 'orders' table and add it if not
            cursor.execute("PRAGMA table_info(orders)")
            columns = [col[1] for col in cursor.fetchall()]
            if 'pnl' not in columns:
                cursor.execute("ALTER TABLE orders ADD COLUMN pnl REAL")
                logger.info("Added 'pnl' column to 'orders' table.")
            
            conn.commit()
            logger.info("Database schema initialized successfully at: %s", db_path)
            print(f"Database schema initialized successfully at: {db_path}")
    except sqlite3.Error as e:
        logger.error("Error initializing database: %s", str(e))
        print(f"Error initializing database: {e}")
        raise

if __name__ == "__main__":
    # Determine the database path based on sandbox mode
    SANDBOX_MODE = True  # Change this to False for live mode
    db_name = "sandbox_trades.db" if SANDBOX_MODE else "live_trades.db"
    db_path = os.path.join("trades", db_name)
    initialize_database(db_path)
