"""
Dashboard em Dash para controle de bots de trading e visualização de métricas - Versão 2.0 com melhorias.
"""

import json
import os
import subprocess
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional

import dash
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from dash import dcc, html, dash_table, callback_context
from dash.dependencies import Input, Output, State

# Importação direta dos módulos customizados sem fallback
from utils.database import TradingDatabase
from utils.logger import TradingLogger

# Nota: Importação de calculate_indicators removida, pois não existe em utils.metrics
# Placeholder para indicadores técnicos será usado até integração com módulo apropriado
# Nota: Importação de handle_error removida, pois não existe em utils.error_handler

# Inicialização do app Dash
app = dash.Dash(__name__, title="Dashboard de Trading Avançado")

# Logger
logger = TradingLogger.get_logger(__name__)

# Configurações
BOTS = {
    "999_base_trail_WMA": "999_base_trail_wma.py",
    "999_base_OCO_WMA": "999_base_oco_wma.py",
    "999_base_trail_Swing": "999_base_trail_swing.py",
    "777_base_grid": "777_base_grid.py",
}
DB_PATH = "trades/sandbox_trades.db"
PARAMETERS_FILE = "parameters.json"
UPDATE_INTERVAL = 10000  # 1 hora em milissegundos para minimizar a carga no servidor e evitar timeouts
TEMP_CONFIG_DIR = "temp_configs"
METRICS_FILE = "analises/okx_analyzer.json"

# Criar diretório para configs temporários
os.makedirs(TEMP_CONFIG_DIR, exist_ok=True)

# Dicionário para armazenar processos ativos
active_processes: Dict[str, subprocess.Popen] = {}


# Carregar parâmetros do arquivo parameters.json
def load_parameters():
    try:
        with open(PARAMETERS_FILE, "r") as f:
            return json.load(f)
    except FileNotFoundError:
        logger.warning(f"Arquivo {PARAMETERS_FILE} não encontrado")
        return {"general": {}}
    except Exception as e:
        logger.error(f"Erro ao carregar {PARAMETERS_FILE}: {e}")
        return {"general": {}}


parameters = load_parameters()


# Função para obter parâmetros de um bot
def get_bot_parameters(bot_name: str) -> Dict:
    subgroup_mapping = {
        "999_base_trail_WMA": "trail_signals_wma_rsi",
        "999_base_OCO_WMA": "OCO_wma_rsi",
        "999_base_trail_Swing": "swing",
        "777_base_grid": "general",
    }

    subgroup = subgroup_mapping.get(bot_name, "general")
    return parameters.get(subgroup, parameters.get("general", {}))


# Função para limpar arquivos temporários
def cleanup_temp_files():
    try:
        for file in os.listdir(TEMP_CONFIG_DIR):
            if file.startswith("temp_config_") and file.endswith(".json"):
                os.remove(os.path.join(TEMP_CONFIG_DIR, file))
    except Exception as e:
        logger.error(f"Erro ao limpar arquivos temporários: {e}")


# Função para iniciar um bot
def start_bot(bot_name: str, config: Dict):
    if bot_name in active_processes:
        logger.info(f"Bot {bot_name} já está em execução.")
        return f"Bot {bot_name} já está em execução."

    bot_file = BOTS.get(bot_name)
    if not bot_file:
        logger.error(f"Bot {bot_name} não encontrado.")
        return f"Bot {bot_name} não encontrado."

    # Verificar se o arquivo do bot existe
    if not os.path.exists(bot_file):
        logger.error(f"Arquivo do bot {bot_file} não encontrado.")
        return f"Arquivo do bot {bot_file} não encontrado."

    # Criar arquivo de configuração temporário para o bot
    temp_config_file = os.path.join(TEMP_CONFIG_DIR, f"temp_config_{bot_name}.json")

    try:
        with open(temp_config_file, "w") as f:
            json.dump(config, f, indent=2)

        # Iniciar o bot como processo separado
        process = subprocess.Popen(
            ["python", bot_file, temp_config_file],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
        )
        active_processes[bot_name] = process
        logger.info(f"Bot {bot_name} iniciado com PID {process.pid}.")
        return f"Bot {bot_name} iniciado com sucesso (PID: {process.pid})."

    except Exception as e:
        logger.error(f"Erro ao iniciar bot {bot_name}: {e}")
        # Limpar arquivo temporário em caso de erro
        try:
            os.remove(temp_config_file)
        except:
            pass
        return f"Erro ao iniciar bot {bot_name}: {str(e)}"


# Função para parar um bot
def stop_bot(bot_name: str):
    if bot_name not in active_processes:
        logger.info(f"Bot {bot_name} não está em execução.")
        return f"Bot {bot_name} não está em execução."

    process = active_processes[bot_name]
    try:
        process.terminate()
        process.wait(timeout=5)
        del active_processes[bot_name]

        # Limpar arquivo de configuração temporário
        temp_config_file = os.path.join(TEMP_CONFIG_DIR, f"temp_config_{bot_name}.json")
        try:
            os.remove(temp_config_file)
        except:
            pass

        logger.info(f"Bot {bot_name} parado com sucesso.")
        return f"Bot {bot_name} parado com sucesso."

    except subprocess.TimeoutExpired:
        # Forçar terminação se não parar normalmente
        process.kill()
        del active_processes[bot_name]
        logger.warning(f"Bot {bot_name} foi forçado a parar.")
        return f"Bot {bot_name} foi forçado a parar."

    except Exception as e:
        logger.error(f"Erro ao parar bot {bot_name}: {e}")
        return f"Erro ao parar bot {bot_name}: {str(e)}"


# Função para carregar dados do banco de dados
def load_data_from_db(table: str, limit: int = 10) -> List[Dict]:
    try:
        db = TradingDatabase(DB_PATH)
        # Carregar todas as ordens e filtrar manualmente
        all_orders = db.get_orders(
            limit=limit * 2
        )  # Carregar mais para garantir que temos ordens abertas e fechadas

        if table == "closed_orders":
            data = [order for order in all_orders if order.get("status") == "closed"][
                :limit
            ]
        elif table == "open_orders":
            data = [order for order in all_orders if order.get("status") != "closed"][
                :limit
            ]
        else:
            data = []
        return data

    except Exception as e:
        logger.error(f"Erro ao carregar dados do banco de dados: {e}")
        return []


# Função para carregar métricas do arquivo JSON
def load_metrics_from_json() -> Dict:
    try:
        if not os.path.exists(METRICS_FILE):
            logger.warning(f"Arquivo de métricas {METRICS_FILE} não encontrado")
            return {}

        with open(METRICS_FILE, "r") as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Erro ao carregar métricas do JSON: {e}")
        return {}


# Função para obter status dos bots
def get_bot_status():
    status = {}
    for bot_name, process in active_processes.items():
        if process.poll() is None:  # Processo ainda está rodando
            status[bot_name] = "Rodando"
        else:
            status[bot_name] = "Parado"
            # Remover processo morto da lista
            del active_processes[bot_name]
    return status


# Função para calcular indicadores técnicos (placeholder)
def get_technical_indicators(symbol: str, timeframe: str = "1h"):
    try:
        # Placeholder para indicadores técnicos, pois utils.metrics não possui calculate_indicators
        # Futuramente, integrar com módulo de indicadores técnicos (RSI, MACD, etc.)
        logger.info(
            f"Calculando indicadores técnicos para {symbol} no timeframe {timeframe} (placeholder)"
        )
        return {
            "rsi": "N/A (placeholder)",
            "macd": "N/A (placeholder)",
            "wma": "N/A (placeholder)",
        }
    except Exception as e:
        logger.error(f"Erro ao calcular indicadores técnicos: {e}")
        return {}


# Layout do Dashboard com melhorias
app.layout = html.Div(
    [
        html.H1(
            "Dashboard de Trading Avançado",
            style={"textAlign": "center", "color": "#333"},
        ),
        # Seção de Alertas no topo
        html.Div(
            id="alerts-section",
            style={
                "padding": "10px",
                "border": "1px solid #ff9999",
                "marginBottom": "20px",
                "backgroundColor": "#ffeeee",
            },
        ),
        # Seção de Controle de Bots
        html.Div(
            [
                html.H2("Controle de Bots", style={"color": "#444"}),
                dcc.Dropdown(
                    id="bot-selector",
                    options=[{"label": bot, "value": bot} for bot in BOTS.keys()],
                    value=list(BOTS.keys())[0],
                    style={"width": "50%"},
                ),
                html.Div(id="bot-config-form", style={"marginTop": "10px"}),
                html.Button(
                    "Iniciar Bot",
                    id="start-bot-button",
                    n_clicks=0,
                    style={"marginRight": "10px"},
                ),
                html.Button("Parar Bot", id="stop-bot-button", n_clicks=0),
                html.Div(
                    id="bot-status-output",
                    style={"marginTop": "10px", "fontWeight": "bold"},
                ),
                html.Div(id="bot-status-list", style={"marginTop": "10px"}),
            ],
            style={
                "padding": "20px",
                "border": "1px solid #ddd",
                "marginBottom": "20px",
            },
        ),
        # Seção de Métricas Avançadas
        html.Div(
            [
                html.H2("Métricas de Trading", style={"color": "#444"}),
                html.Div(
                    id="metrics-cards",
                    style={"display": "flex", "flexWrap": "wrap", "gap": "20px"},
                ),
                html.Div(
                    [
                        html.H3(
                            "Métricas por Par e Estratégia", style={"marginTop": "20px"}
                        ),
                        dcc.Dropdown(
                            id="pair-strategy-filter",
                            options=[
                                {"label": "Todos os Pares", "value": "all"},
                            ],
                            value="all",
                            style={"width": "30%", "marginBottom": "10px"},
                        ),
                        dcc.Dropdown(
                            id="time-filter",
                            options=[
                                {"label": "Diário", "value": "daily"},
                                {"label": "Semanal", "value": "weekly"},
                                {"label": "Mensal", "value": "monthly"},
                            ],
                            value="daily",
                            style={"width": "30%", "marginBottom": "10px"},
                        ),
                        html.Div(id="pair-strategy-metrics"),
                    ],
                    style={"marginTop": "20px"},
                ),
            ],
            style={
                "padding": "20px",
                "border": "1px solid #ddd",
                "marginBottom": "20px",
            },
        ),
        # Seção de Gráficos Interativos
        html.Div(
            [
                html.H2("Gráficos Dinâmicos", style={"color": "#444"}),
                dcc.Dropdown(
                    id="chart-timeframe-selector",
                    options=[
                        {"label": "Diário", "value": "daily"},
                        {"label": "Semanal", "value": "weekly"},
                        {"label": "Mensal", "value": "monthly"},
                    ],
                    value="daily",
                    style={"width": "30%", "marginBottom": "10px"},
                ),
                dcc.Graph(id="pnl-chart"),
                dcc.Graph(id="portfolio-composition-chart"),
                dcc.Graph(id="symbol-performance-chart"),
            ],
            style={
                "padding": "20px",
                "border": "1px solid #ddd",
                "marginBottom": "20px",
            },
        ),
        # Seção de Indicadores Técnicos
        html.Div(
            [
                html.H2("Indicadores Técnicos", style={"color": "#444"}),
                dcc.Dropdown(
                    id="indicator-symbol-selector",
                    options=[
                        {"label": sym, "value": sym}
                        for sym in parameters.get("general", {}).get(
                            "TRADING_SYMBOLS", []
                        )
                    ],
                    value=(
                        parameters.get("general", {}).get("TRADING_SYMBOLS", [])[0]
                        if parameters.get("general", {}).get("TRADING_SYMBOLS", [])
                        else None
                    ),
                    style={"width": "30%", "marginBottom": "10px"},
                ),
                html.Div(id="technical-indicators-display"),
            ],
            style={
                "padding": "20px",
                "border": "1px solid #ddd",
                "marginBottom": "20px",
            },
        ),
        # Seção de Tabelas
        html.Div(
            [
                html.H2("Operações Terminadas", style={"color": "#444"}),
                html.Div(id="closed-orders-table"),
                html.H2("Operações Ativas", style={"color": "#444"}),
                html.Div(id="open-orders-table"),
            ],
            style={"padding": "20px", "border": "1px solid #ddd"},
        ),
        # Botão de Exportação de Dados
        html.Div(
            [
                html.Button(
                    "Exportar Dados (CSV)",
                    id="export-data-button",
                    n_clicks=0,
                    style={"marginTop": "20px"},
                ),
                html.Div(id="export-status", style={"marginTop": "10px"}),
            ],
            style={"padding": "10px", "textAlign": "center"},
        ),
        # Intervalo para atualização automática
        dcc.Interval(id="interval-component", interval=UPDATE_INTERVAL, n_intervals=0),
    ],
    style={"fontFamily": "Arial, sans-serif", "maxWidth": "1200px", "margin": "0 auto"},
)


# Callback para atualizar o formulário de configuração do bot
@app.callback(Output("bot-config-form", "children"), Input("bot-selector", "value"))
def update_config_form(bot_name):
    if not bot_name:
        return html.Div("Selecione um bot para configurar.")

    params = get_bot_parameters(bot_name)
    symbols = params.get("TRADING_SYMBOLS", ["BTC/USDT", "ETH/USDT"])
    timeframe = params.get("TIMEFRAME", "15m")
    rsi_period = params.get("RSI_PERIOD", 30)
    wma_period = params.get("WMA_PERIOD", 84)

    return html.Div(
        [
            html.Label("Símbolos:"),
            dcc.Checklist(
                id="symbols-checklist",
                options=[{"label": symbol, "value": symbol} for symbol in symbols],
                value=symbols,
                style={"marginBottom": "10px"},
            ),
            html.Label("Timeframe:"),
            dcc.Input(
                id="timeframe-input",
                value=timeframe,
                type="text",
                style={"marginBottom": "10px"},
            ),
            html.Label("RSI Period:"),
            dcc.Input(
                id="rsi-period-input",
                value=rsi_period,
                type="number",
                style={"marginBottom": "10px"},
            ),
            html.Label("WMA Period:"),
            dcc.Input(id="wma-period-input", value=wma_period, type="number"),
        ]
    )


# Callback para atualizar opções de filtro de pares
@app.callback(
    Output("pair-strategy-filter", "options"),
    Input("interval-component", "n_intervals"),
)
def update_pair_filter_options(n_intervals):
    metrics_data = load_metrics_from_json()
    analysis_results = metrics_data.get("analysis_results", {})
    symbol_perf = (
        analysis_results.get("performance", {})
        .get("symbol_performance", {})
        .get("('cost', 'sum')", {})
    )
    pairs = list(symbol_perf.keys())
    options = [{"label": "Todos os Pares", "value": "all"}] + [
        {"label": pair, "value": pair} for pair in pairs
    ]
    return options


# Callback unificado para controle de bots
@app.callback(
    Output("bot-status-output", "children"),
    [Input("start-bot-button", "n_clicks"), Input("stop-bot-button", "n_clicks")],
    [
        State("bot-selector", "value"),
        State("symbols-checklist", "value"),
        State("timeframe-input", "value"),
        State("rsi-period-input", "value"),
        State("wma-period-input", "value"),
    ],
)
def handle_bot_control(
    start_clicks, stop_clicks, bot_name, symbols, timeframe, rsi_period, wma_period
):
    if not bot_name:
        return "Selecione um bot."

    ctx = callback_context
    if not ctx.triggered:
        return ""

    button_id = ctx.triggered[0]["prop_id"].split(".")[0]

    if button_id == "start-bot-button" and start_clicks > 0:
        config = {
            "TRADING_SYMBOLS": symbols or [],
            "TIMEFRAME": timeframe or "15m",
            "RSI_PERIOD": rsi_period or 30,
            "WMA_PERIOD": wma_period or 84,
        }
        return start_bot(bot_name, config)

    elif button_id == "stop-bot-button" and stop_clicks > 0:
        return stop_bot(bot_name)

    return ""


# Callback para atualizar métricas, gráficos, alertas e indicadores
@app.callback(
    [
        Output("alerts-section", "children"),
        Output("metrics-cards", "children"),
        Output("pnl-chart", "figure"),
        Output("portfolio-composition-chart", "figure"),
        Output("symbol-performance-chart", "figure"),
        Output("closed-orders-table", "children"),
        Output("open-orders-table", "children"),
        Output("bot-status-list", "children"),
        Output("technical-indicators-display", "children"),
        Output("pair-strategy-metrics", "children"),
    ],
    [
        Input("interval-component", "n_intervals"),
        Input("chart-timeframe-selector", "value"),
        Input("indicator-symbol-selector", "value"),
        Input("pair-strategy-filter", "value"),
        Input("time-filter", "value"),
    ],
)
def update_dashboard(n_intervals, timeframe, selected_symbol, pair_filter, time_filter):
    try:
        # Carregar métricas
        metrics_data = load_metrics_from_json()
        result = metrics_data.get("result", {})
        analysis_results = metrics_data.get("analysis_results", {})

        # Seção de Alertas
        recommendations = result.get("recommendations", [])
        risk_score = result.get("risk_score", 0.0)
        alerts = []
        if risk_score > 0.5:
            alerts.append(
                html.P(
                    "⚠️ Alto Risco Detectado: Revise suas posições!",
                    style={"color": "red", "fontWeight": "bold"},
                )
            )
        for rec in recommendations:
            alerts.append(html.P(rec, style={"color": "#d9534f"}))
        alerts_section = (
            alerts
            if alerts
            else html.P("Nenhum alerta crítico no momento.", style={"color": "green"})
        )

        # Cards de métricas avançadas
        risk_data = analysis_results.get("risk", {})
        portfolio_data = analysis_results.get("portfolio", {})
        metrics_cards = [
            html.Div(
                [
                    html.H3("Total de Ordens", style={"margin": 0}),
                    html.P(
                        result.get("total_orders", 0),
                        style={"fontSize": "24px", "margin": 0},
                    ),
                ],
                style={
                    "border": "1px solid #ddd",
                    "padding": "10px",
                    "borderRadius": "5px",
                    "flex": "1",
                },
            ),
            html.Div(
                [
                    html.H3("Taxa de Acerto", style={"margin": 0}),
                    html.P(
                        f"{result.get('win_rate', 0.0):.1f}%",
                        style={"fontSize": "24px", "margin": 0},
                    ),
                ],
                style={
                    "border": "1px solid #ddd",
                    "padding": "10px",
                    "borderRadius": "5px",
                    "flex": "1",
                },
            ),
            html.Div(
                [
                    html.H3("Lucro/Prejuízo", style={"margin": 0}),
                    html.P(
                        f"${result.get('profit_loss', 0.0):.2f}",
                        style={
                            "fontSize": "24px",
                            "margin": 0,
                            "color": (
                                "green" if result.get("profit_loss", 0.0) > 0 else "red"
                            ),
                        },
                    ),
                ],
                style={
                    "border": "1px solid #ddd",
                    "padding": "10px",
                    "borderRadius": "5px",
                    "flex": "1",
                },
            ),
            html.Div(
                [
                    html.H3("Sharpe Ratio", style={"margin": 0}),
                    html.P(
                        f"{risk_data.get('sharpe_ratio', 0.0):.2f}",
                        style={"fontSize": "24px", "margin": 0},
                    ),
                ],
                style={
                    "border": "1px solid #ddd",
                    "padding": "10px",
                    "borderRadius": "5px",
                    "flex": "1",
                },
            ),
            html.Div(
                [
                    html.H3("Máximo Drawdown", style={"margin": 0}),
                    html.P(
                        f"{risk_data.get('max_drawdown', 0.0):.2f}%",
                        style={"fontSize": "24px", "margin": 0},
                    ),
                ],
                style={
                    "border": "1px solid #ddd",
                    "padding": "10px",
                    "borderRadius": "5px",
                    "flex": "1",
                },
            ),
            html.Div(
                [
                    html.H3("VaR (95%)", style={"margin": 0}),
                    html.P(
                        f"${risk_data.get('var_95', 0.0):.2f}",
                        style={"fontSize": "24px", "margin": 0},
                    ),
                ],
                style={
                    "border": "1px solid #ddd",
                    "padding": "10px",
                    "borderRadius": "5px",
                    "flex": "1",
                },
            ),
            html.Div(
                [
                    html.H3("Risco de Concentração", style={"margin": 0}),
                    html.P(
                        f"{portfolio_data.get('concentration_risk', 0.0):.1f}%",
                        style={"fontSize": "24px", "margin": 0},
                    ),
                ],
                style={
                    "border": "1px solid #ddd",
                    "padding": "10px",
                    "borderRadius": "5px",
                    "flex": "1",
                },
            ),
        ]

        # Gráfico de PnL com interatividade
        daily_pnl = analysis_results.get("performance", {}).get("daily_pnl", {})
        if daily_pnl:
            dates = list(daily_pnl.keys())
            pnl_values = list(daily_pnl.values())
            pnl_fig = px.line(
                x=dates,
                y=pnl_values,
                title="Evolução do PnL",
                labels={"x": "Data", "y": "PnL ($)"},
            )
            pnl_fig.update_layout(
                hovermode="x unified",
                xaxis=dict(rangeslider=dict(visible=True), type="date"),
            )
        else:
            pnl_fig = px.line(title="Evolução do PnL - Sem dados disponíveis")

        # Gráfico de Composição do Portfólio (Pie Chart)
        asset_weights = portfolio_data.get("asset_weights", {})
        if asset_weights:
            assets = list(asset_weights.keys())
            weights = list(asset_weights.values())
            portfolio_fig = px.pie(
                names=assets, values=weights, title="Composição do Portfólio"
            )
        else:
            portfolio_fig = px.pie(
                title="Composição do Portfólio - Sem dados disponíveis"
            )

        # Gráfico de Desempenho por Símbolo
        symbol_perf = (
            analysis_results.get("performance", {})
            .get("symbol_performance", {})
            .get("('cost', 'sum')", {})
        )
        if symbol_perf:
            symbols = list(symbol_perf.keys())
            costs = list(symbol_perf.values())
            symbol_fig = px.bar(
                x=symbols,
                y=costs,
                title="Desempenho por Símbolo (Custo Total)",
                labels={"x": "Símbolo", "y": "Custo ($)"},
            )
            symbol_fig.update_layout(xaxis_tickangle=45)
        else:
            symbol_fig = px.bar(title="Desempenho por Símbolo - Sem dados disponíveis")

        # Tabelas de ordens
        logger.info("Carregando ordens do banco de dados")
        closed_orders = load_data_from_db("closed_orders", limit=10)
        open_orders = load_data_from_db("open_orders", limit=10)
        logger.info("Ordens carregadas com sucesso")

        # Tabela de ordens fechadas
        if closed_orders:
            closed_orders_df = pd.DataFrame(closed_orders)
            if "datetime" in closed_orders_df.columns:
                closed_orders_df["datetime"] = pd.to_datetime(
                    closed_orders_df["datetime"]
                )
                closed_orders_df["datetime"] = closed_orders_df["datetime"].dt.strftime(
                    "%Y-%m-%d %H:%M:%S"
                )

            closed_orders_table = dash_table.DataTable(
                data=closed_orders_df.to_dict("records"),
                columns=[
                    {"name": "ID", "id": "id"},
                    {"name": "Símbolo", "id": "symbol"},
                    {"name": "Lado", "id": "side"},
                    {"name": "Preço", "id": "price"},
                    {"name": "Quantidade", "id": "amount"},
                    {"name": "Custo", "id": "cost"},
                    (
                        {"name": "Data/Hora", "id": "datetime"}
                        if "datetime" in closed_orders_df.columns
                        else {"name": "Data/Hora", "id": "timestamp"}
                    ),
                ],
                style_table={"overflowX": "auto"},
                style_cell={"textAlign": "left"},
                style_header={
                    "backgroundColor": "rgb(230, 230, 230)",
                    "fontWeight": "bold",
                },
            )
        else:
            closed_orders_table = html.P("Nenhuma ordem terminada encontrada.")

        # Tabela de ordens abertas
        if open_orders:
            open_orders_df = pd.DataFrame(open_orders)
            if "datetime" in open_orders_df.columns:
                open_orders_df["datetime"] = pd.to_datetime(open_orders_df["datetime"])
                open_orders_df["datetime"] = open_orders_df["datetime"].dt.strftime(
                    "%Y-%m-%d %H:%M:%S"
                )

            open_orders_table = dash_table.DataTable(
                data=open_orders_df.to_dict("records"),
                columns=[
                    {"name": "ID", "id": "id"},
                    {"name": "Símbolo", "id": "symbol"},
                    {"name": "Lado", "id": "side"},
                    {"name": "Preço", "id": "price"},
                    {"name": "Quantidade", "id": "amount"},
                    {"name": "Custo", "id": "cost"},
                    (
                        {"name": "Data/Hora", "id": "datetime"}
                        if "datetime" in open_orders_df.columns
                        else {"name": "Data/Hora", "id": "timestamp"}
                    ),
                ],
                style_table={"overflowX": "auto"},
                style_cell={"textAlign": "left"},
                style_header={
                    "backgroundColor": "rgb(230, 230, 230)",
                    "fontWeight": "bold",
                },
            )
        else:
            open_orders_table = html.P("Nenhuma ordem ativa encontrada.")

        # Status dos bots
        bot_status = get_bot_status()
        status_list = [
            html.H4("Status dos Bots:", style={"marginBottom": "10px"}),
            (
                html.Ul(
                    [
                        html.Li(
                            f"{bot}: {status}",
                            style={"color": "green" if status == "Rodando" else "red"},
                        )
                        for bot, status in bot_status.items()
                    ]
                )
                if bot_status
                else html.P("Nenhum bot em execução.")
            ),
        ]

        # Indicadores técnicos para o símbolo selecionado
        if selected_symbol:
            indicators = get_technical_indicators(selected_symbol)
            if indicators:
                indicators_display = html.Div(
                    [
                        html.P(
                            f"RSI: {indicators.get('rsi', 'N/A')}",
                            style={"fontSize": "18px"},
                        ),
                        html.P(
                            f"MACD: {indicators.get('macd', 'N/A')}",
                            style={"fontSize": "18px"},
                        ),
                        html.P(
                            f"WMA: {indicators.get('wma', 'N/A')}",
                            style={"fontSize": "18px"},
                        ),
                    ]
                )
            else:
                indicators_display = html.P(
                    "Indicadores técnicos não disponíveis para este símbolo."
                )
        else:
            indicators_display = html.P(
                "Selecione um símbolo para ver os indicadores técnicos."
            )

        # Métricas por par e estratégia
        pair_strategy_metrics = []
        strategy_performance = analysis_results.get("performance", {}).get(
            "strategy_performance", {}
        )
        if pair_filter == "all":
            for strategy, data in strategy_performance.items():
                if time_filter in data:
                    pair_strategy_metrics.append(
                        html.Div(
                            [
                                html.H4(f"Estratégia: {strategy}"),
                                html.P(
                                    f"Lucro/Prejuízo ({time_filter}): ${data[time_filter].get('profit_loss', 0.0):.2f}"
                                ),
                                html.P(
                                    f"Taxa de Acerto ({time_filter}): {data[time_filter].get('win_rate', 0.0):.1f}%"
                                ),
                                html.P(
                                    f"Total de Ordens ({time_filter}): {data[time_filter].get('total_orders', 0)}"
                                ),
                            ],
                            style={
                                "border": "1px solid #ddd",
                                "padding": "10px",
                                "borderRadius": "5px",
                                "marginBottom": "10px",
                            },
                        )
                    )
        else:
            for strategy, data in strategy_performance.items():
                if time_filter in data and pair_filter in data[time_filter].get(
                    "pairs", {}
                ):
                    pair_data = data[time_filter]["pairs"][pair_filter]
                    pair_strategy_metrics.append(
                        html.Div(
                            [
                                html.H4(f"Estratégia: {strategy} - Par: {pair_filter}"),
                                html.P(
                                    f"Lucro/Prejuízo ({time_filter}): ${pair_data.get('profit_loss', 0.0):.2f}"
                                ),
                                html.P(
                                    f"Taxa de Acerto ({time_filter}): {pair_data.get('win_rate', 0.0):.1f}%"
                                ),
                                html.P(
                                    f"Total de Ordens ({time_filter}): {pair_data.get('total_orders', 0)}"
                                ),
                            ],
                            style={
                                "border": "1px solid #ddd",
                                "padding": "10px",
                                "borderRadius": "5px",
                                "marginBottom": "10px",
                            },
                        )
                    )
        if not pair_strategy_metrics:
            pair_strategy_metrics = html.P(
                "Nenhuma métrica disponível para o filtro selecionado."
            )

        logger.info("Atualização do dashboard concluída com sucesso")
        return (
            alerts_section,
            metrics_cards,
            pnl_fig,
            portfolio_fig,
            symbol_fig,
            closed_orders_table,
            open_orders_table,
            status_list,
            indicators_display,
            pair_strategy_metrics,
        )
    except Exception as e:
        logger.error(f"Erro no callback update_dashboard: {str(e)}", exc_info=True)
        # Retornar valores padrão ou mensagens de erro para os componentes do dashboard
        alerts_section = html.P(
            "Erro ao carregar alertas. Verifique os logs para mais detalhes.",
            style={"color": "red"},
        )
        metrics_cards = html.P("Erro ao carregar métricas.")
        pnl_fig = px.line(title="Erro ao carregar gráfico de PnL")
        portfolio_fig = px.pie(title="Erro ao carregar composição do portfólio")
        symbol_fig = px.bar(title="Erro ao carregar desempenho por símbolo")
        closed_orders_table = html.P("Erro ao carregar ordens terminadas.")
        open_orders_table = html.P("Erro ao carregar ordens ativas.")
        status_list = html.P("Erro ao carregar status dos bots.")
        indicators_display = html.P("Erro ao carregar indicadores técnicos.")
        pair_strategy_metrics = html.P(
            "Erro ao carregar métricas por par e estratégia."
        )
        return (
            alerts_section,
            metrics_cards,
            pnl_fig,
            portfolio_fig,
            symbol_fig,
            closed_orders_table,
            open_orders_table,
            status_list,
            indicators_display,
            pair_strategy_metrics,
        )


# Callback para exportação de dados
@app.callback(
    Output("export-status", "children"), Input("export-data-button", "n_clicks")
)
def export_data(n_clicks):
    if n_clicks > 0:
        try:
            metrics_data = load_metrics_from_json()
            closed_orders = load_data_from_db("closed_orders", limit=100)
            open_orders = load_data_from_db("open_orders", limit=100)

            # Exportar métricas
            with open("export_metrics.json", "w") as f:
                json.dump(metrics_data, f, indent=2)

            # Exportar ordens fechadas
            closed_df = pd.DataFrame(closed_orders)
            closed_df.to_csv("export_closed_orders.csv", index=False)

            # Exportar ordens abertas
            open_df = pd.DataFrame(open_orders)
            open_df.to_csv("export_open_orders.csv", index=False)

            return html.P(
                "Dados exportados com sucesso! Verifique os arquivos export_*.csv e export_metrics.json.",
                style={"color": "green"},
            )
        except Exception as e:
            logger.error(f"Erro ao exportar dados: {e}")
            return html.P(f"Erro ao exportar dados: {str(e)}", style={"color": "red"})
    return ""


# Função de limpeza ao encerrar
def cleanup_on_exit():
    """Limpa recursos ao encerrar o aplicativo"""
    logger.info("Encerrando aplicativo...")

    # Parar todos os bots
    for bot_name in list(active_processes.keys()):
        stop_bot(bot_name)

    # Limpar arquivos temporários
    cleanup_temp_files()


# Registrar função de limpeza
import atexit

atexit.register(cleanup_on_exit)

# Rodar o servidor
if __name__ == "__main__":
    try:
        app.run(debug=True, host="0.0.0.0", port=8050)
    except KeyboardInterrupt:
        logger.info("Aplicativo interrompido pelo usuário")
        cleanup_on_exit()
