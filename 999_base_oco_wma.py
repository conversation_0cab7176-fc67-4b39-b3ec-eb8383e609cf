"""
Bot de trading automatizado para OKX Exchange utilizando estratégia OCO com WMA.
"""

import warnings

warnings.filterwarnings("ignore", message="pkg_resources is deprecated as an API")

import asyncio
import os
import sys
import numpy as np
import json
import ccxt
import talib
import pandas as pd
import uuid
import time
from decimal import Decimal, ROUND_HALF_UP
from dotenv import load_dotenv
from typing import Dict, List, Optional, Tuple, Any

# Importações dos módulos compartilhados
from core.config import BotConfig, OrderCache
from core.base_bot import (
    TradingBotBase,
    initialize_bot_components,
    send_startup_notification,
)
from core.okx_client import OKXClient
from core.risk_management import (
    VolatilityCalculator,
    PositionSizeCalculator,
    ATRCalculator,
    MarketDataProcessor,
    TrendValidator,
    PositionSizer,
    RiskManager
)
from indicators.indicators import TechnicalIndicator
from utils.logger import TradingLogger
from utils.check_orders import check_oco_orders
from utils.order_validator import OrderValidator
from utils.exceptions import ConfigurationError, ExchangeConnectionError
from utils.common import load_parameters
from utils.check_orders import check_closed_oco_orders
from utils.database import TradingDatabase
from signals.signal_wma_rsi import SinaisWmaRsi

load_dotenv()



class OKXOrder:
    """Classe para gerenciar operações de ordens na exchange OKX com estratégia OCO."""

    def __init__(self, client: OKXClient):
        self.client = client
        self.logger = TradingLogger.get_logger(__name__)
        self.validator = OrderValidator(client)
        # Garantir que o cache de ordens esteja inicializado no cliente
        if not hasattr(self.client, "order_cache"):
            self.client.order_cache = OrderCache(cache_duration=30)

        # Cache para indicadores técnicos para evitar recálculos
        self._indicators_cache = {}
        self._indicators_cache_timestamps = {}
        self._indicators_cache_ttl = 60  # 1 minuto de TTL para indicadores

        # Calculadora de volatilidade otimizada
        self.volatility_calculator = VolatilityCalculator(
            lookback_periods=getattr(client.config, 'ATR_LOOKBACK_PERIODS', 100),
            cache_ttl=300  # 5 minutos para volatilidade
        )

        # Calculadora de tamanho de posição otimizada (nova versão)
        self.position_sizer = PositionSizer(client.config)

        # Calculadora de ATR otimizada
        self.atr_calculator = ATRCalculator(cache_ttl=300)

        # Processador de dados de mercado otimizado
        self.market_processor = MarketDataProcessor(client)

        # Validador de tendência otimizado
        self.trend_validator = TrendValidator(client.config)

        # Gerenciador de risco otimizado
        self.risk_manager = RiskManager(client.config)

    def _get_cached_indicator(self, cache_key: str, calc_func, *args, **kwargs):
        """
        Obtém indicador do cache ou calcula se necessário.

        Args:
            cache_key: Chave única para o cache
            calc_func: Função de cálculo do indicador
            *args, **kwargs: Argumentos para a função de cálculo

        Returns:
            Valor do indicador calculado ou do cache
        """
        import time
        current_time = time.time()

        # Verificar se está no cache e ainda válido
        if (cache_key in self._indicators_cache and
            cache_key in self._indicators_cache_timestamps and
            current_time - self._indicators_cache_timestamps[cache_key] < self._indicators_cache_ttl):
            self.logger.debug(f"Usando indicador do cache: {cache_key}")
            return self._indicators_cache[cache_key]

        # Calcular novo valor
        result = calc_func(*args, **kwargs)

        # Armazenar no cache
        if result is not None:
            self._indicators_cache[cache_key] = result
            self._indicators_cache_timestamps[cache_key] = current_time
            self.logger.debug(f"Indicador calculado e armazenado no cache: {cache_key}")

        return result

    def _get_ticker_from_bulk(self, symbol: str, tickers: Dict) -> Optional[Dict]:
        """
        Obtém ticker de dados bulk ou do cache individual.

        Args:
            symbol: Símbolo do par de trading
            tickers: Dicionário de tickers obtidos em bulk

        Returns:
            Dados do ticker ou None
        """
        # Primeiro tentar dos dados bulk
        if tickers and symbol in tickers:
            ticker = tickers[symbol]
            # Atualizar cache individual do cliente
            self.client.tickers_cache[symbol] = ticker
            self.client.last_tickers_updated[symbol] = time.time()
            return ticker

        # Fallback para cache individual ou nova requisição
        return self.client.get_ticker(symbol)

    def _cleanup_expired_cache(self):
        """
        Remove entradas expiradas do cache de indicadores e volatilidade para evitar acúmulo excessivo.
        """
        import time
        current_time = time.time()
        expired_keys = []

        # Limpar cache de indicadores
        for key, timestamp in self._indicators_cache_timestamps.items():
            if current_time - timestamp > self._indicators_cache_ttl:
                expired_keys.append(key)

        for key in expired_keys:
            self._indicators_cache.pop(key, None)
            self._indicators_cache_timestamps.pop(key, None)

        if expired_keys:
            self.logger.debug(f"Removidas {len(expired_keys)} entradas expiradas do cache de indicadores")

        # Limpar cache de volatilidade
        self.volatility_calculator.cleanup_expired_cache()

        # Limpar cache de ATR
        self.atr_calculator.cleanup_expired_cache()

        # Limpar cache de decisões de tendência
        self.trend_validator.cleanup_expired_cache()

        # Limpar cache de posições
        self.position_sizer.cleanup_expired_cache()

        # Limpar cache de risco
        self.risk_manager.cleanup_expired_cache()

    def validate_order_conditions(self, symbol: str) -> bool:
        """
        Valida se as condições estão adequadas para colocar uma ordem.
        Inclui verificação de ordens tp/sl (oco).

        Args:
            symbol: Par de trading

        Returns:
            True se condições estão OK, False caso contrário
        """
        return self.validator.validate_order_conditions(symbol, strategy_type="oco")

    def _generate_client_order_id(self) -> str:
        """Gera um ID único para a ordem."""
        return uuid.uuid4().hex[:32]

    def has_active_oco_orders(self, symbol: str) -> bool:
        """
        Verifica especificamente se há ordens tp/sl (oco) ativas.

        Args:
            symbol: Par de trading

        Returns:
            True se há ordens OCO ativas, False caso contrário
        """
        try:
            # Verificar primeiro no cache
            open_orders = self.client.order_cache.get_open_orders(symbol)
            if open_orders is not None:
                oco_orders = [
                    order
                    for order in open_orders
                    if order.get("info", {}).get("oco_details")
                ]
                active_count = len(oco_orders)
                if active_count > 0:
                    return True
                return False

            # Se não estiver no cache, buscar da exchange
            open_orders = check_oco_orders(self.client, symbol)
            active_count = len(open_orders)

            if active_count > 0:
                for order in open_orders:
                    order_id = order.get("id", "N/A")
                    status = order.get("status", "N/A")
                    self.logger.debug(
                        "Ordem OCO ativa: ID=%s, Status=%s", order_id, status
                    )
                return True

            return False
        except Exception as exc:
            self.logger.debug("Erro ao buscar dados para %s: %s", symbol, str(exc))
            self.logger.info("O símbolo %s não existe na exchange.", symbol)
            return False # Return false to avoid blocking on API errors

    def validate_trend_conditions(self, symbol: str, market_data: Dict) -> bool:
        """
        Versão otimizada de validação de tendência com menos cálculos redundantes.

        Args:
            symbol: Trading pair symbol
            market_data: Dict contendo dados já calculados do mercado
                        {'close': float, 'wma': float, 'rsi': float, 'wma_trend_up': bool}

        Returns:
            bool: True if trend conditions are favorable
        """
        try:
            config = self.client.config

            # Usar dados já calculados - evita recálculos
            current_close = market_data['close']
            current_wma = market_data['wma']
            current_rsi = market_data['rsi']
            wma_trend_up = market_data.get('wma_trend_up', False)

            # Verificação rápida para modo estrito
            if not getattr(config, 'ENABLE_FLEXIBLE_ENTRY', True):
                result = current_close > current_wma and current_rsi > 50
                if not result:
                    self.logger.info(
                        f"Strict mode: Trend not favorable for {symbol} "
                        f"(Price: {current_close:.2f}, WMA: {current_wma:.2f}, RSI: {current_rsi:.2f})"
                    )
                return result

            # Cálculo otimizado da distância preço vs WMA
            price_vs_wma_pct = ((current_close - current_wma) / current_wma) * 100

            # Condições de entrada flexível otimizadas
            wma_tolerance_pct = getattr(config, 'WMA_TOLERANCE_PCT', 0.5)
            rsi_min_threshold = getattr(config, 'RSI_MIN_THRESHOLD', 45)

            conditions = [
                (current_close > current_wma and current_rsi > 50, "traditional_bullish"),
                (abs(price_vs_wma_pct) <= wma_tolerance_pct and wma_trend_up, "wma_trend_breakout"),
                (current_rsi >= rsi_min_threshold and price_vs_wma_pct > -1.0, "rsi_support"),
                (current_rsi > 60 and price_vs_wma_pct > -0.75, "rsi_momentum")
            ]

            # Verificação otimizada das condições
            for condition, reason in conditions:
                if condition:
                    self.logger.info(f"✅ Trade approved for {symbol} - Reason: {reason}")
                    self.logger.debug(
                        f"Trend data for {symbol}: Price={current_close:.2f}, "
                        f"WMA={current_wma:.2f}, RSI={current_rsi:.2f}, "
                        f"Price vs WMA={price_vs_wma_pct:.2f}%"
                    )
                    return True

            # Se nenhuma condição foi atendida
            self.logger.info(f"❌ Trade rejected for {symbol} - No conditions met")
            self.logger.debug(
                f"Failed conditions for {symbol}: Price={current_close:.2f}, "
                f"WMA={current_wma:.2f}, RSI={current_rsi:.2f}, "
                f"Price vs WMA={price_vs_wma_pct:.2f}%, WMA Trend Up={wma_trend_up}"
            )
            return False

        except Exception as e:
            self.logger.error(f"Error validating trend for {symbol}: {str(e)}")
            return False

    def place_buy_order_with_oco(
        self, symbol: str, indicator: TechnicalIndicator, timeframe: str = "15m", tickers: Optional[Dict] = None
    ) -> Optional[Dict]:
        """
        Coloca ordem de compra com OCO (One Cancels the Other) usando 10% do saldo.
        O Stop Loss (SL) é definido como 1.75x o ATR abaixo do preço de entrada.
        O Take Profit (TP) é definido como 1.75x o ATR acima do preço de entrada.
        Verifica tendência antes de entrar e implementa timeouts inteligentes.
        """
        try:
            # Limpar cache expirado periodicamente
            self._cleanup_expired_cache()

            # Verificar se há ordens existentes com timeout de 5 minutos
            existing_orders = check_oco_orders(self.client, symbol)
            if existing_orders:
                # Verificar se alguma ordem está pendente há mais de 5 minutos
                current_time = time.time()
                for order in existing_orders:
                    order_time = order.get("timestamp", 0)
                    if current_time - order_time > 300:  # 5 minutos
                        self.logger.info(
                            f"Ordem pendente há mais de 5 minutos para {symbol}, cancelando..."
                        )
                        try:
                            self.client.exchange.cancel_order(order["id"], symbol)
                        except Exception as e:
                            self.logger.error(f"Erro ao cancelar ordem: {e}")
                return None

            # Preparar dados de mercado otimizados
            market_data = self.market_processor.prepare_market_data(
                symbol, indicator, timeframe, tickers
            )

            if not market_data:
                self.logger.error(f"Não foi possível preparar dados de mercado para {symbol}")
                return None

            # Validação de tendência otimizada com cache de decisões
            if not self.trend_validator.validate_with_cache(symbol, market_data):
                return None

            # Usar preço já obtido dos dados de mercado
            current_close = market_data['close']

            # Obter melhor bid (otimizado - usar preço atual como fallback)
            best_bid = self.client.get_best_bid(symbol)
            if not best_bid or not isinstance(best_bid, (int, float)) or best_bid <= 0:
                # Usar preço atual como fallback se bid não disponível
                entry_price = current_close
                self.logger.warning(f"Usando preço atual como entry para {symbol}: {entry_price}")
            else:
                entry_price = float(best_bid)
                self.logger.debug("Melhor bid obtido para %s: %s", symbol, entry_price)

            # Verificar saldo
            balance = self.client.get_balance()
            quote_currency = symbol.split("/")[1]
            available_balance = balance["total"].get(quote_currency, 0)
            if available_balance <= 0:
                self.logger.error(
                    "Saldo insuficiente em %s para %s", quote_currency, symbol
                )
                return None

            # Calcular ATR uma única vez para reutilizar em todos os cálculos
            atr_cache_key = f"atr_{symbol}_{timeframe}_14"
            atr = self._get_cached_indicator(
                atr_cache_key,
                indicator.calculate_atr,
                symbol,
                timeframe,
                period=14
            )

            if atr is None:
                self.logger.error("Não foi possível calcular ATR para %s", symbol)
                return None

            # Cálculo otimizado de tamanho de posição usando dados pré-calculados
            position_data = {'position_size': 0.1}  # Default

            if (
                hasattr(self.client.config, "ENABLE_DYNAMIC_POSITION_SIZING")
                and self.client.config.ENABLE_DYNAMIC_POSITION_SIZING
            ):
                # Usar ATR já calculado para múltiplas medidas de volatilidade
                if atr is not None:
                    # Obter dados históricos para análise (cache já implementado no TechnicalIndicator)
                    lookback_periods = getattr(
                        self.client.config, "ATR_LOOKBACK_PERIODS", 100
                    )
                    ohlcv_data = indicator.fetch_historical_data(
                        symbol, timeframe, limit=lookback_periods
                    )

                    if ohlcv_data and len(ohlcv_data) > 1:
                        closes = np.array([x[4] for x in ohlcv_data])

                        # Calcular ATR values para volatilidade
                        atr_values = talib.ATR(
                            np.array([x[2] for x in ohlcv_data]),
                            np.array([x[3] for x in ohlcv_data]),
                            np.array([x[4] for x in ohlcv_data]),
                            timeperiod=14,
                        )

                        # Usar calculadora de volatilidade otimizada
                        volatility_metrics = self.volatility_calculator.calculate_weighted_volatility(
                            symbol, closes, atr_values
                        )

                        # Calcular fator de correlação com BTC usando calculadora otimizada
                        returns = np.diff(closes) / closes[:-1]
                        btc_corr = 1.0  # Default
                        if symbol != "BTC/USDC":
                            btc_data = indicator.fetch_historical_data(
                                "BTC/USDC", timeframe, limit=lookback_periods
                            )
                            if btc_data and len(btc_data) > 1:
                                btc_closes = np.array([x[4] for x in btc_data])
                                btc_returns = np.diff(btc_closes) / btc_closes[:-1]
                                btc_corr = self.volatility_calculator.calculate_correlation_factor(
                                    symbol, returns, btc_returns
                                )

                        # Adicionar correlação aos dados de volatilidade
                        volatility_metrics['btc_correlation'] = btc_corr

                        # Usar novo calculador otimizado de posição
                        position_data = self.position_sizer.calculate_position_size(
                            symbol, volatility_metrics
                        )

                    if ohlcv_data and len(ohlcv_data) > 14:
                        # Usar calculadora otimizada de ATR para ajuste adicional
                        atr_stats = self.atr_calculator.calculate_atr_statistics(
                            ohlcv_data, period=14, lookback=20
                        )

                        # Aplicar ajuste de volatilidade se necessário
                        if atr_stats['ratio'] > 1.2:  # Alta volatilidade
                            position_data['position_size'] *= 0.8  # Reduzir posição
                            position_data['volatility_adjustment'] = 0.8
                            position_data['reason'] = 'high_atr_volatility'
                        elif atr_stats['ratio'] < 0.8:  # Baixa volatilidade
                            position_data['position_size'] *= 1.1  # Aumentar posição
                            position_data['volatility_adjustment'] = 1.1
                            position_data['reason'] = 'low_atr_volatility'

                        # Notificação de ajuste de volatilidade
                        if (
                            hasattr(self.client.config, "ENABLE_TELEGRAM_NOTIFICATIONS")
                            and self.client.config.ENABLE_TELEGRAM_NOTIFICATIONS
                            and volatility_ratio > high_threshold
                        ):
                            message = f"⚠️ *Alta Volatilidade Detectada - {symbol}*\n• Tamanho da posição reduzido para {position_size * 100:.1f}%\n• Razão ATR: {volatility_ratio:.2f}"
                            try:
                                import asyncio
                                asyncio.run_coroutine_threadsafe(
                                    self.send_telegram_notification(message),
                                    asyncio.get_event_loop(),
                                )
                            except Exception as e:
                                self.logger.error(f"Erro ao enviar notificação: {e}")
                    else:
                        self.logger.warning(
                            "Dados históricos insuficientes para calcular média ATR de %s, usando tamanho padrão",
                            symbol,
                        )
                else:
                    self.logger.warning(
                        "Não foi possível calcular ATR para %s, usando tamanho padrão",
                        symbol,
                    )
            else:
                # Usar tamanho padrão se dynamic sizing desabilitado
                position_data = {
                    'position_size': 0.1,
                    'reason': 'dynamic_sizing_disabled',
                    'volatility_adjusted': False
                }

            # Extrair tamanho de posição calculado
            position_size = position_data['position_size']

            # Calcular quantidade da ordem usando novo sistema
            order_calculation = self.position_sizer.calculate_amount_from_balance(
                position_size, available_balance, entry_price
            )

            amount = order_calculation['amount']
            balance_to_use = order_calculation['balance_used']
            ten_percent_balance = balance_to_use  # Para compatibilidade

            formatted_amount = self.client.format_amount_with_precision(symbol, amount)
            formatted_price = self.client.format_price_with_precision(symbol, entry_price)
            
            if float(formatted_amount) <= 0:
                self.logger.error(
                    "Quantidade calculada insuficiente para %s: %s",
                    symbol,
                    formatted_amount,
                )
                return None
            
            # Logging detalhado do novo sistema de posições
            self.logger.info(
                f"Position calculation for {symbol}:\n"
                f"  • Position Size: {position_size:.2%}\n"
                f"  • Amount: {formatted_amount}\n"
                f"  • Entry Price: {formatted_price}\n"
                f"  • Balance Used: ${balance_to_use:.2f}\n"
                f"  • Reason: {position_data.get('reason', 'unknown')}\n"
                f"  • Volatility Adjusted: {position_data.get('volatility_adjusted', False)}\n"
                f"  • Risk Level: {position_data.get('risk_level', 'unknown')}"
            )

            # Usar gerenciador de risco otimizado para calcular SL/TP
            # Passar dados de volatilidade se disponíveis
            volatility_data_for_risk = None
            if 'volatility_metrics' in locals():
                volatility_data_for_risk = volatility_metrics

            risk_levels = self.risk_manager.calculate_sl_tp_levels(
                symbol, entry_price, atr, volatility_data_for_risk
            )

            # Extrair valores calculados
            sl_trigger = risk_levels['sl_trigger']
            sl_price = risk_levels['sl_price']
            tp_trigger = risk_levels['tp_trigger']
            tp_price = risk_levels['tp_price']

            # Formatar preços com precisão adequada
            formatted_sl_trigger = self.client.format_price_with_precision(symbol, sl_trigger)
            formatted_sl_price = self.client.format_price_with_precision(symbol, sl_price)
            formatted_tp_trigger = self.client.format_price_with_precision(symbol, tp_trigger)
            formatted_tp_price = self.client.format_price_with_precision(symbol, tp_price)

            # Definir parâmetros da ordem OCO
            order_params = {
                "orderType": "oco",
                "postOnly": False,
                "stopLoss": {
                    "triggerPrice": formatted_sl_trigger,
                    "price": formatted_sl_price,
                },
                "takeProfit": {
                    "triggerPrice": formatted_tp_trigger,
                    "price": formatted_tp_price,
                },
            }

            # Calculate max acceptable slippage (0.2% of entry price)
            max_slippage = entry_price * 0.002
            try:
                ticker = self.client.exchange.fetch_ticker(symbol)
                current_price = ticker.get("last", 0.0)
                if current_price <= 0.0:
                    self.logger.error(f"Preço atual inválido obtido para {symbol}")
                    return None
            except Exception as e:
                self.logger.error(f"Erro ao obter preço atual para {symbol}: {str(e)}")
                return None

            # Verify price hasn't moved beyond acceptable slippage
            if abs(current_price - entry_price) > max_slippage:
                self.logger.warning(
                    f"Price slippage too high for {symbol}: "
                    f"Requested {entry_price}, Current {current_price}, "
                    f"Slippage {abs(current_price - entry_price)/entry_price*100:.2f}%"
                )
                return None

            # Place order with price validation
            try:
                limit_order = self.client.exchange.create_order(
                    symbol=symbol,
                    type="limit",
                    side="buy",
                    amount=(
                        float(formatted_amount) if formatted_amount is not None else 0.0
                    ),
                    price=entry_price,
                    params={
                        **order_params,
                        "priceProtect": True,  # Enable exchange's price protection
                        "timeInForce": "GTC",  # Good Till Cancelled
                    },
                )
            except ccxt.NetworkError as e:
                self.logger.error(f"Network error placing order for {symbol}: {str(e)}")
                return None
            except ccxt.ExchangeError as e:
                self.logger.error(
                    f"Exchange error placing order for {symbol}: {str(e)}"
                )
                return None

            if "amount" not in limit_order or limit_order["amount"] is None:
                limit_order["amount"] = float(formatted_amount)

            self.logger.info(
                "Ordem limite com OCO criada com sucesso: %s", limit_order.get("id")
            )

            # Adicionar informações OCO ao campo additional_info para armazenamento no banco de dados
            oco_info = {
                "oco_details": {
                    "sl_trigger": float(formatted_sl_trigger),
                    "sl_price": float(formatted_sl_price),
                    "tp_trigger": float(formatted_tp_trigger),
                    "tp_price": float(formatted_tp_price),
                }
            }
            if "info" not in limit_order:
                limit_order["info"] = {}
            limit_order["info"].update(oco_info)

            result = {
                "limit_order": limit_order,
                "entry_price": float(formatted_price),
                "amount": float(formatted_amount),
                "symbol": symbol,
                "ten_percent_used": ten_percent_balance,
                "sl_trigger": float(formatted_sl_trigger),
                "sl_price": float(formatted_sl_price),
                "tp_trigger": float(formatted_tp_trigger),
                "tp_price": float(formatted_tp_price),
            }

            self.logger.info("Ordem OCO processada com sucesso para %s", symbol)
            return result

        except Exception as exc:
            self.logger.error("Erro ao colocar ordem OCO para %s: %s", symbol, str(exc))
            return None

            
        def log_order_prevention_details(self, symbol: str) -> None:
            """
            Registra detalhes sobre prevenção de criação de ordens.

            Args:
                symbol: Par de trading
            """
            try:
                trailing_orders = check_oco_orders(self.client, symbol)

                if trailing_orders:
                    self.logger.info("=== PREVENÇÃO DE ORDEM ATIVADA ===")
                    self.logger.info("Símbolo: %s", symbol)
                    self.logger.info("Trailing stops ativos: %d", len(trailing_orders))

                    for i, order in enumerate(trailing_orders, 1):
                        self.logger.info("Trailing Stop #%d:", i)
                        self.logger.info("  - ID: %s", order.get("id", "N/A"))
                        self.logger.info("  - Status: %s", order.get("status", "N/A"))
                        self.logger.info("  - Lado: %s", order.get("side", "N/A"))
                        self.logger.info("  - Quantidade: %s", order.get("amount", "N/A"))
                        self.logger.info(
                            "  - Trigger: %s", order.get("triggerPrice", "N/A")
                        )

                    self.logger.info("Nova ordem BLOQUEADA para %s", symbol)
                    self.logger.info("=====================================")

            except Exception as exc:
                self.logger.error("Erro ao registrar detalhes de prevenção: %s", str(exc))


class OCOTradingBot(TradingBotBase):
    """Bot de trading específico para estratégia OCO WMA."""

    async def _execute_strategy(
        self,
        client: OKXClient,
        indicator: TechnicalIndicator,
        signal_checker: SinaisWmaRsi,
        order_manager: OKXOrder,
        strategy_type: str,
    ) -> None:
        """
        Implementação da lógica de trading específica para estratégia OCO WMA.

        Args:
            client: Instance of OKXClient for exchange interactions.
            indicator: TechnicalIndicator instance for calculating indicators.
            signal_checker: SinaisWmaRsi instance for checking entry signals.
            order_manager: OKXOrder instance for managing order placement.
            strategy_type: Type of strategy for notifications.
        """
        print("\n🤖 Verificando Sinais de Trading:")
        print("=" * 50)

        # Initialize database for order tracking
        db = TradingDatabase("trades/sandbox_trades.db")

        # Bulk ticker fetch for order checks
        tickers = client.exchange.fetch_tickers(client.config.TRADING_SYMBOLS)

        for symbol in client.config.TRADING_SYMBOLS:
            try:
                # Check for closed OCO orders and update associations
                closed_orders = check_closed_oco_orders(client, symbol, db)
                if closed_orders:
                    print(
                        f"🔄 Processed {len(closed_orders)} closed OCO orders for {symbol}"
                    )

                # Exibir sinal atual
                signal_checker.print_signal(symbol, timeframe=None, tickers=tickers)

                # Always check entry signal, regardless of closed_orders
                has_buy_signal = signal_checker.check_entry_signal(
                    symbol, timeframe=None, tickers=tickers
                )

                # NOVA LÓGICA: Verificar trailing stops antes de processar sinal
                if order_manager.has_active_oco_orders(symbol):
                    continue  # Pula para o próximo símbolo

                # Se há sinal de compra, processar ordem
                if has_buy_signal:
                    print(f"\n🚨 SINAL DE COMPRA DETECTADO PARA {symbol}!")

                    # Validar condições antes de colocar ordem (já inclui verificação de trailing stop)
                    if order_manager.validate_order_conditions(symbol):
                        print(f"✅ Condições validadas para {symbol}")

                        # Colocar ordem passando os tickers bulk
                        order_result = order_manager.place_buy_order_with_oco(
                            symbol=symbol, indicator=indicator, tickers=tickers
                        )

                        if order_result:
                            print(f"🎯 ORDEM Stop Loss EXECUTADA COM SUCESSO!")
                            print(f"• Símbolo: {symbol}")
                            print(f"• Quantidade: {order_result['amount']}")
                            print(f"• Preço: ${order_result['entry_price']}")
                            print(
                                f"• Valor usado: ${order_result['ten_percent_used']:.2f}"
                            )
                            print(
                                f"• ID da ordem: {order_result['limit_order'].get('id', 'N/A')}"
                            )

                            # Salvar no banco de dados
                            self.save_order_to_db(order_result["limit_order"])
                            self.created_orders.append(order_result["limit_order"])

                            # Notificações
                            await self.play_alert("success", volume=0.7)

                            message = f"""🎯 *ORDEM Stop Loss EXECUTADA*
        • *Bot*: {self.config.SCRIPT_NAME}
        • *Símbolo*: {symbol}
        • *Tipo*: OCO (Limit)
        • *Quantidade*: {order_result['amount']}
        • *Preço*: ${order_result['entry_price']}
        • *Valor*: ${order_result['ten_percent_used']:.2f}
        """
                            await self.send_telegram_notification(message)

                        else:
                            print(f"❌ Falha ao executar ordem para {symbol}")
                            await self.play_alert("error", volume=0.5)
                    else:
                        pass
                else:
                    self.logger.info(f"OCO | {symbol} -> Wait")
                    print()

            except Exception as exc:
                self.logger.error("Erro ao processar %s: %s", symbol, str(exc))
                print(f"❌ Erro ao processar {symbol}: {exc}")


async def main() -> None:
    """Função principal com auto-trading implementado para estratégia OCO WMA."""
    logger = TradingLogger.get_logger(__name__)

    try:
        config = BotConfig(strategy="OCO_wma_rsi")
        config.SCRIPT_NAME = "999 Base OCO WMA"
        client = OKXClient(config)
        # Ensure TRADING_SYMBOLS from config are used in client
        client.config.TRADING_SYMBOLS = config.TRADING_SYMBOLS
        order_manager = OKXOrder(
            client
        )  # Inicializar order_manager específico para OCO
        bot = OCOTradingBot(config)
        await send_startup_notification(client, bot)
        await bot.play_alert("start", volume=0.25)

        indicator = TechnicalIndicator(client)

        # Inicializar dados históricos
        for symbol in client.config.TRADING_SYMBOLS:
            indicator.fetch_historical_data(symbol, client.config.TIMEFRAME)

        signal_checker = SinaisWmaRsi(indicator, client)
        await bot.run_trading_loop(
            client, indicator, signal_checker, order_manager, "OCO"
        )

    except KeyboardInterrupt:
        logger.info("Bot interrompido manualmente pelo usuário.")
        print("\n🛑 Bot interrompido manualmente.")
        if "bot" in locals():
            await bot.play_alert("exit", volume=0.9)
        print("A finalizar o bot. Até breve!")
        return

    except (ConfigurationError, ExchangeConnectionError) as exc:
        logger.error("Erro de configuração/conexão: %s", exc)
        print(f"❌ Erro: {exc}")
        print("Por favor, verifique as suas credenciais e configuração.")

    except Exception as exc:
        logger.error("Erro inesperado: %s", exc)
        logger.exception(exc)
        print(f"❌ Erro inesperado: {exc}")
        print("Verifique os logs para mais detalhes.")


if __name__ == "__main__":
    asyncio.run(main())
