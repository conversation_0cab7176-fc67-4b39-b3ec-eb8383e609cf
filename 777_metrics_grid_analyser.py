import streamlit as st
import plotly.express as px
import pandas as pd
from datetime import datetime
import plotly.graph_objects as go
from typing import Dict, List, Optional
from utils.monitor_grid_orders import OKXGridBotMonitor
import traceback


# Initialize OKXGridBotMonitor
@st.cache_resource
def init_monitor():
    try:
        monitor = OKXGridBotMonitor()
        return monitor
    except Exception as e:
        st.error(f"Failed to initialize OKX connection: {str(e)}")
        return None


# Fetch real data from OKXGridBotMonitor
def fetch_real_data(monitor):
    if not monitor:
        return None

    try:
        # Fetch active bots data
        active_data = monitor.monitor_active_bots()
        detailed_active_bots = active_data.get("active_bots", [])

        # Fetch historical bots
        history = monitor.get_bot_history(20)

        # Structure data similar to the original export format
        data = {
            "export_timestamp": datetime.now().isoformat(),
            "summary": {
                "active_bots_count": len(detailed_active_bots),
                "history_count": len(history),
            },
            "active_bots": detailed_active_bots,
            "history": history,
        }
        return data

    except Exception as e:
        st.error(f"Error fetching data: {str(e)}")
        st.write(traceback.format_exc())
        return None


# Helper functions (same as original)
def format_currency(value: str, currency: str = "USDT") -> str:
    try:
        num_value = float(value)
        return f"{num_value:,.2f} {currency}"
    except (ValueError, TypeError):
        return f"{value} {currency}"


def calculate_roi(total_pnl: str, investment: str) -> str:
    try:
        pnl_val = float(total_pnl)
        inv_val = float(investment)
        if inv_val > 0:
            roi = (pnl_val / inv_val) * 100
            return f"{roi:+.2f}%"
        return "N/A"
    except (ValueError, TypeError, ZeroDivisionError):
        return "N/A"


def get_pnl_status_emoji(pnl: str) -> str:
    try:
        pnl_val = float(pnl)
        if pnl_val > 0:
            return "🟢"
        elif pnl_val < 0:
            return "🔴"
        else:
            return "⚪"
    except (ValueError, TypeError):
        return "⚪"


def calculate_sharpe_ratio(
    pnl: float, risk_free_rate: float = 0.02, periods: int = 252
) -> str:
    """
    Calculate Sharpe Ratio for a bot's performance.
    Assuming annualized risk-free rate and daily returns for simplicity.
    """
    try:
        if pnl > 0:
            # Simplified: Assuming PNL is over a year for demonstration
            excess_return = pnl - risk_free_rate
            volatility = pnl * 0.2  # Placeholder for volatility (20% of return)
            if volatility > 0:
                sharpe = excess_return / volatility * (periods**0.5)
                return f"{sharpe:.2f}"
        return "N/A"
    except (ValueError, TypeError, ZeroDivisionError):
        return "N/A"


def calculate_sortino_ratio(
    pnl: float, risk_free_rate: float = 0.02, periods: int = 252
) -> str:
    """
    Calculate Sortino Ratio for a bot's performance.
    Focuses on downside risk only.
    """
    try:
        if pnl > 0:
            excess_return = pnl - risk_free_rate
            downside_volatility = (
                pnl * 0.1
            )  # Placeholder for downside volatility (10% of return)
            if downside_volatility > 0:
                sortino = excess_return / downside_volatility * (periods**0.5)
                return f"{sortino:.2f}"
        return "N/A"
    except (ValueError, TypeError, ZeroDivisionError):
        return "N/A"


def calculate_max_drawdown(pnl: float) -> str:
    """
    Calculate Maximum Drawdown as a percentage of peak value.
    Simplified for demonstration.
    """
    try:
        if pnl < 0:
            drawdown = abs(pnl) / (abs(pnl) + 100) * 100  # Placeholder logic
            return f"{drawdown:.2f}%"
        return "0.00%"
    except (ValueError, TypeError):
        return "N/A"


# Streamlit app
st.set_page_config(page_title="OKX Grid Bot Dashboard", layout="wide", page_icon="🤖")

# Sem tema personalizado, usar o padrão do Streamlit

st.title("🤖 OKX Grid Bot Monitor Dashboard")
st.markdown(
    "Monitorize e visualize o desempenho dos seus bots de grid OKX com insights em tempo real.",
    unsafe_allow_html=True
)

# ————————————————
# CARREGUE OS DADOS ANTES
monitor = init_monitor()
data = fetch_real_data(monitor)
if not data:
    st.warning("No data…")
    st.stop()

# ————————————————
# 1) BARRA LATERAL (SEM FILTROS)
with st.sidebar:
    st.header("📊 Dashboard")
    st.markdown("Monitorize os seus bots de grid OKX com dados em tempo real.")

# ————————————————
# 2) CRIAR DataFrame DE ACTIVE BOTS
bot_data = []
for bot in data["active_bots"]:
    bot_info = bot.get("bot_data", bot)  # Fallback to bot itself if no bot_data key
    details = bot.get("details", {})
    total_pnl_val = float(bot_info.get("totalPnl", "0"))
    bot_data.append(
        {
            "ID": bot_info.get("algoId", "N/A"),
            "Symbol": bot_info.get("instId", "N/A"),
            "State": bot_info.get("state", "N/A").capitalize(),
            "Total PnL": format_currency(bot_info.get("totalPnl", "0")),
            "Grid Profit": format_currency(bot_info.get("gridProfit", "0")),
            "Unrealized PnL": format_currency(bot_info.get("floatProfit", "0")),
            "Investment": format_currency(bot_info.get("investment", "0")),
            "ROI": calculate_roi(
                bot_info.get("totalPnl", "0"), bot_info.get("investment", "0")
            ),
            "Sharpe Ratio": calculate_sharpe_ratio(total_pnl_val),
            "Sortino Ratio": calculate_sortino_ratio(total_pnl_val),
            "Max Drawdown": calculate_max_drawdown(total_pnl_val),
            "Arbitrage Count": details.get("arbitrageNum", "N/A"),
            "Annualized Rate": (
                f"{float(details.get('annualizedRate', 0)) * 100:.2f}%"
                if details.get("annualizedRate")
                else "N/A"
            ),
            "Created": (
                monitor.formatter.timestamp_to_date(details.get("cTime", "0"))
                if details.get("cTime")
                else "N/A"
            ),
        }
    )

df_bots = pd.DataFrame(bot_data)

# ————————————————
# 3) CRIAR COLUNA NUMÉRICA ROI
df_bots["ROI_num"] = (
    df_bots["ROI"]
      .str.rstrip("%")
      .replace("N/A", "0")
      .astype(float)
)

# ————————————————
# 4) SEM FILTROS APLICADOS
df_bots = df_bots.reset_index(drop=True)

st.header("📈 Métricas Gerais")
col_a, col_b, col_c, col_d = st.columns(4)
with col_a:
    st.metric("Média ROI (%)", f"{df_bots['ROI'].str.replace('%','').astype(float).mean():.2f}%", delta="🟢" if df_bots['ROI'].str.replace('%','').astype(float).mean() > 0 else "🔴")
with col_b:
    mean_sharpe = df_bots["Sharpe Ratio"].replace("N/A", "0").astype(float).mean()
    mean_sortino = df_bots["Sortino Ratio"].replace("N/A", "0").astype(float).mean()

    st.metric("Média Sharpe", f"{mean_sharpe:.2f}", delta="🟢" if mean_sharpe > 0 else "🔴")
    st.metric("Média Sortino", f"{mean_sortino:.2f}", delta="🟢" if mean_sortino > 0 else "🔴")
with col_c:
    total_pnl = sum(float(row['Total PnL'].split()[0].replace(',', '')) for _, row in df_bots.iterrows())
    st.metric("Total PnL (USD)", f"{total_pnl:,.2f}", delta=get_pnl_status_emoji(str(total_pnl)))
with col_d:
    st.metric("Bots Ativos", len(df_bots))

# Gráfico de Evolução do Portfólio
st.header("💰 Evolução do Portfólio")
portfolio_data = []
for bot in data["active_bots"]:
    if bot["details"] and bot["details"].get("cTime"):
        bot_info = bot.get("bot_data", bot)
        portfolio_data.append({
            "Timestamp": monitor.formatter.timestamp_to_date(bot["details"].get("cTime", "0")),
            "PnL": float(bot_info.get("totalPnl", 0))
        })

if portfolio_data:
    df_portfolio = pd.DataFrame(portfolio_data)
    df_portfolio["Timestamp"] = pd.to_datetime(df_portfolio["Timestamp"])
    df_portfolio = df_portfolio.sort_values("Timestamp")
    df_portfolio["Cumulative PnL"] = df_portfolio["PnL"].cumsum()

    fig_portfolio = px.line(
        df_portfolio,
        x="Timestamp",
        y="Cumulative PnL",
        title="Evolução do Portfólio (PnL Acumulado)",
        labels={"Timestamp": "Tempo", "Cumulative PnL": "PnL Acumulado (USD)"}
    )
    st.plotly_chart(fig_portfolio, use_container_width=True, height=300)
else:
    st.write("Dados de evolução do portfólio não disponíveis.")

# Nova seção para Grids Ativos em formato de cartão, 3 por linha
st.header("🟢 Grids Ativos")
if not df_bots.empty:
    # Criar linhas com 3 cartões cada
    for i in range(0, len(df_bots), 3):
        row_bots = df_bots.iloc[i:i+3]
        cols = st.columns(3)
        for idx, (col, (_, row)) in enumerate(zip(cols, row_bots.iterrows())):
            with col:
                with st.container():
                    st.markdown("---")
                    st.markdown(f"**Symbol:** {row['Symbol']}")
                    created_time = row['Created']
                    if created_time != "N/A":
                        time_diff = datetime.now() - pd.to_datetime(created_time)
                        days = time_diff.days
                        hours = time_diff.seconds // 3600
                        st.markdown(f"**Tempo de Atividade:** {days} dias, {hours} horas")
                    else:
                        st.markdown("**Tempo de Atividade:** N/A")
                    st.markdown(f"**Amount (USD):** {row['Investment']}")
                    total_pnl = row['Total PnL']
                    roi = row['ROI']
                    pnl_color = "green" if "+" in total_pnl else "red"
                    roi_color = "green" if "+" in row['ROI'] else "red"
                    st.markdown(f"**Total PnL (USD):** <span style='color:{pnl_color}'>{total_pnl}</span> (<span style='color:{roi_color}'>{roi}</span>)", unsafe_allow_html=True)
                    bot_id = row['ID']
                    bot_details = next((bot['details'] for bot in data['active_bots'] if bot.get('bot_data', bot).get('algoId') == bot_id), {})
                    price_range = f"{bot_details.get('minPx', 'N/A')} - {bot_details.get('maxPx', 'N/A')}"
                    current_price = bot_details.get('curPx', 'N/A')
                    st.markdown(f"**Price Range:** {price_range}")
                    st.markdown(f"**Current Price:** {current_price}")
                    st.markdown("---")
else:
    st.write("Nenhum grid ativo encontrado.")
# Sem paginação, exibir todos os dados
st.dataframe(df_bots, use_container_width=True)

import plotly.express as px

with st.expander("📊 Distribuição de ROI"):
    fig_roi = px.histogram(
        df_bots,
        x="ROI_num",
        nbins=20,
        title="Histograma de ROI (%)",
        labels={"ROI_num": "ROI (%)"},
        color_discrete_sequence=["#10b981"] if df_bots["ROI_num"].mean() > 0 else ["#ef4444"]
    )
    st.plotly_chart(fig_roi, use_container_width=True, height=300)

with st.expander("🏆 Top 5 Bots por ROI"):
    top5 = (
        df_bots
        .assign(ROI_num=df_bots["ROI"]
                .str.rstrip("%")
                .replace("N/A", "0")
                .astype(float))
        .nlargest(5, "ROI_num")
    )
    fig_top5 = px.bar(
        top5,
        x="ID",
        y="ROI_num",
        color="Symbol",
        title="Top 5 Bots por ROI (%)",
        labels={"ROI_num": "ROI (%)"},
        color_discrete_sequence=px.colors.qualitative.Bold
    )
    st.plotly_chart(fig_top5, use_container_width=True, height=300)

# Bot Selection for Detailed View
st.header("🔍 Bot Details")
bot_ids = [bot["bot_data"].get("algoId", "N/A") for bot in data["active_bots"]]
selected_bot_id = st.selectbox("Select Bot ID", bot_ids)
selected_bot = next(
    (
        bot
        for bot in data["active_bots"]
        if bot["bot_data"].get("algoId") == selected_bot_id
    ),
    None,
)

# Add error handling
if selected_bot is None:
    st.error("No bot found with the selected ID. Please choose a valid bot.")
    st.stop()

# Add loading state
with st.spinner("Loading bot details..."):
    # Cache the bot data for better performance
    @st.cache_data(ttl=300)  # Cache for 5 minutes
    def get_bot_details(bot_id):
        return next(
            (
                bot
                for bot in data["active_bots"]
                if bot["bot_data"].get("algoId") == bot_id
            ),
            None,
        )
    
    selected_bot = get_bot_details(selected_bot_id)
if selected_bot:
    st.subheader(
        f"Bot {selected_bot_id} ({selected_bot['bot_data'].get('instId', 'N/A')})"
    )
    col1, col2 = st.columns(2)
    total_pnl_val = float(selected_bot["bot_data"].get("totalPnl", "0"))
    with col1:
        st.metric(
            "Total PnL",
            format_currency(selected_bot["bot_data"].get("totalPnl", "0")),
            delta=get_pnl_status_emoji(selected_bot["bot_data"].get("totalPnl", "0")),
        )
        st.metric(
            "Investment",
            format_currency(selected_bot["bot_data"].get("investment", "0")),
        )
        st.metric(
            "ROI",
            calculate_roi(
                selected_bot["bot_data"].get("totalPnl", "0"),
                selected_bot["bot_data"].get("investment", "0"),
            ),
        )
        st.metric("Sharpe Ratio", calculate_sharpe_ratio(total_pnl_val))
        st.metric("Sortino Ratio", calculate_sortino_ratio(total_pnl_val))
    with col2:
        st.metric("Max Drawdown", calculate_max_drawdown(total_pnl_val))
        st.metric("Arbitrage Count", selected_bot["details"].get("arbitrageNum", "N/A"))
        st.metric("Grid Count", selected_bot["details"].get("gridNum", "N/A"))
        st.metric(
            "Price Range",
            f"{selected_bot['details'].get('minPx', 'N/A')} - {selected_bot['details'].get('maxPx', 'N/A')}",
        )

    # Sub-orders Chart
    st.subheader("📋 Sub-Orders")
    sub_orders = selected_bot["sub_orders"]
    if sub_orders:
        @st.cache_data(ttl=300)
        def load_bot_suborders(bot):
            return pd.DataFrame(bot["sub_orders"])

        with st.spinner("Carregando sub-orders…"):
            df_sub = load_bot_suborders(selected_bot)
            if not df_sub.empty:
                df_sub["px"] = pd.to_numeric(df_sub["px"], errors="coerce")
                df_sub["sz"] = pd.to_numeric(df_sub["sz"], errors="coerce")
                df_sub["cTime"] = pd.to_datetime(
                    df_sub["cTime"].astype(str).str.slice(0, 13),
                    format="%Y%m%d%H%M%S",
                    errors="coerce",
                )
                # Filtrar linhas com NaN em px ou cTime para evitar erros no gráfico
                df_sub = df_sub.dropna(subset=["px", "cTime"])

                # Scatter plot with time dimension
                fig_scatter = px.scatter(
                    df_sub,
                    x="px",
                    y="sz",
                    color="side",
                    size="sz",
                    hover_data=["cTime", "px", "sz", "side"],
                    title="Sub-Orders: Price vs Size (Sized by Volume)",
                    labels={"px": "Price (USDT)", "sz": "Size"},
                    color_discrete_map={"buy": "green", "sell": "red"},
                )

                # Add time series of orders
                fig_time = px.line(
                    df_sub.sort_values("cTime"),
                    x="cTime",
                    y="px",
                    color="side",
                    title="Sub-Orders: Price Over Time",
                    labels={"cTime": "Time", "px": "Price (USDT)"},
                    color_discrete_map={"buy": "green", "sell": "red"},
                )

                st.plotly_chart(fig_scatter, use_container_width=True, height=300)
                st.plotly_chart(fig_time, use_container_width=True, height=300)
            else:
                st.write("No sub-orders available.")
    else:
        st.write("No sub-orders available.")

# Historical Bots
st.header("📚 Historical Bots")
history_data = []
for bot in data["history"]:
    bot_info = bot.get("bot_data", bot) if isinstance(bot, dict) else bot
    history_data.append(
        {
            "ID": bot_info.get("algoId", "N/A") if isinstance(bot_info, dict) else "N/A",
            "Symbol": bot_info.get("instId", "N/A") if isinstance(bot_info, dict) else "N/A",
            "State": bot_info.get("state", "N/A").capitalize() if isinstance(bot_info, dict) else "N/A",
            "Total PnL": format_currency(bot_info.get("totalPnl", "0") if isinstance(bot_info, dict) else "0"),
            "Investment": format_currency(bot_info.get("investment", "0") if isinstance(bot_info, dict) else "0"),
            "ROI": calculate_roi(
                bot_info.get("totalPnl", "0") if isinstance(bot_info, dict) else "0", 
                bot_info.get("investment", "0") if isinstance(bot_info, dict) else "0"
            ),
        }
    )

df_history = pd.DataFrame(history_data)
st.dataframe(df_history, use_container_width=True)

# PnL Trend
st.header("📈 PnL Trend")
pnl_data = []
for bot in data["active_bots"]:
    if bot["details"] and bot["details"].get("cTime"):
        try:
            timestamp = bot["details"].get("cTime", "0")
            # Convert timestamp to date if it's a string or integer
            if isinstance(timestamp, str) and timestamp.isdigit():
                ts = int(timestamp) / 1000  # Convert from milliseconds to seconds
                date_str = datetime.fromtimestamp(ts).strftime("%Y-%m-%d %H:%M:%S")
            elif isinstance(timestamp, int):
                ts = timestamp / 1000
                date_str = datetime.fromtimestamp(ts).strftime("%Y-%m-%d %H:%M:%S")
            else:
                date_str = timestamp

            pnl_data.append(
                {
                    "Bot ID": bot["bot_data"].get("algoId", "N/A"),
                    "Symbol": bot["bot_data"].get("instId", "N/A"),
                    "PnL": float(bot["bot_data"].get("totalPnl", 0)),
                    "Timestamp": date_str,
                }
            )
        except (ValueError, TypeError) as e:
            st.warning(f"Error processing timestamp for bot {bot['bot_data'].get('algoId', 'N/A')}: {e}")

if pnl_data:
    df_pnl = pd.DataFrame(pnl_data)
    df_pnl["Timestamp"] = pd.to_datetime(df_pnl["Timestamp"], errors="coerce")
    df_pnl = df_pnl.dropna(subset=["Timestamp"]).sort_values("Timestamp")

    if not df_pnl.empty:
        # Line chart for PnL over time
        fig_pnl_line = px.line(
            df_pnl,
            x="Timestamp",
            y="PnL",
            color="Bot ID",
            title="PnL Over Time by Bot",
            labels={"Timestamp": "Time", "PnL": "Profit and Loss (USDT)"},
            hover_data=["Bot ID", "Symbol", "PnL"],
        )

        # Area chart for cumulative PnL
        df_pnl["Cumulative PnL"] = df_pnl.groupby("Bot ID")["PnL"].cumsum()
        fig_pnl_area = px.area(
            df_pnl,
            x="Timestamp",
            y="Cumulative PnL",
            color="Bot ID",
            title="Cumulative PnL Over Time by Bot",
            labels={
                "Timestamp": "Time",
                "Cumulative PnL": "Cumulative Profit and Loss (USDT)",
            },
            hover_data=["Bot ID", "Symbol", "Cumulative PnL"],
        )

        st.plotly_chart(fig_pnl_line, use_container_width=True, height=300)
        st.plotly_chart(fig_pnl_area, use_container_width=True, height=300)
    else:
        st.write("No valid timestamp data available for PnL trend.")
else:
    st.write("No PnL trend data available.")

# Footer
st.markdown("---")
st.markdown("Built with Streamlit | Data sourced from OKX Grid Bot Monitor by Bluetuga")
