import pandas as pd
import talib
import optuna
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
import warnings

warnings.filterwarnings("ignore")

# === 1. Carregar dados ===
df = pd.read_csv("data/data_BTC_USDC_1h.csv")

# === 2. Calcular indicadores ===
df['rsi'] = talib.RSI(df['close'], timeperiod=14)
df['atr'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=14)
df['wma'] = talib.WMA(df['close'], timeperiod=20)

# === 3. Adicionar feature volume ===
df['volume'] = df['volume']

# === 4. Criar target: 1 se subir em 3 candles, senão 0 ===
df['target'] = (df['close'].shift(-3) > df['close']).astype(int)

# === 5. Remover NaNs ===
df = df.dropna()

# === 6. Features e targets ===
features = ['rsi', 'atr', 'wma', 'volume']
X = df[features]
y = df['target']

# === 7. Separar treino/teste ===
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, shuffle=False)

# === 8. Função de optimização ===
def objective(trial):
    params = {
        "n_estimators": trial.suggest_int("n_estimators", 50, 300),
        "max_depth": trial.suggest_int("max_depth", 3, 20),
        "min_samples_split": trial.suggest_int("min_samples_split", 2, 10),
        "min_samples_leaf": trial.suggest_int("min_samples_leaf", 1, 10),
    }
    model = RandomForestClassifier(**params, random_state=42)
    model.fit(X_train, y_train)
    preds = model.predict(X_test)
    return accuracy_score(y_test, preds)

# === 9. Executar Optuna ===
study = optuna.create_study(direction="maximize")
study.optimize(objective, n_trials=50)

# === 10. Treinar modelo final ===
best_params = study.best_params
model = RandomForestClassifier(**best_params, random_state=42)
model.fit(X_train, y_train)
y_pred = model.predict(X_test)

# === 11. Avaliação ===
print("\nMelhores parâmetros encontrados:")
for k, v in best_params.items():
    print(f" - {k}: {v}")
print(f"Accuracy: {accuracy_score(y_test, y_pred):.4f}")
print("\nClassification Report:")
print(classification_report(y_test, y_pred, target_names=["Desce", "Sobe"]))

# Confusion Matrix
conf = confusion_matrix(y_test, y_pred)
plt.figure(figsize=(5, 4))
sns.heatmap(conf, annot=True, fmt="d", cmap="Blues",
            xticklabels=["Prev: Desce", "Prev: Sobe"],
            yticklabels=["Real: Desce", "Real: Sobe"])
plt.title("Confusion Matrix")
plt.tight_layout()
plt.show()

# === 12. Backtest simples ===
test_df = df.iloc[y_test.index].copy()
test_df['pred'] = y_pred
test_df['future_close'] = test_df['close'].shift(-3)
test_df['ret'] = 0.0

# Compra se pred = 1, vende em close +3h
test_df.loc[test_df['pred'] == 1, 'ret'] = (
    test_df['future_close'] - test_df['close']) / test_df['close']

test_df['cumulative_return'] = (1 + test_df['ret']).cumprod()

# Resultados
print("\n=== Backtest Simples ===")
print(f"Total de sinais de compra: {test_df['pred'].sum()}")
print(f"Retorno médio por trade: {test_df.loc[test_df['pred'] == 1, 'ret'].mean():.5f}")
print(f"Capital final: {test_df['cumulative_return'].iloc[-1]:.2f}x")

# Gráfico
plt.figure(figsize=(10, 4))
plt.plot(test_df['cumulative_return'].values, label="Capital")
plt.title("Backtest com sinais do modelo (Optuna + volume)")
plt.xlabel("Execuções de trade")
plt.ylabel("Capital acumulado (x)")
plt.grid()
plt.legend()
plt.tight_layout()
plt.show()