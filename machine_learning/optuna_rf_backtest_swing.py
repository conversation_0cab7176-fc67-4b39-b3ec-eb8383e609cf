# === IMPORTAÇÕES ===
import pandas as pd
import talib
import optuna
from tqdm import tqdm
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import warnings

# === CONFIG GLOBAL ===
INITIAL_CAPITAL = 1000
optuna.logging.set_verbosity(optuna.logging.WARNING)
warnings.filterwarnings("ignore")
tqdm.pandas()

# === 1. CARREGAR DADOS ===
df_raw = pd.read_csv("data/data_BTC_USDC_1h.csv")

# === 2. FUNÇÃO DOS INDICADORES ===
def df_indicators(df_base, rsi_period, rsi_sWMA_PERIOD, WMA_PERIDOD):
    df = df_base.copy()
    df['rsi'] = talib.RSI(df['close'], timeperiod=rsi_period)
    df['rsi_sma'] = talib.SMA(df['rsi'], timeperiod=rsi_sWMA_PERIOD)
    df['wma'] = talib.WMA(df['close'], timeperiod=WMA_PERIDOD)
    df['target'] = (df['close'].shift(-3) > df['close']).astype(int)
    df.dropna(inplace=True)
    df.reset_index(drop=True, inplace=True)
    return df

# === 3. OTIMIZAÇÃO COM OPTUNA ===
def objective(trial):
    # Parâmetros dos indicadores
    rsi_period = trial.suggest_int("rsi_period", 5, 30)
    rsi_sWMA_PERIOD = trial.suggest_int("rsi_sWMA_PERIOD", 5, 40)
    WMA_PERIDOD = trial.suggest_int("WMA_PERIDOD", 5, 40)

    # Parâmetros do RandomForest
    params = {
        "n_estimators": trial.suggest_int("n_estimators", 50, 300),
        "max_depth": trial.suggest_int("max_depth", 3, 20),
        "min_samples_split": trial.suggest_int("min_samples_split", 2, 10),
        "min_samples_leaf": trial.suggest_int("min_samples_leaf", 1, 10),
    }

    df_prepared = df_indicators(df_raw, rsi_period, rsi_sWMA_PERIOD, WMA_PERIDOD)
    X = df_prepared[['rsi', 'rsi_sma', 'wma']]
    y = df_prepared['target']
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, shuffle=False)

    model = RandomForestClassifier(**params, random_state=42)
    model.fit(X_train, y_train)
    preds = model.predict(X_test)
    return accuracy_score(y_test, preds)

study = optuna.create_study(direction="maximize")
for _ in tqdm(range(50), desc="Optuna Trials"):
    study.optimize(objective, n_trials=1, catch=(Exception,))

best_params = study.best_params

# === 4. MODELO FINAL ===
df = df_indicators(df_raw, best_params['rsi_period'], best_params['rsi_sWMA_PERIOD'], best_params['WMA_PERIDOD'])
X = df[['rsi', 'rsi_sma', 'wma']]
y = df['target']
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, shuffle=False)

model = RandomForestClassifier(
    n_estimators=best_params['n_estimators'],
    max_depth=best_params['max_depth'],
    min_samples_split=best_params['min_samples_split'],
    min_samples_leaf=best_params['min_samples_leaf'],
    random_state=42
)
model.fit(X_train, y_train)
y_pred = model.predict(X_test)

# === 5. AVALIAÇÃO ===
accuracy = accuracy_score(y_test, y_pred)
print("\n=== Resultados ===")
print("Melhores parâmetros encontrados:")
for k, v in best_params.items():
    print(f" - {k}: {v}")
print(f"Accuracy: {accuracy * 100:.2f}%")

print("\nClassification Report:")
print(classification_report(y_test, y_pred, target_names=["Desce", "Sobe"]))

conf = confusion_matrix(y_test, y_pred)
plt.figure(figsize=(5, 4))
sns.heatmap(conf, annot=True, fmt="d", cmap="Blues",
            xticklabels=["Prev: Desce", "Prev: Sobe"],
            yticklabels=["Real: Desce", "Real: Sobe"])
plt.title("Confusion Matrix")
plt.tight_layout()
plt.show()

print("\nExplicação da matriz de confusão:")
print("- Verdadeiros negativos (canto superior esquerdo): modelo previu queda e realmente caiu.")
print("- Falsos positivos (superior direito): modelo previu alta, mas caiu.")
print("- Falsos negativos (inferior esquerdo): modelo previu queda, mas subiu.")
print("- Verdadeiros positivos (inferior direito): modelo previu alta e realmente subiu.")

# === 6. BACKTEST SIMPLES ===
test_df = df.loc[y_test.index].copy()
test_df['pred'] = y_pred
test_df['future_close'] = test_df['close'].shift(-3)
test_df['ret'] = 0.0
test_df.loc[test_df['pred'] == 1, 'ret'] = (
    test_df['future_close'] - test_df['close']) / test_df['close']

test_df['capital'] = INITIAL_CAPITAL * (1 + test_df['ret']).cumprod()

# === 7. MÉTRICAS BACKTEST ===
print("\n=== Backtest Simples ===")
total_trades = test_df['pred'].sum()
avg_return = test_df.loc[test_df['pred'] == 1, 'ret'].mean()
final_capital = test_df['capital'].iloc[-1]

print(f"Total de sinais de compra: {total_trades}")
print(f"Retorno médio por trade: {avg_return:.5f} ({avg_return*100:.2f}%)")
print(f"Capital final: {final_capital:.2f} USDC (de {INITIAL_CAPITAL} USDC)")
print(f"Multiplicador de capital: {final_capital / INITIAL_CAPITAL:.2f}x")

# === 8. GRÁFICOS ===
plt.figure(figsize=(10, 4))
plt.plot(test_df['capital'].values, label="Capital")
plt.title("Evolução do capital com sinais do modelo")
plt.xlabel("Trades")
plt.ylabel("Capital (USDC)")
plt.grid()
plt.legend()
plt.tight_layout()
plt.show()

plt.figure(figsize=(10, 4))
plt.plot(test_df['ret'].values, label="Retorno")
plt.title("Evolução do retorno com sinais do modelo")
plt.xlabel("Trades")
plt.ylabel("Retorno")
plt.grid()
plt.legend()
plt.tight_layout()
plt.show()

plt.figure(figsize=(10, 4))
plt.plot(test_df['capital'].pct_change().cumsum().values, label="Retorno acumulado")
plt.title("Evolução do retorno acumulado")
plt.xlabel("Trades")
plt.ylabel("Retorno acumulado")
plt.grid()
plt.legend()
plt.tight_layout()
plt.show()

plt.figure(figsize=(10, 4))
plt.plot(np.log(test_df['capital']).pct_change().cumsum().values, label="Retorno acumulado")
plt.title("Evolução do retorno acumulado (logarithmico)")
plt.xlabel("Trades")
plt.ylabel("Retorno acumulado")
plt.grid()
plt.legend()
plt.tight_layout()
plt.show()

plt.figure(figsize=(10, 4))
plt.plot(test_df['capital'].pct_change().cumsum().values, label="Retorno acumulado")
plt.title("Evolução do retorno acumulado (sem log)")
plt.xlabel("Trades")
plt.ylabel("Retorno acumulado")
plt.grid()
plt.legend()
plt.tight_layout()
plt.show()

# === 9. CONCLUSÃO ===
print("\n=== Conclusão dos resultados ===")

if accuracy < 0.55:
    print("- O modelo apresenta uma precisão de apenas {:.2f}%, o que indica uma fraca capacidade de prever corretamente os movimentos do mercado.".format(accuracy * 100))
else:
    print("- O modelo apresenta uma precisão aceitável de {:.2f}%, indicando alguma utilidade para prever movimentos de curto prazo.".format(accuracy * 100))

if avg_return > 0:
    print(f"- O retorno médio por trade é positivo ({avg_return*100:.2f}%), sugerindo que os sinais de compra têm potencial lucrativo.")
else:
    print(f"- O retorno médio por trade é negativo ({avg_return*100:.2f}%), sugerindo que os sinais atuais geram mais prejuízo que lucro.")

if final_capital > INITIAL_CAPITAL:
    print(f"- O capital final ({final_capital:.2f} USDC) superou o capital inicial ({INITIAL_CAPITAL} USDC), o que é um sinal positivo.")
else:
    print(f"- O capital final ({final_capital:.2f} USDC) está abaixo do capital inicial ({INITIAL_CAPITAL} USDC), indicando que o sistema não foi rentável.")

print("\n=== Próximos passos recomendados ===")
if accuracy < 0.55 or avg_return <= 0 or final_capital <= INITIAL_CAPITAL:
    print("- Melhorar os indicadores técnicos usados ou combinar com outros (ex: volume, volatilidade, tendência).")
    print("- Ajustar o horizonte de previsão (ex: prever variação em 6 ou 12 candles, não 3).")
    print("- Testar outros algoritmos de classificação (ex: XGBoost, SVM, redes neurais).")
    print("- Adicionar filtros de contexto (ex: evitar operar em lateralizações ou horários de baixa liquidez).")
else:
    print("- O sistema apresenta potencial. Considerar:")
    print("  - Incluir stop loss e take profit para gestão de risco.")
    print("  - Executar testes com dados de outros pares ou timeframes.")
    print("  - Automatizar testes forward (walk-forward validation).")
