# Refatorado para melhorar detecção de compra e prever melhor saída
import ccxt
import pandas as pd
import numpy as np
import talib
import requests
import time
import os
from datetime import datetime
from tabulate import tabulate
from dotenv import load_dotenv

# --- Load credenciais do Telegram ---
load_dotenv()
TELEGRAM_TOKEN = os.getenv("TELEGRAM_TOKEN")
TELEGRAM_CHAT_ID = os.getenv("TELEGRAM_CHAT_ID")

# --- Configurações ---
SYMBOLS = ['BTC/USDC', 'ETH/USDC', 'SOL/USDC', "ETH/BTC"]
TIMEFRAMES = ['5m', '15m','1h', '4h']
LIMIT = 200
exchange = ccxt.myokx()
last_signals = {}

# --- Telegram ---
def send_telegram_message(message):
    url = f"https://api.telegram.org/bot{TELEGRAM_TOKEN}/sendMessage"
    payload = {"chat_id": TELEGRAM_CHAT_ID, "text": message}
    try:
        requests.post(url, data=payload)
    except Exception as e:
        print(f"Erro ao enviar mensagem Telegram: {e}")

# --- Dados OHLCV ---
def fetch_ohlcv(symbol, timeframe):
    ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=LIMIT)
    df = pd.DataFrame(ohlcv, columns=['timestamp','open','high','low','close','volume'])
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
    return df

# --- Cálculo de indicadores ---
def calculate_indicators(df, rsi_period=14, WMA_PERIDOD=21):
    df['RSI'] = talib.RSI(df['close'], timeperiod=rsi_period)
    df['WMA'] = talib.WMA(df['close'], timeperiod=WMA_PERIDOD)
    df['ATR'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=14)
    return df

# --- Lógica de sinal de compra ---
def detect_buy_signal(df):
    latest = df.iloc[-1]
    previous = df.iloc[-2]

    is_rsi_oversold = latest['RSI'] < 30
    wma_cross_up = previous['close'] < previous['WMA'] and latest['close'] > latest['WMA']

    return is_rsi_oversold and wma_cross_up

# --- Previsão de saída (TP e SL) ---
def estimate_exit(df):
    atr = df.iloc[-1]['ATR']
    close = df.iloc[-1]['close']
    take_profit = round(close + 2 * atr, 2)
    stop_loss = round(close - 1.5 * atr, 2)
    return take_profit, stop_loss

# --- Execução principal ---
def run_bot():
    results_by_timeframe = {tf: [] for tf in TIMEFRAMES}

    while True:
        for symbol in SYMBOLS:
            for timeframe in TIMEFRAMES:
                try:
                    df = fetch_ohlcv(symbol, timeframe)
                    df = calculate_indicators(df)

                    if detect_buy_signal(df):
                        timestamp = df.iloc[-1]['timestamp']
                        take_profit, stop_loss = estimate_exit(df)
                        signal_id = f"{symbol}_{timeframe}_{timestamp}"

                        if last_signals.get(signal_id):
                            continue

                        last_signals[signal_id] = True

                        results_by_timeframe[timeframe].append([
                            symbol, "RSI+WMA", "BUY", timestamp,
                            round(df.iloc[-1]['RSI'], 2),
                            round(df.iloc[-1]['WMA'], 2)
                        ])

                        msg = (
                            f"\u2b06\ufe0f Sinal de COMPRA detectado\n"
                            f"\ud83d\udd39 {symbol} | {timeframe}\n"
                            f"\u23f0 {timestamp}\n"
                            f"RSI: {df.iloc[-1]['RSI']:.2f} | WMA: {df.iloc[-1]['WMA']:.2f}\n"
                            f"TP: {take_profit} | SL: {stop_loss}"
                        )
                        send_telegram_message(msg)

                except Exception as e:
                    print(f"Erro ao processar {symbol} {timeframe}: {e}")

        for timeframe, rows in results_by_timeframe.items():
            print(f"\n===== Signals SkLearn - Timeframe: {timeframe} =====")
            headers = ['Symbol', 'Strategy', 'Signal', 'Timestamp', 'RSI', 'WMA']
            print(tabulate(rows, headers=headers, tablefmt='grid'))

        sleep_minutes = {
            '1h': 60,
            '4h': 240,
            '15m': 15,
            '5m': 5
        }[TIMEFRAMES[0]]
        print(f"\nA aguardar {sleep_minutes} minutos...")
        time.sleep(sleep_minutes * 60)

if __name__ == "__main__":
    run_bot()
