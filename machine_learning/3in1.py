import pandas as pd
import numpy as np
import talib
import optuna
import requests
import time
import threading
import logging
import os
import json
from tqdm import tqdm
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import (
    accuracy_score,
    classification_report,
    confusion_matrix,
    roc_auc_score,
    roc_curve,
    precision_recall_curve,
    average_precision_score,
)
import matplotlib.pyplot as plt
import seaborn as sns
import warnings

# === CONFIGURAÇÕES GLOBAIS ===
warnings.filterwarnings("ignore")
logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")
optuna.logging.set_verbosity(optuna.logging.WARNING)

TELEGRAM_BOT_TOKEN = '**********************************************'
TELEGRAM_CHAT_ID = '21748554'
INITIAL_CAPITAL = 1000
SYMBOL = 'BTC/USDC'
TIMEFRAME = '1h'
DATA_FILE = "data/data_BTC_USDC_1h.csv"
N_TRIALS = 100
FORWARD_PERIOD = 3
CHECK_INTERVAL_SEC = 3600

# === FUNÇÕES AUXILIARES ===
def send_telegram_message(message):
    """Envia mensagem para o Telegram"""
    url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"
    payload = {"chat_id": TELEGRAM_CHAT_ID, "text": message}
    try:
        requests.post(url, data=payload, timeout=10)
    except Exception as e:
        logging.error(f"Erro Telegram: {e}")

def calculate_indicators(df, params):
    """Calcula todos os indicadores técnicos com parâmetros otimizados"""
    df = df.copy()
    
    # Indicadores principais
    df['rsi'] = talib.RSI(df['close'], timeperiod=params['rsi_period'])
    df['rsi_sma'] = talib.SMA(df['rsi'], timeperiod=params['rsi_sWMA_PERIOD'])
    df['wma'] = talib.WMA(df['close'], timeperiod=params['WMA_PERIDOD'])
    
    # Volume e VWAP
    df['cum_vol'] = df['volume'].cumsum()
    df['cum_vol_price'] = (df['close'] * df['volume']).cumsum()
    df['vwap'] = df['cum_vol_price'] / df['cum_vol']
    df['volume_ma'] = df['volume'].rolling(window=params['volume_ma_window']).mean()
    
    # ATR para stop loss dinâmico
    df['atr'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=params['atr_period'])
    
    # Criação do target
    df['future_close'] = df['close'].shift(-FORWARD_PERIOD)
    df['target'] = (df['future_close'] > df['close']).astype(int)
    
    df.dropna(inplace=True)
    return df

def dynamic_sl_tp(close_price, atr_value, sl_multiplier=1.5, tp_multiplier=2.0):
    """Calcula stop loss e take profit dinâmicos baseados no ATR"""
    sl_price = close_price - (atr_value * sl_multiplier)
    tp_price = close_price + (atr_value * tp_multiplier)
    return sl_price, tp_price

def calculate_drawdown(capital_series):
    """Calcula drawdown máximo e duração"""
    wealth_index = capital_series
    previous_peaks = wealth_index.cummax()
    drawdown = (wealth_index - previous_peaks) / previous_peaks
    
    max_drawdown = drawdown.min()
    end_date = drawdown.idxmin()
    start_date = wealth_index[:end_date].idxmax()
    logging.info(f"start_date type: {type(start_date)}, end_date type: {type(end_date)}")
    drawdown_duration = (end_date - start_date).days if not pd.isnull(end_date) else 0
    
    return drawdown, max_drawdown, drawdown_duration

# === FUNÇÃO OBJETIVO DO OPTUNA ===
def objective(trial):
    """Função objetivo para otimização com Optuna"""
    # Parâmetros dos indicadores
    params = {
        'rsi_period': trial.suggest_int("rsi_period", 5, 30),
        'rsi_sWMA_PERIOD': trial.suggest_int("rsi_sWMA_PERIOD", 5, 40),
        'WMA_PERIDOD': trial.suggest_int("WMA_PERIDOD", 5, 40),
        'volume_ma_window': trial.suggest_int("volume_ma_window", 5, 50),
        'atr_period': trial.suggest_int("atr_period", 7, 30),
        'sl_multiplier': trial.suggest_float("sl_multiplier", 1.0, 3.0),
        'tp_multiplier': trial.suggest_float("tp_multiplier", 1.5, 4.0),
    }
    
    # Hiperparâmetros do modelo
    model_params = {
        "n_estimators": trial.suggest_int("n_estimators", 50, 300),
        "max_depth": trial.suggest_int("max_depth", 3, 20),
        "min_samples_split": trial.suggest_int("min_samples_split", 2, 10),
        "min_samples_leaf": trial.suggest_int("min_samples_leaf", 1, 10),
    }
    
    # Preparar dados
    df_prepared = calculate_indicators(df_raw, params)
    features = ['rsi', 'rsi_sma', 'wma', 'vwap', 'volume', 'volume_ma']
    X = df_prepared[features]
    y = df_prepared['target']
    
    # Divisão treino/teste temporal
    split_idx = int(len(X) * 0.8)
    X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
    y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
    
    # Treinar e avaliar modelo
    model = RandomForestClassifier(**model_params, random_state=42)
    model.fit(X_train, y_train)
    preds = model.predict(X_test)
    return accuracy_score(y_test, preds)

# === MONITORAMENTO CONTÍNUO ===
def monitor_market(model, best_params):
    """Monitora o mercado continuamente e envia sinais"""
    last_signal = None
    logging.info(f"[{SYMBOL}] Iniciando monitoramento...")
    
    while True:
        try:
            # Buscar dados mais recentes (simulado)
            df_latest = pd.read_csv(DATA_FILE).iloc[-100:]
            
            # Calcular indicadores
            df_processed = calculate_indicators(df_latest, best_params)
            if df_processed.empty:
                time.sleep(CHECK_INTERVAL_SEC)
                continue
            
            # Gerar previsão
            features = ['rsi', 'rsi_sma', 'wma', 'vwap', 'volume', 'volume_ma']
            X = df_processed[features].iloc[-1:].values
            signal = model.predict(X)[0]
            
            # Obter últimos valores
            current_close = df_processed['close'].iloc[-1]
            atr_value = df_processed['atr'].iloc[-1]
            
            # Calcular SL e TP dinâmicos
            sl_price, tp_price = dynamic_sl_tp(
                current_close, atr_value,
                best_params['sl_multiplier'],
                best_params['tp_multiplier']
            )
            
            # Enviar sinal se mudou
            if signal != last_signal:
                action = "COMPRAR" if signal == 1 else "AGUARDAR"
                msg = (f"[{SYMBOL}] SINAL: {action}\n"
                      f"Preço: {current_close:.2f}\n"
                      f"SL: {sl_price:.2f}\n"
                      f"TP: {tp_price:.2f}")
                send_telegram_message(msg)
                last_signal = signal
                
        except Exception as e:
            logging.error(f"Erro no monitoramento: {e}")
        
        time.sleep(CHECK_INTERVAL_SEC)

# === BACKTEST AVANÇADO ===
def enhanced_backtest(test_df, model, best_params):
    """Executa backtest com saídas refinadas e SL/TP dinâmicos"""
    test_df = test_df.copy()
    test_df['pred'] = model.predict(test_df[features])
    test_df['future_close'] = test_df['close'].shift(-FORWARD_PERIOD)
    test_df['position'] = 0
    test_df['capital'] = INITIAL_CAPITAL
    test_df['atr'] = talib.ATR(test_df['high'], test_df['low'], test_df['close'], best_params['atr_period'])
    
    in_position = False
    entry_price = 0
    entry_index = None
    
    for idx, row in tqdm(test_df.iterrows(), desc="Backtesting"):
 
        # Entrada apenas em long positions
        if not in_position and row['pred'] == 1:
            in_position = True
            entry_price = row['close']
            logging.info(f"entry_price type: {type(entry_price)}, row['close'] type: {type(row['close'])}")
            entry_index = idx
            test_df.at[idx, 'position'] = 1

            # Calcular SL e TP dinâmico
            sl_price, tp_price = dynamic_sl_tp(
                entry_price, row['atr'],
                best_params['sl_multiplier'],
                best_params['tp_multiplier']
            )

        # Verificar saída por SL/TP ou tempo
        elif in_position and entry_index is not None:
            current_price = row['close']

            # Saída por take profit
            if current_price >= tp_price:
                exit_reason = "TP"
                exit_price = tp_price

            # Saída por stop loss
            elif current_price <= sl_price:
                exit_reason = "SL"
                exit_price = sl_price

            # Saída por tempo (final do período)
            else:
                if isinstance(idx, pd.Timestamp) and isinstance(entry_index, pd.Timestamp):
                    if (idx - entry_index) >= pd.Timedelta(hours=FORWARD_PERIOD):
                        exit_reason = "Tempo"
                        exit_price = row['close']
                    else:
                        continue
                elif isinstance(idx, (int, np.integer)) and isinstance(entry_index, (int, np.integer)):
                    if (idx - entry_index) >= FORWARD_PERIOD:
                        exit_reason = "Tempo"
                        exit_price = row['close']
                    else:
                        continue
                else:
                    continue

            # Calcular retorno
            ret = (exit_price - entry_price) / entry_price
            prev_pos = test_df.index.get_loc(idx) - 1
            if prev_pos >= 0:
                prev_capital = test_df.iloc[prev_pos]['capital']
            else:
                prev_capital = INITIAL_CAPITAL
            logging.info(f"ret type: {type(ret)}, prev_capital type: {type(prev_capital)}")
            test_df.at[idx, 'capital'] = prev_capital * (1 + ret)
            test_df.at[idx, 'position'] = 0
            in_position = False
    return test_df

# === FUNÇÃO PRINCIPAL ===
def main():
    global df_raw, features
    
    # Carregar dados
    df_raw = pd.read_csv(DATA_FILE, parse_dates=['timestamp']).set_index('timestamp')
    
    # Otimização com Optuna
    study = optuna.create_study(direction="maximize")
    study.optimize(objective, n_trials=N_TRIALS)
    best_params = study.best_params
    print("\n=== Melhores parâmetros ===")
    for k, v in best_params.items():
        print(f"{k}: {v}")
    
    # Preparar dados com melhores parâmetros
    df_processed = calculate_indicators(df_raw, best_params)
    features = ['rsi', 'rsi_sma', 'wma', 'vwap', 'volume', 'volume_ma']
    X = df_processed[features]
    y = df_processed['target']
    
    # Divisão treino/teste
    split_idx = int(len(X) * 0.8)
    X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
    y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
    
    # Treinar modelo final
    model_params = {k: best_params[k] for k in ['n_estimators', 'max_depth', 'min_samples_split', 'min_samples_leaf']}
    model = RandomForestClassifier(**model_params, random_state=42)
    model.fit(X_train, y_train)
    
    # Avaliação do modelo
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    print(f"\nAccuracy: {accuracy*100:.2f}%")
    print(classification_report(y_test, y_pred))
    
    # Backtest avançado
    test_df = df_processed.iloc[split_idx:].copy()
    backtest_results = enhanced_backtest(test_df, model, best_params)
    
    # Análise de risco
    returns = backtest_results['capital'].pct_change().dropna()
    drawdown_series, max_drawdown, drawdown_duration = calculate_drawdown(backtest_results['capital'])
    volatility = returns.std() * np.sqrt(252)  # Volatilidade anualizada
    
    print("\n=== Análise de Risco ===")
    print(f"Drawdown Máximo: {max_drawdown*100:.2f}%")
    print(f"Duração do Drawdown: {drawdown_duration} dias")
    print(f"Volatilidade Anualizada: {volatility*100:.2f}%")
    
    # Iniciar monitoramento em thread separada
    monitor_thread = threading.Thread(target=monitor_market, args=(model, best_params))
    monitor_thread.daemon = True
    monitor_thread.start()
    logging.info("Monitoramento contínuo iniciado em segundo plano...")
    
    # Manter o script em execução
    try:
        while True:
            time.sleep(3600)
    except KeyboardInterrupt:
        logging.info("Encerrando monitoramento...")

if __name__ == "__main__":
    main()

