import ccxt
import pandas as pd
import numpy as np
import optuna
import talib
import requests
import time
import os
from datetime import datetime
from tabulate import tabulate
from dotenv import load_dotenv

# --- Load credenciais do Telegram ---
load_dotenv()
TELEGRAM_TOKEN = os.getenv("TELEGRAM_TOKEN")
TELEGRAM_CHAT_ID = os.getenv("TELEGRAM_CHAT_ID")

# --- Configurações ---
SYMBOLS = ['BTC/USDC', 'ETH/USDC', 'SOL/USDC', "ETH/BTC"]
TIMEFRAMES = ['1h', '4h']
LIMIT = 200
exchange = ccxt.myokx()
last_signals = {}  # memoriza sinais anteriores

# --- Função enviar mensagem Telegram ---
def send_telegram_message(message):
    url = f"https://api.telegram.org/bot{TELEGRAM_TOKEN}/sendMessage"
    payload = {"chat_id": TELEGRAM_CHAT_ID, "text": message}
    try:
        requests.post(url, data=payload)
    except Exception as e:
        print(f"Erro ao enviar mensagem Telegram: {e}")

# --- Obter dados históricos ---
def fetch_ohlcv(symbol, timeframe):
    ohlcv = exchange.fetch_ohlcv(symbol, timeframe=timeframe, limit=LIMIT)
    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
    return df

# --- Calcular indicadores ---
def add_indicators(df, rsi_period=14, WMA_PERIDOD=20):
    close = df['close'].values
    high = df['high'].values
    low = df['low'].values
    volume = df['volume'].values

    df['rsi'] = talib.RSI(close, timeperiod=rsi_period)
    df['wma'] = talib.WMA(close, timeperiod=WMA_PERIDOD)

    tp = (high + low + close) / 3
    df['vwap'] = np.cumsum(tp * volume) / np.cumsum(volume)

    df.dropna(inplace=True)
    return df

# --- Estratégias ---
def strategy_1(row):
    if row['close'] > row['vwap'] and row['rsi'] < 30:
        return 'buy'
    elif row['rsi'] > 70 or row['close'] < row['wma']:
        return 'sell'
    return 'hold'

def strategy_2(row):
    if row['rsi'] > 50 and row['close'] > row['wma']:
        return 'buy'
    elif row['rsi'] < 50:
        return 'sell'
    return 'hold'

def strategy_3(row):
    if row['close'] > row['vwap']:
        return 'buy'
    elif row['close'] < row['vwap']:
        return 'sell'
    return 'hold'

strategies = {
    "Strategy 1": strategy_1,
    "Strategy 2": strategy_2,
    "Strategy 3": strategy_3
}

# --- Optuna: otimizar parâmetros ---
from sklearn.model_selection import GridSearchCV
from sklearn.metrics import make_scorer

# Função de scoring personalizada
def signal_score(y_true, y_pred):
    score = list(y_pred).count('buy') - list(y_pred).count('sell')
    return score

def sklearn_optimize(df):
    params_grid = {
        'rsi_period': list(range(8, 22, 2)),
        'WMA_PERIDOD': list(range(10, 40, 5))
    }

    results = []
    for rsi_p in params_grid['rsi_period']:
        for wma_p in params_grid['WMA_PERIDOD']:
            test_df = add_indicators(df.copy(), rsi_period=rsi_p, WMA_PERIDOD=wma_p)
            if len(test_df) == 0:
                continue
            test_df['signal'] = test_df.apply(strategy_1, axis=1)
            buy_score = test_df['signal'].value_counts().get('buy', 0)
            sell_score = test_df['signal'].value_counts().get('sell', 0)
            score = buy_score - sell_score
            results.append((score, {'rsi_period': rsi_p, 'WMA_PERIDOD': wma_p}))

    best = sorted(results, key=lambda x: x[0], reverse=True)[0]
    return best[1]  # retorna apenas os melhores parâmetros

# --- Loop principal ---
def run_bot():
    while True:
        now = datetime.utcnow()
        results_by_timeframe = {tf: [] for tf in TIMEFRAMES}

        for symbol in SYMBOLS:
            for timeframe in TIMEFRAMES:
                try:
                    df = fetch_ohlcv(symbol, timeframe)
                    best_params = sklearn_optimize(df)


                    df = add_indicators(df, **best_params)
                    latest = df.iloc[-1]

                    for name, strategy in strategies.items():
                        signal = strategy(latest)
                        key = f"{symbol}_{timeframe}_{name}"
                        timestamp = latest['timestamp'].strftime('%Y-%m-%d %H:%M')

                        results_by_timeframe[timeframe].append([
                            symbol, name, signal, timestamp,
                            best_params['rsi_period'], best_params['WMA_PERIDOD'],
                        ])

                        # Comparar com último sinal
                        previous = last_signals.get(key)
                        if previous != signal:
                            last_signals[key] = signal
                            msg = (
                                f"🔔 SINAL ALTERADO\n"
                                f"⏱ Timeframe: {timeframe}\n"
                                f"📈 Symbol: {symbol}\n"
                                f"🧠 Strategy: {name}\n"
                                f"📊 Novo Sinal: {signal.upper()}\n"
                                f"📅 {timestamp}\n"
                                f"⚙️ Params: RSI={best_params['rsi_period']} WMA={best_params['WMA_PERIDOD']} "
                            )
                            send_telegram_message(msg)

                except Exception as e:
                    print(f"Erro ao processar {symbol} {timeframe}: {e}")

        # Imprimir tabelas
        for timeframe, rows in results_by_timeframe.items():
            print(f"\n===== Timeframe: {timeframe} =====")
            headers = ['Symbol', 'Strategy', 'Signal', 'Timestamp', 'RSI', 'WMA']
            print(tabulate(rows, headers=headers, tablefmt='grid'))

        # Esperar o tempo correspondente ao menor timeframe
        sleep_minutes = 60 if '1h' in TIMEFRAMES else 240
        print(f"\nA aguardar {sleep_minutes} minutos até próxima análise...")
        time.sleep(sleep_minutes * 60)

# --- Execução ---
if __name__ == "__main__":
    run_bot()
