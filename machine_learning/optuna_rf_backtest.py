import pandas as pd
import numpy as np
import talib
import ccxt
import joblib
import time
import json
import os
import requests
import logging
import optuna
from tqdm import tqdm
from datetime import datetime
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import threading  # IMPORTANTE: Adicionado no topo

optuna.logging.set_verbosity(optuna.logging.WARNING)

# === CONFIG ===

TELEGRAM_BOT_TOKEN='**********************************************'
TELEGRAM_CHAT_ID='21748554'
exchange = ccxt.binance()
SYMBOLS = ['BTC/USDC', 'ETH/USDC', 'SOL/USDC', 'XRP/USDC']
TIMEFRAME = '1h'
VOLUME_MA_WINDOW = 20

FORWARD = 3
CHECK_INTERVAL_SEC = 3600

BASE_DIR = 'machine_learning/backtests'
os.makedirs(BASE_DIR, exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.FileHandler("monitor.log"),
        logging.StreamHandler()
    ]
)

# === FUNÇÕES ===

def send_telegram_message(token: str, chat_id: str, message: str):
    url = f"https://api.telegram.org/bot{token}/sendMessage"
    payload = {"chat_id": chat_id, "text": message}
    try:
        requests.post(url, data=payload, timeout=10)
    except Exception as e:
        logging.error(f"Erro Telegram: {e}")

def fetch_latest_data(symbol, limit=1000):
    try:
        ohlcv = exchange.fetch_ohlcv(symbol, timeframe=TIMEFRAME, limit=limit)
        df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        return df
    except Exception as e:
        logging.error(f"[{symbol}] Erro ao buscar dados: {e}")
        return pd.DataFrame()

def calculate_indicators(df, rsi_period, WMA_PERIDOD):
    df['rsi'] = talib.RSI(df['close'], timeperiod=rsi_period)
    df['volume_ma'] = df['volume'].rolling(window=VOLUME_MA_WINDOW).mean()
    df['wma'] = talib.WMA(df['close'], timeperiod=WMA_PERIDOD)
    df.dropna(inplace=True)
    return df

def prepare_labels(df):
    df['future_close'] = df['close'].shift(-FORWARD)
    df['target'] = (df['future_close'] > df['close']).astype(int)
    df.dropna(inplace=True)
    return df

def train_model(df):
    df = prepare_labels(df)
    X = df[['rsi', 'wma', 'volume', 'volume_ma']]
    y = df['target']
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, shuffle=False)
    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X_train, y_train)
    preds = model.predict(X_test)
    acc = accuracy_score(y_test, preds)
    report = classification_report(y_test, preds, output_dict=True)
    return model, acc, report

def save_model_and_status(symbol, model, acc, report, best_params):
    symbol_id = symbol.replace('/', '_')
    model_file = f"{BASE_DIR}/{symbol_id}_model.pkl"
    status_file = f"{BASE_DIR}/{symbol_id}_status.json"

    joblib.dump(model, model_file)
    logging.info(f"[{symbol}] Modelo salvo em {model_file}")
    status = {
        "model_trained": True,
        "last_trained": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "accuracy": acc,
        "classification_report": report,
        "optimized_params": best_params
    }
    with open(status_file, 'w') as f:
        json.dump(status, f, indent=4)

def load_model(symbol):
    symbol_id = symbol.replace('/', '_')
    model_file = f"{BASE_DIR}/{symbol_id}_model.pkl"
    if not os.path.exists(model_file):
        logging.warning(f"[{symbol}] Modelo não encontrado")
        return None
    try:
        return joblib.load(model_file)
    except Exception as e:
        logging.error(f"[{symbol}] Erro ao carregar modelo: {e}")
        return None

def generate_signal(df, model):
    if df.empty or model is None:
        return None
    X = df[['rsi', 'wma', 'volume', 'volume_ma']]
    return model.predict(X)[-1]

# === OPTUNA ===

def objective(trial, symbol):
    rsi_period = trial.suggest_int("rsi_period", 5, 39)
    WMA_PERIDOD = trial.suggest_int("WMA_PERIDOD", 10, 50)
    df = fetch_latest_data(symbol)
    if df.empty:
        return 0.0
    df = calculate_indicators(df, rsi_period, WMA_PERIDOD)
    df = prepare_labels(df)
    if df.empty:
        return 0.0
    X = df[['rsi', 'wma', 'volume', 'volume_ma']]
    y = df['target']
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, shuffle=False)
    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X_train, y_train)
    preds = model.predict(X_test)
    return accuracy_score(y_test, preds)

def run_optuna(symbol, n_trials=30):
    study = optuna.create_study(direction="maximize")
    study.optimize(lambda trial: objective(trial, symbol), n_trials=n_trials)
    logging.info(f"[{symbol}] Melhores parâmetros: {study.best_params}")
    return study.best_params

# === MONITORAMENTO ===

def monitor_market(symbol, model, best_params):
    last_signal = None
    logging.info(f"[{symbol}] Iniciando monitoramento...")
    while True:
        try:
            df = fetch_latest_data(symbol)
            if df.empty:
                time.sleep(CHECK_INTERVAL_SEC)
                continue
            df = calculate_indicators(df, best_params['rsi_period'], best_params['WMA_PERIDOD'])
            signal = generate_signal(df, model)
            if signal is None:
                time.sleep(CHECK_INTERVAL_SEC)
                continue
            if signal != last_signal:
                msg = f"[{symbol}] SINAL NOVO: {'Comprar' if signal == 1 else 'Vender'}"
                send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, msg)
                logging.info(msg)
                last_signal = signal
            else:
                logging.info(f"[{symbol}] Sinal inalterado: {'Comprar' if signal == 1 else 'Vender'}")
        except Exception as e:
            logging.error(f"[{symbol}] Erro no monitoramento: {e}")
        time.sleep(CHECK_INTERVAL_SEC)

# === EXECUÇÃO ===

import threading  # IMPORTANTE: Adicionado no topo

if __name__ == "__main__":
    for symbol in tqdm(SYMBOLS, desc="Treinando modelos"):
        df = fetch_latest_data(symbol)
        if df.empty:
            logging.warning(f"[{symbol}] Sem dados")
            continue
        best_params = run_optuna(symbol, n_trials=30)
        df = calculate_indicators(df, best_params['rsi_period'], best_params['WMA_PERIDOD'])
        model, acc, report = train_model(df)
        save_model_and_status(symbol, model, acc, report, best_params)

    threads = []
    for symbol in SYMBOLS:
        model = load_model(symbol)
        symbol_id = symbol.replace('/', '_')
        status_file = f"{BASE_DIR}/{symbol_id}_status.json"
        if model is None or not os.path.exists(status_file):
            continue
        with open(status_file) as f:
            status = json.load(f)
        best_params = status["optimized_params"]

        # Inicia uma thread para cada símbolo
        t = threading.Thread(target=monitor_market, args=(symbol, model, best_params))
        t.start()
        threads.append(t)

    # Espera que todas as threads terminem (teoricamente nunca terminam)
    for t in threads:
        t.join()

