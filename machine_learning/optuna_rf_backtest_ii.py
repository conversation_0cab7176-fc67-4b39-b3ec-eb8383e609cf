import pandas as pd
import numpy as np
import talib
import optuna
from tqdm import tqdm
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import (
    accuracy_score,
    classification_report,
    confusion_matrix,
    roc_auc_score,
    roc_curve,
    precision_recall_curve,
    average_precision_score,
)
import matplotlib.pyplot as plt
import seaborn as sns
import warnings

warnings.filterwarnings("ignore")
optuna.logging.set_verbosity(optuna.logging.WARNING)
tqdm.pandas()

# === CONFIG ===
INITIAL_CAPITAL = 1000  # Capital inicial para simulação

# === 1. Carregar dados ===
df = pd.read_csv("data/data_BTC_USDC_1h.csv")

# === 2. Criar target: 1 se subir em 3 candles, senão 0 ===
df['target'] = (df['close'].shift(-3) > df['close']).astype(int)

# === 3. Calcular VWAP ===
# VWAP = sum(price * volume) / sum(volume) acumulado dentro da barra (exemplo simples usando cumulativos por dia)
# Aqui vamos usar VWAP acumulado por dia (ajuste conforme seu timeframe e dados)
df['cum_vol'] = df['volume'].cumsum()
df['cum_vol_price'] = (df['close'] * df['volume']).cumsum()
df['vwap'] = df['cum_vol_price'] / df['cum_vol']

# Remove colunas auxiliares depois para evitar confusão
df.drop(columns=['cum_vol', 'cum_vol_price'], inplace=True)

# === 4. Função de objetivo para Optuna ===
def objective(trial):
    rsi_period = trial.suggest_int("rsi_period", 7, 30)
    volume_ma_window = trial.suggest_int("volume_ma_window", 5, 50)

    # Indicadores
    df['rsi'] = talib.RSI(df['close'], timeperiod=rsi_period)
    df['volume_ma'] = df['volume'].rolling(window=volume_ma_window).mean()

    # Remove NaNs gerados por indicadores
    df_ = df.dropna()

    features = ['rsi', 'vwap', 'volume', 'volume_ma']
    X = df_[features]
    y = df_['target']

    # Train/test split fixo, sem embaralhamento para evitar vazamento temporal
    split_idx = int(len(df_) * 0.8)
    X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
    y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]

    params = {
        "n_estimators": trial.suggest_int("n_estimators", 50, 300),
        "max_depth": trial.suggest_int("max_depth", 3, 20),
        "min_samples_split": trial.suggest_int("min_samples_split", 2, 10),
        "min_samples_leaf": trial.suggest_int("min_samples_leaf", 1, 10),
    }

    model = RandomForestClassifier(**params, random_state=42)
    model.fit(X_train, y_train)
    preds = model.predict(X_test)
    return accuracy_score(y_test, preds)

# === 5. Rodar otimização Optuna ===
study = optuna.create_study(direction="maximize")
for _ in tqdm(range(50), desc="Optuna Trials"):
    study.optimize(objective, n_trials=1, catch=(Exception,))

best_params = study.best_params
print("\n=== Melhores parâmetros encontrados ===")
for k, v in best_params.items():
    print(f" - {k}: {v}")

# === 6. Recalcular indicadores no df original com parâmetros otimizados ===
rsi_period = best_params['rsi_period']
volume_ma_window = best_params['volume_ma_window']

df['rsi'] = talib.RSI(df['close'], timeperiod=rsi_period)
df['volume_ma'] = df['volume'].rolling(window=volume_ma_window).mean()
df.dropna(inplace=True)

features = ['rsi', 'vwap', 'volume', 'volume_ma']
X = df[features]
y = df['target']

# Train/test split fixo
split_idx = int(len(df) * 0.8)
X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]

# === 7. Treinar modelo final ===
rf_params = {k: best_params[k] for k in ['n_estimators', 'max_depth', 'min_samples_split', 'min_samples_leaf']}
model = RandomForestClassifier(**rf_params, random_state=42)
model.fit(X_train, y_train)
y_pred = model.predict(X_test)
y_pred_proba = model.predict_proba(X_test)[:, 1]

# === 8. Avaliação ===
accuracy = accuracy_score(y_test, y_pred)
print(f"\nAccuracy: {accuracy*100:.2f}%")

print("\nClassification Report:")
print(classification_report(y_test, y_pred, target_names=["Desce", "Sobe"]))

conf = confusion_matrix(y_test, y_pred)
plt.figure(figsize=(6, 5))
sns.heatmap(conf, annot=True, fmt="d", cmap="Blues",
            xticklabels=["Prev: Desce", "Prev: Sobe"],
            yticklabels=["Real: Desce", "Real: Sobe"])
plt.title("Matriz de Confusão")
plt.tight_layout()
plt.show()

# Métricas adicionais
roc_auc = roc_auc_score(y_test, y_pred_proba)
avg_precision = average_precision_score(y_test, y_pred_proba)
print(f"ROC AUC: {roc_auc:.4f}")
print(f"Average Precision (AP): {avg_precision:.4f}")

# Curva ROC
fpr, tpr, _ = roc_curve(y_test, y_pred_proba)
plt.figure(figsize=(6, 5))
plt.plot(fpr, tpr, label=f"ROC curve (AUC = {roc_auc:.3f})")
plt.plot([0,1], [0,1], 'k--')
plt.xlabel("False Positive Rate")
plt.ylabel("True Positive Rate")
plt.title("Curva ROC")
plt.legend()
plt.grid()
plt.tight_layout()
plt.show()

# Curva Precision-Recall
precision, recall, _ = precision_recall_curve(y_test, y_pred_proba)
plt.figure(figsize=(6, 5))
plt.plot(recall, precision, label=f"Precision-Recall (AP = {avg_precision:.3f})")
plt.xlabel("Recall")
plt.ylabel("Precision")
plt.title("Curva Precision-Recall")
plt.legend()
plt.grid()
plt.tight_layout()
plt.show()

# === 9. Backtest simples ===
test_df = df.iloc[split_idx:].copy()
test_df['pred'] = y_pred
test_df['future_close'] = test_df['close'].shift(-3)
test_df['ret'] = 0.0

# Aplicar retorno onde a previsão for de subida
test_df.loc[test_df['pred'] == 1, 'ret'] = (test_df['future_close'] - test_df['close']) / test_df['close']

# Crescimento do capital
test_df['capital'] = INITIAL_CAPITAL * (1 + test_df['ret']).cumprod()

# === 10. Métricas do backtest ===
print("\n=== Backtest Simples ===")
total_trades = test_df['pred'].sum()
avg_return = test_df.loc[test_df['pred'] == 1, 'ret'].mean()
final_capital = test_df['capital'].iloc[-1]

print(f"Total de sinais de compra: {total_trades}")
print(f"Retorno médio por trade: {avg_return:.5f} ({avg_return*100:.2f}%)")
print(f"Capital final: {final_capital:.2f} USDC (de {INITIAL_CAPITAL} USDC)")
print(f"Multiplicador de capital: {final_capital / INITIAL_CAPITAL:.2f}x")

# === 11. Gráficos de evolução do capital e retornos ===
plt.figure(figsize=(10, 4))
plt.plot(test_df['capital'].values, label="Capital")
plt.title("Evolução do capital com sinais do modelo")
plt.xlabel("Trades")
plt.ylabel("Capital (USDC)")
plt.grid()
plt.legend()
plt.tight_layout()
plt.show()

plt.figure(figsize=(10, 4))
plt.plot(test_df['ret'].values, label="Retorno")
plt.title("Evolução do retorno com sinais do modelo")
plt.xlabel("Trades")
plt.ylabel("Retorno")
plt.grid()
plt.legend()
plt.tight_layout()
plt.show()

plt.figure(figsize=(10, 4))
plt.plot(test_df['capital'].pct_change().cumsum().values, label="Retorno acumulado")
plt.title("Retorno acumulado com sinais do modelo")
plt.xlabel("Trades")
plt.ylabel("Retorno acumulado")
plt.grid()
plt.legend()
plt.tight_layout()
plt.show()

plt.figure(figsize=(10, 4))
plt.plot(np.log(test_df['capital']).pct_change().cumsum().values, label="Retorno acumulado (log)")
plt.title("Retorno acumulado com sinais do modelo (logarítmico)")
plt.xlabel("Trades")
plt.ylabel("Retorno acumulado")
plt.grid()
plt.legend()
plt.tight_layout()
plt.show()

# === 12. Análise final ===
print("\n=== Análise final ===")
if accuracy < 0.55:
    print(f"- Precisão baixa ({accuracy*100:.2f}%). Modelo não confiável para prever movimentos.")
else:
    print(f"- Precisão razoável ({accuracy*100:.2f}%), pode ter algum valor em previsão de curto prazo.")

if avg_return > 0:
    print(f"- Retorno médio positivo por trade: {avg_return*100:.2f}%. Indicativo de sinais lucrativos.")
else:
    print(f"- Retorno médio negativo por trade: {avg_return*100:.2f}%. Sinais podem causar prejuízo.")

if final_capital > INITIAL_CAPITAL:
    print(f"- Capital final ({final_capital:.2f} USDC) superou o inicial, sinal positivo.")
else:
    print(f"- Capital final ({final_capital:.2f} USDC) abaixo do inicial, indica não rentabilidade.")

print("\n=== Sugestões para melhorias ===")
if accuracy < 0.55 or avg_return <= 0 or final_capital <= INITIAL_CAPITAL:
    print("- Testar outros indicadores técnicos e combiná-los.")
    print("- Ajustar horizonte de previsão (ex: 6 ou 12 candles).")
    print("- Testar outros modelos (XGBoost, redes neurais).")
    print("- Implementar filtros para evitar trades em períodos de baixa liquidez.")
else:
    print("- O modelo mostra potencial. Pode-se melhorar com gestão de risco (stop loss/take profit).")
    print("- Testar em outros pares e timeframes.")
    print("- Automatizar testes walk-forward para validar robustez.")

# === 13. Explicação do shift para target ===
print("\n=== Explicação do shift ===")
print("O target é criado comparando o preço de fechamento atual com o preço de fechamento 3 candles à frente.")
print("O shift(-3) desloca a coluna 'close' para cima em 3 linhas, alinhando o preço futuro com a linha atual.")
print("Assim, 'target' = 1 indica que o preço subiu nos próximos 3 candles, 0 indica que não subiu.")
