import dash
from dash import dcc, html
from dash.dependencies import Input, Output, State
import subprocess
import os
import signal

# Inicialização do app Dash
app = dash.Dash(__name__)

# Lista de scripts com prefixo 777
scripts_777 = [f for f in os.listdir('.') if f.startswith('777_') and f.endswith('.py')]
script_status = {script: 'Parado' for script in scripts_777}
process_dict = {script: None for script in scripts_777}

# Parâmetros para cada bot (baseado na classe BotConfig de cada script)
bot_parameters = {
    '777_grid_base.py': [
        {'name': 'trading_symbols', 'type': 'text', 'label': 'Símbolos de Trading (separados por vírgula)', 'default': 'BTC-USDT,ETH-USDT'},
        {'name': 'rsi_period', 'type': 'number', 'label': 'Período RSI', 'default': 14},
        {'name': 'wma_period', 'type': 'number', 'label': 'Período WMA', 'default': 20},
        {'name': 'sl_multiplier', 'type': 'number', 'label': 'Multiplicador SL', 'default': 1.5},
        {'name': 'atr_multiplier', 'type': 'number', 'label': 'Multiplicador ATR', 'default': 2.0},
        {'name': 'atr_lookback', 'type': 'number', 'label': 'Lookback ATR', 'default': 14},
        {'name': 'position_size', 'type': 'number', 'label': 'Tamanho da Posição', 'default': 0.1},
        {'name': 'grid_size', 'type': 'number', 'label': 'Tamanho da Grade', 'default': 10},
        {'name': 'grid_step', 'type': 'number', 'label': 'Passo da Grade', 'default': 100},
    ],
    '777_oco_base.py': [
        {'name': 'trading_symbols', 'type': 'text', 'label': 'Símbolos de Trading (separados por vírgula)', 'default': 'ETH-USDT,SOL-USDT'},
        {'name': 'rsi_period', 'type': 'number', 'label': 'Período RSI', 'default': 14},
        {'name': 'wma_period', 'type': 'number', 'label': 'Período WMA', 'default': 20},
        {'name': 'sl_multiplier', 'type': 'number', 'label': 'Multiplicador SL', 'default': 1.5},
        {'name': 'atr_multiplier', 'type': 'number', 'label': 'Multiplicador ATR', 'default': 2.0},
        {'name': 'atr_lookback', 'type': 'number', 'label': 'Lookback ATR', 'default': 14},
        {'name': 'position_size', 'type': 'number', 'label': 'Tamanho da Posição', 'default': 0.1},
        {'name': 'target_profit', 'type': 'number', 'label': 'Lucro Alvo (%)', 'default': 2},
        {'name': 'stop_loss', 'type': 'number', 'label': 'Stop Loss (%)', 'default': 1},
    ],
    '777_trail_base.py': [
        {'name': 'trading_symbols', 'type': 'text', 'label': 'Símbolos de Trading (separados por vírgula)', 'default': 'SOL-USDT,ADA-USDT'},
        {'name': 'rsi_period', 'type': 'number', 'label': 'Período RSI', 'default': 14},
        {'name': 'wma_period', 'type': 'number', 'label': 'Período WMA', 'default': 20},
        {'name': 'sl_multiplier', 'type': 'number', 'label': 'Multiplicador SL', 'default': 1.5},
        {'name': 'atr_multiplier', 'type': 'number', 'label': 'Multiplicador ATR', 'default': 2.0},
        {'name': 'atr_lookback', 'type': 'number', 'label': 'Lookback ATR', 'default': 14},
        {'name': 'position_size', 'type': 'number', 'label': 'Tamanho da Posição', 'default': 0.1},
        {'name': 'trail_distance', 'type': 'number', 'label': 'Distância de Trailing (%)', 'default': 1.5},
    ],
    '777_fetch_data_okx_multi.py': [
        {'name': 'trading_symbols', 'type': 'text', 'label': 'Símbolos de Trading (separados por vírgula)', 'default': 'BTC-USDT,ETH-USDT'},
        {'name': 'timeframe', 'type': 'text', 'label': 'Timeframe', 'default': '1h'},
    ],
    '777_swing_strategy_analiser.py': [
        {'name': 'trading_symbols', 'type': 'text', 'label': 'Símbolos de Trading (separados por vírgula)', 'default': 'BTC-USDT,ETH-USDT'},
        {'name': 'rsi_period', 'type': 'number', 'label': 'Período RSI', 'default': 14},
        {'name': 'wma_period', 'type': 'number', 'label': 'Período WMA', 'default': 20},
        {'name': 'position_size', 'type': 'number', 'label': 'Tamanho da Posição', 'default': 0.1},
    ],
    '777_top_50_analyzer.py': [
        {'name': 'trading_symbols', 'type': 'text', 'label': 'Símbolos de Trading (separados por vírgula)', 'default': 'BTC-USDT,ETH-USDT,SOL-USDT'},
        {'name': 'rsi_period', 'type': 'number', 'label': 'Período RSI', 'default': 14},
        {'name': 'wma_period', 'type': 'number', 'label': 'Período WMA', 'default': 20},
    ],
    '777_top_50_analyzer_macd.py': [
        {'name': 'trading_symbols', 'type': 'text', 'label': 'Símbolos de Trading (separados por vírgula)', 'default': 'BTC-USDT,ETH-USDT,SOL-USDT'},
        {'name': 'rsi_period', 'type': 'number', 'label': 'Período RSI', 'default': 14},
        {'name': 'wma_period', 'type': 'number', 'label': 'Período WMA', 'default': 20},
    ],
    '777_cache_cleaner.py': [
        {'name': 'cache_path', 'type': 'text', 'label': 'Caminho do Cache', 'default': './cache'},
    ],
    # Adicione mais bots e parâmetros conforme necessário
}

# Layout do dashboard
def generate_bot_controls():
    controls = []
    for script in scripts_777:
        params = bot_parameters.get(script, [])
        param_inputs = []
        for param in params:
            input_id = f"{script.replace('.', '_')}_{param['name']}"
            if param['type'] == 'text':
                input_component = dcc.Input(id=input_id, value=param['default'], type='text', placeholder=param['label'], style={'width': '100%', 'margin': '5px'})
            elif param['type'] == 'number':
                input_component = dcc.Input(id=input_id, value=param['default'], type='number', placeholder=param['label'], style={'width': '100%', 'margin': '5px'})
            elif param['type'] == 'dropdown':
                input_component = dcc.Dropdown(id=input_id, options=param.get('options', []), value=param['default'], placeholder=param['label'], style={'width': '100%', 'margin': '5px'})
            param_inputs.append(html.Div([html.Label(param['label'], style={'fontWeight': 'bold'}), input_component], style={'marginBottom': '10px'}))
        
        controls.append(
            html.Div([
                html.H3(script, style={'color': '#2c3e50', 'borderBottom': '2px solid #3498db', 'paddingBottom': '5px'}),
                html.Div([
                    html.Div(param_inputs, style={'flex': '1', 'padding': '10px'}),
                    html.Div([
                        html.Button('Iniciar', id=f'start_{script.replace(".", "_")}', n_clicks=0, style={'backgroundColor': '#2ecc71', 'color': 'white', 'border': 'none', 'padding': '10px 20px', 'textAlign': 'center', 'textDecoration': 'none', 'display': 'inline-block', 'fontSize': '16px', 'margin': '4px 2px', 'cursor': 'pointer', 'borderRadius': '4px'}),
                        html.Button('Parar', id=f'stop_{script.replace(".", "_")}', n_clicks=0, style={'backgroundColor': '#e74c3c', 'color': 'white', 'border': 'none', 'padding': '10px 20px', 'textAlign': 'center', 'textDecoration': 'none', 'display': 'inline-block', 'fontSize': '16px', 'margin': '4px 2px', 'cursor': 'pointer', 'borderRadius': '4px'}),
                        html.Div(id=f'status_{script.replace(".", "_")}', children=f'Status: {script_status[script]}', style={'marginTop': '10px', 'fontSize': '18px', 'color': '#7f8c8d'})
                    ], style={'flex': '1', 'padding': '10px', 'textAlign': 'center'}),
                ], style={'display': 'flex', 'justifyContent': 'space-between'}),
                html.Div([
                    html.H4('Logs do Script', style={'marginTop': '20px', 'color': '#34495e'}),
                    html.Pre(id=f'log_{script.replace(".", "_")}', children='Nenhum log disponível no momento.', style={'backgroundColor': '#f8f9fa', 'padding': '10px', 'borderRadius': '5px', 'maxHeight': '200px', 'overflowY': 'auto', 'border': '1px solid #ddd'})
                ])
            ], style={'border': '1px solid #ccc', 'padding': '15px', 'margin': '15px', 'borderRadius': '8px', 'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)', 'backgroundColor': '#fff'})
        )
    return controls

app.layout = html.Div([
    html.H1('Dashboard 777 Bots', style={'textAlign': 'center', 'color': '#2c3e50', 'marginBottom': '30px', 'fontFamily': 'Arial, sans-serif'}),
    html.Div(generate_bot_controls(), style={'margin': '0 auto', 'width': '90%', 'fontFamily': 'Arial, sans-serif', 'backgroundColor': '#ecf0f1', 'padding': '20px', 'borderRadius': '10px'})
], style={'backgroundColor': '#f2f2f2', 'minHeight': '100vh', 'padding': '20px'})

# Callbacks para iniciar e parar scripts
for script in scripts_777:
    @app.callback(
        Output(f'status_{script.replace(".", "_")}', 'children'),
        [Input(f'start_{script.replace(".", "_")}', 'n_clicks'),
         Input(f'stop_{script.replace(".", "_")}', 'n_clicks')],
        [State(f'status_{script.replace(".", "_")}', 'children')]
    )
    def update_script_status(start_clicks, stop_clicks, current_status, script=script):
        ctx = dash.callback_context
        if not ctx.triggered:
            return f'Status: {script_status[script]}'
        
        triggered_id = ctx.triggered[0]['prop_id'].split('.')[0]
        if triggered_id == f'start_{script.replace(".", "_")}' and start_clicks > 0:
            if script_status[script] == 'Parado':
                # Coleta parâmetros
                params = []
                for param in bot_parameters.get(script, []):
                    param_id = f"{script}_{param['name']}"
                    param_value = dash.no_update  # Será atualizado pelo valor do input, mas como exemplo, assumimos que está disponível
                    params.extend(['--' + param['name'], str(param_value)])
                
                # Inicia o script
                command = ['python', script] + params
                process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, preexec_fn=os.setsid)
                process_dict[script] = process
                script_status[script] = 'Rodando'
                return f'Status: Rodando (PID: {process.pid})'
        elif triggered_id == f'stop_{script.replace(".", "_")}' and stop_clicks > 0:
            if script_status[script] == 'Rodando':
                process = process_dict[script]
                if process:
                    os.killpg(process.pid, signal.SIGTERM)
                    process_dict[script] = None
                    script_status[script] = 'Parado'
                    return 'Status: Parado'
        return f'Status: {script_status[script]}'

# Rodar o app
if __name__ == '__main__':
    app.run(debug=True, port=8051)
