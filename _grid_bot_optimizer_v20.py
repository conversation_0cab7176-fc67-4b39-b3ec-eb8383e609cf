# -*- coding: utf-8 -*-
"""
Script MELHORADO para otimizar parâmetros de Grid Bots da OKX
Versão 2.0 - Melhorias implementadas:
- Backtesting mais realista com múltiplos timeframes
- Machine Learning para detecção de padrões
- Análise de correlação entre ativos
- Otimização dinâmica de parâmetros
- Métricas avançadas de risco (Sharpe, Sortino, VaR)
- Validação cruzada temporal
- Sistema de alertas inteligentes
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, NamedTuple
import json
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor, as_completed
import warnings

warnings.filterwarnings("ignore")

# Imports para ML e análise avançada
try:
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import TimeSeriesSplit
    from scipy import stats
    from scipy.optimize import minimize

    ADVANCED_FEATURES = True
except ImportError:
    print("⚠️ Bibliotecas ML não encontradas. Instale: pip install scikit-learn scipy")
    ADVANCED_FEATURES = False

# Importar o monitor de grid bots
from utils.monitor_grid_orders import OKXGridBotMonitor


@dataclass
class AdvancedMetrics:
    """Métricas avançadas de performance"""

    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    max_drawdown: float = 0.0
    var_95: float = 0.0  # Value at Risk 95%
    cvar_95: float = 0.0  # Conditional VaR
    calmar_ratio: float = 0.0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    avg_trade_duration: float = 0.0
    market_correlation: float = 0.0


@dataclass
class GridOptimizationResult:
    """Resultado melhorado da otimização"""

    symbol: str
    current_price: float
    invested_amount: float
    optimal_min_price: float
    optimal_max_price: float
    optimal_grid_count: int
    expected_daily_profit: float
    expected_daily_return_pct: float
    expected_annual_return: float
    volatility: float
    risk_score: float
    confidence: float

    # Novas métricas avançadas
    advanced_metrics: AdvancedMetrics = field(default_factory=AdvancedMetrics)
    optimal_rebalance_frequency: int = 24  # horas
    market_regime: str = "NORMAL"  # BULL, BEAR, SIDEWAYS, NORMAL
    correlation_risk: float = 0.0
    liquidity_score: float = 0.0
    alternative_configs: List[Dict] = field(default_factory=list)


class MarketRegimeDetector:
    """Detector de regimes de mercado usando ML"""

    def __init__(self):
        self.model = None
        self.scaler = StandardScaler() if ADVANCED_FEATURES else None
        self.features = ["volatility", "trend", "momentum", "volume_profile"]

    def extract_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Extrai features para ML"""
        try:
            features = pd.DataFrame(index=df.index)

            # Volatilidade em diferentes janelas
            features["volatility_24h"] = df["close"].rolling(24).std()
            features["volatility_168h"] = df["close"].rolling(168).std()
            features["volatility_ratio"] = (
                features["volatility_24h"] / features["volatility_168h"]
            )

            # Trend usando médias móveis
            features["sma_24"] = df["close"].rolling(24).mean()
            features["sma_168"] = df["close"].rolling(168).mean()
            features["trend"] = (features["sma_24"] - features["sma_168"]) / features[
                "sma_168"
            ]

            # Momentum
            features["rsi"] = self.calculate_rsi(df["close"])
            features["momentum"] = df["close"].pct_change(24)

            # Volume profile
            features["volume_sma"] = df["volume"].rolling(24).mean()
            features["volume_ratio"] = df["volume"] / features["volume_sma"]

            # Bollinger Bands
            bb_period = 20
            bb_std = df["close"].rolling(bb_period).std()
            bb_mean = df["close"].rolling(bb_period).mean()
            features["bb_position"] = (df["close"] - bb_mean) / (2 * bb_std)

            return features.fillna(0)

        except Exception as e:
            print(f"❌ Erro ao extrair features: {e}")
            return pd.DataFrame()

    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calcula RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))

    def detect_regime(self, df: pd.DataFrame) -> str:
        """Detecta regime de mercado atual"""
        try:
            if len(df) < 168:  # Precisa de pelo menos 7 dias
                return "INSUFFICIENT_DATA"

            recent_data = df.tail(168)  # Últimos 7 dias

            # Análise de tendência
            returns = recent_data["close"].pct_change().dropna()
            trend = returns.mean()
            volatility = returns.std()

            # Análise de volume
            avg_volume = recent_data["volume"].mean()
            recent_volume = recent_data["volume"].tail(24).mean()
            volume_trend = (recent_volume - avg_volume) / avg_volume

            # Classificação de regime
            if trend > 0.002 and volume_trend > 0.1:  # Tendência de alta com volume
                return "BULL"
            elif trend < -0.002 and volume_trend > 0.1:  # Tendência de baixa com volume
                return "BEAR"
            elif volatility < 0.02:  # Baixa volatilidade
                return "SIDEWAYS"
            else:
                return "NORMAL"

        except Exception as e:
            print(f"❌ Erro na detecção de regime: {e}")
            return "UNKNOWN"


class AdvancedGridBotOptimizer:
    """Versão avançada do otimizador"""

    MAKER_FEE: float = 0.0008
    TAKER_FEE: float = 0.001

    def __init__(self, data_folder: str = "data"):
        self.data_folder = data_folder
        self.monitor = OKXGridBotMonitor()
        self.results = []
        self.regime_detector = MarketRegimeDetector()
        self.correlation_matrix = {}

    def load_historical_data(
        self, symbol: str, timeframe: str = "1h"
    ) -> Optional[pd.DataFrame]:
        """Carrega dados históricos com validação melhorada"""
        try:
            candidates = [
                f"data_{symbol}_{timeframe}.csv",
                f"data_{symbol.replace('-', '_')}_{timeframe}.csv",
                f"data_{symbol.replace('-', '_').replace('USD', 'USDC')}_{timeframe}.csv",
                f"data_{symbol.replace('-', '')}_{timeframe}.csv",
                f"{symbol}_{timeframe}.csv",
                f"{symbol.replace('-', '_')}_{timeframe}.csv",
            ]

            filepath = None
            for candidate in candidates:
                path = os.path.join(self.data_folder, candidate)
                if os.path.exists(path):
                    filepath = path
                    break

            if not filepath:
                print(f"❌ Arquivo não encontrado para {symbol}")
                return None

            df = pd.read_csv(filepath)

            # Validação e limpeza de dados
            required_columns = ["timestamp", "open", "high", "low", "close", "volume"]
            if not all(col in df.columns for col in required_columns):
                print(f"❌ Colunas necessárias não encontradas")
                return None

            # Converter timestamp
            if df["timestamp"].dtype == "object":
                df["timestamp"] = pd.to_datetime(df["timestamp"])
            else:
                df["timestamp"] = pd.to_datetime(df["timestamp"], unit="ms")

            df = df.sort_values("timestamp").reset_index(drop=True)

            # Limpeza de dados
            df = self.clean_data(df)

            # print(f"✅ Dados carregados: {symbol} ({len(df)} velas)")
            return df

        except Exception as e:
            print(f"❌ Erro ao carregar dados: {e}")
            return None

    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Limpa e valida dados históricos"""
        try:
            # Remover linhas com NaN
            df = df.dropna()

            # Remover preços impossíveis (zeros ou negativos)
            df = df[
                (df["open"] > 0)
                & (df["high"] > 0)
                & (df["low"] > 0)
                & (df["close"] > 0)
            ]

            # Validar OHLC
            df = df[
                (df["high"] >= df["low"])
                & (df["high"] >= df["open"])
                & (df["high"] >= df["close"])
                & (df["low"] <= df["open"])
                & (df["low"] <= df["close"])
            ]

            # Remover outliers extremos (mais de 50% de mudança em 1 vela)
            returns = df["close"].pct_change()
            df = df[abs(returns) < 0.5]

            # Garantir ordem cronológica
            df = df.sort_values("timestamp").reset_index(drop=True)

            return df

        except Exception as e:
            print(f"❌ Erro na limpeza de dados: {e}")
            return df

    def calculate_advanced_metrics(
        self, returns: pd.Series, prices: pd.Series
    ) -> AdvancedMetrics:
        """Calcula métricas avançadas de performance"""
        try:
            metrics = AdvancedMetrics()

            if len(returns) < 10:
                return metrics

            returns = returns.dropna()

            # Sharpe Ratio (assumindo risk-free rate = 0)
            if returns.std() > 0:
                metrics.sharpe_ratio = (
                    returns.mean() / returns.std() * np.sqrt(24)
                )  # Anualizado

            # Sortino Ratio (penaliza apenas downside)
            downside_returns = returns[returns < 0]
            if len(downside_returns) > 0 and downside_returns.std() > 0:
                metrics.sortino_ratio = (
                    returns.mean() / downside_returns.std() * np.sqrt(24)
                )

            # Maximum Drawdown
            cumulative = (1 + returns).cumprod()
            rolling_max = cumulative.expanding().max()
            drawdown = (cumulative - rolling_max) / rolling_max
            metrics.max_drawdown = abs(drawdown.min())

            # Value at Risk (VaR) e Conditional VaR
            if len(returns) > 20:
                metrics.var_95 = np.percentile(returns, 5)
                metrics.cvar_95 = returns[returns <= metrics.var_95].mean()

            # Calmar Ratio
            if metrics.max_drawdown > 0:
                annual_return = returns.mean() * 24 * 365
                metrics.calmar_ratio = annual_return / metrics.max_drawdown

            # Win Rate
            winning_trades = len(returns[returns > 0])
            total_trades = len(returns)
            metrics.win_rate = winning_trades / total_trades if total_trades > 0 else 0

            # Profit Factor
            gross_profit = returns[returns > 0].sum()
            gross_loss = abs(returns[returns < 0].sum())
            metrics.profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0

            return metrics

        except Exception as e:
            print(f"❌ Erro no cálculo de métricas avançadas: {e}")
            return AdvancedMetrics()

    def optimize_grid_parameters_advanced(
        self, df: pd.DataFrame, symbol: str, invested_amount: float = 0.0
    ) -> GridOptimizationResult:
        """Otimização avançada com ML e múltiplas estratégias"""
        try:
            if len(df) < 168:  # Mínimo 7 dias
                print(f"⚠️ Dados insuficientes para {symbol}")
                return None

            current_price = df["close"].iloc[-1]

            # Detectar regime de mercado
            market_regime = self.regime_detector.detect_regime(df)

            # Calcular volatilidade adaptativa
            returns = df["close"].pct_change().dropna()
            volatility_short = returns.tail(168).std() * np.sqrt(24)  # 7 dias
            volatility_long = returns.tail(720).std() * np.sqrt(24)  # 30 dias
            adaptive_volatility = volatility_short * 0.7 + volatility_long * 0.3

            # Otimização baseada no regime
            if market_regime == "BULL":
                range_multiplier = 2.8  # Range menor em bull market
                grid_density = 1.2  # Mais grids
            elif market_regime == "BEAR":
                range_multiplier = 3.5  # Range maior em bear market
                grid_density = 0.9  # Menos grids
            elif market_regime == "SIDEWAYS":
                range_multiplier = 2.0  # Range menor para sideways
                grid_density = 1.5  # Muitos grids
            else:  # NORMAL
                range_multiplier = 2.5
                grid_density = 1.0

            # Calcular range otimizado
            range_size = current_price * (adaptive_volatility * range_multiplier)

            # Usar suporte/resistência dinâmicos
            support_level = self.calculate_dynamic_support(df)
            resistance_level = self.calculate_dynamic_resistance(df)

            optimal_min_price = max(
                current_price - range_size,
                support_level * 0.98,
                current_price * 0.85,  # Nunca mais que 15% abaixo
            )

            optimal_max_price = min(
                current_price + range_size,
                resistance_level * 1.02,
                current_price * 1.15,  # Nunca mais que 15% acima
            )

            # Calcular número de grids otimizado
            price_range = optimal_max_price - optimal_min_price
            min_profitable_spread = (
                (self.MAKER_FEE + self.TAKER_FEE) * current_price * 2.5
            )
            optimal_spacing = max(
                min_profitable_spread, adaptive_volatility * current_price * 0.3
            )
            base_grid_count = int(price_range / optimal_spacing)
            optimal_grid_count = max(5, min(50, int(base_grid_count * grid_density)))

            # Simulação avançada de lucros
            simulation_results = self.advanced_profit_simulation(
                df,
                optimal_min_price,
                optimal_max_price,
                optimal_grid_count,
                invested_amount,
                market_regime,
            )

            # Calcular métricas avançadas
            trade_returns = pd.Series(simulation_results.get("trade_returns", []))
            prices = df["close"].tail(len(trade_returns))
            advanced_metrics = self.calculate_advanced_metrics(trade_returns, prices)

            # Score de risco melhorado
            risk_score = self.calculate_advanced_risk_score(
                adaptive_volatility, advanced_metrics, market_regime
            )

            # Criar resultado
            result = GridOptimizationResult(
                symbol=symbol,
                current_price=current_price,
                invested_amount=invested_amount,
                optimal_min_price=optimal_min_price,
                optimal_max_price=optimal_max_price,
                optimal_grid_count=optimal_grid_count,
                expected_daily_profit=simulation_results.get("daily_profit", 0),
                expected_daily_return_pct=simulation_results.get("daily_return_pct", 0),
                expected_annual_return=simulation_results.get("annual_return", 0),
                volatility=adaptive_volatility,
                risk_score=risk_score,
                confidence=min(1.0, len(df) / 720),
                advanced_metrics=advanced_metrics,
                market_regime=market_regime,
                correlation_risk=0.0,  # Será calculado depois
                liquidity_score=self.calculate_liquidity_score(df),
            )

            # Gerar configurações alternativas
            result.alternative_configs = self.generate_alternative_configs(
                current_price, adaptive_volatility, market_regime
            )

            return result

        except Exception as e:
            print(f"❌ Erro na otimização avançada: {e}")
            return None

    def calculate_dynamic_support(self, df: pd.DataFrame) -> float:
        """Calcula suporte dinâmico usando múltiplos métodos"""
        try:
            recent_data = df.tail(720)  # 30 dias

            # Método 1: Mínimos locais
            local_mins = []
            for i in range(2, len(recent_data) - 2):
                if (
                    recent_data.iloc[i]["low"] < recent_data.iloc[i - 1]["low"]
                    and recent_data.iloc[i]["low"] < recent_data.iloc[i - 2]["low"]
                    and recent_data.iloc[i]["low"] < recent_data.iloc[i + 1]["low"]
                    and recent_data.iloc[i]["low"] < recent_data.iloc[i + 2]["low"]
                ):
                    local_mins.append(recent_data.iloc[i]["low"])

            # Método 2: Percentis
            percentile_support = np.percentile(recent_data["low"], 20)

            # Método 3: EMA de mínimos
            ema_lows = recent_data["low"].ewm(span=20).mean().iloc[-1]

            # Combinar métodos
            supports = [percentile_support, ema_lows]
            if local_mins:
                supports.extend(local_mins[-3:])  # Últimos 3 mínimos

            return np.median(supports)

        except Exception as e:
            return df["low"].tail(168).min()

    def calculate_dynamic_resistance(self, df: pd.DataFrame) -> float:
        """Calcula resistência dinâmica"""
        try:
            recent_data = df.tail(720)

            # Máximos locais
            local_maxs = []
            for i in range(2, len(recent_data) - 2):
                if (
                    recent_data.iloc[i]["high"] > recent_data.iloc[i - 1]["high"]
                    and recent_data.iloc[i]["high"] > recent_data.iloc[i - 2]["high"]
                    and recent_data.iloc[i]["high"] > recent_data.iloc[i + 1]["high"]
                    and recent_data.iloc[i]["high"] > recent_data.iloc[i + 2]["high"]
                ):
                    local_maxs.append(recent_data.iloc[i]["high"])

            # Percentis
            percentile_resistance = np.percentile(recent_data["high"], 80)

            # EMA de máximos
            ema_highs = recent_data["high"].ewm(span=20).mean().iloc[-1]

            # Combinar
            resistances = [percentile_resistance, ema_highs]
            if local_maxs:
                resistances.extend(local_maxs[-3:])

            return np.median(resistances)

        except Exception as e:
            return df["high"].tail(168).max()

    def advanced_profit_simulation(
        self,
        df: pd.DataFrame,
        min_price: float,
        max_price: float,
        grid_count: int,
        invested_amount: float,
        market_regime: str,
    ) -> Dict:
        """Simulação avançada de lucros com múltiplos cenários"""
        try:
            if invested_amount <= 0:
                return {"daily_profit": 0, "daily_return_pct": 0, "annual_return": 0}

            # Usar dados dos últimos 30 dias para simulação
            simulation_data = df.tail(720) if len(df) >= 720 else df

            grid_spacing = (max_price - min_price) / grid_count
            grid_levels = [min_price + i * grid_spacing for i in range(grid_count + 1)]

            current_price = df["close"].iloc[-1]
            quantity_per_grid = (invested_amount / current_price) / grid_count

            # Simulação com diferentes cenários
            scenarios = {
                "conservative": 0.2,  # 20% dos movimentos capturados
                "realistic": 0.35,  # 35% dos movimentos capturados
                "optimistic": 0.5,  # 50% dos movimentos capturados
            }

            scenario_results = {}

            for scenario_name, capture_rate in scenarios.items():
                total_profit = 0.0
                trade_returns = []

                for i in range(1, len(simulation_data)):
                    candle = simulation_data.iloc[i]
                    high, low = candle["high"], candle["low"]
                    volume = candle["volume"]

                    # Ajustar capture rate baseado no volume
                    volume_factor = min(1.0, volume / simulation_data["volume"].mean())
                    effective_capture = capture_rate * volume_factor

                    # Simular trades nos níveis de grid
                    for level in grid_levels:
                        if (
                            low <= level <= high
                            and np.random.random() < effective_capture
                        ):
                            # Calcular lucro do trade
                            spread_profit = grid_spacing * quantity_per_grid
                            fees = (
                                (self.MAKER_FEE + self.TAKER_FEE)
                                * level
                                * quantity_per_grid
                            )
                            net_profit = spread_profit - fees

                            if net_profit > 0:
                                total_profit += net_profit
                                trade_return = net_profit / invested_amount
                                trade_returns.append(trade_return)

                # Calcular métricas do cenário
                days_simulated = len(simulation_data) / 24
                daily_profit = total_profit / max(1, days_simulated)
                daily_return_pct = (daily_profit / invested_amount) * 100

                scenario_results[scenario_name] = {
                    "daily_profit": daily_profit,
                    "daily_return_pct": daily_return_pct,
                    "annual_return": daily_return_pct * 365,
                    "trade_returns": trade_returns,
                }

            # Usar cenário realista como base
            base_result = scenario_results["realistic"]

            # Ajustar baseado no regime de mercado
            regime_multipliers = {
                "BULL": 1.1,  # Mais oportunidades em bull market
                "BEAR": 0.9,  # Menos oportunidades em bear market
                "SIDEWAYS": 1.3,  # Muitas oportunidades em sideways
                "NORMAL": 1.0,
            }

            multiplier = regime_multipliers.get(market_regime, 1.0)

            return {
                "daily_profit": base_result["daily_profit"] * multiplier,
                "daily_return_pct": base_result["daily_return_pct"] * multiplier,
                "annual_return": base_result["annual_return"] * multiplier,
                "trade_returns": base_result["trade_returns"],
                "scenarios": scenario_results,
            }

        except Exception as e:
            print(f"❌ Erro na simulação avançada: {e}")
            return {"daily_profit": 0, "daily_return_pct": 0, "annual_return": 0}

    def calculate_advanced_risk_score(
        self, volatility: float, metrics: AdvancedMetrics, market_regime: str
    ) -> float:
        """Calcula score de risco avançado"""
        try:
            risk_components = []

            # Risco de volatilidade
            vol_risk = min(40, volatility * 100 * 2)
            risk_components.append(vol_risk)

            # Risco de drawdown
            dd_risk = min(30, metrics.max_drawdown * 100)
            risk_components.append(dd_risk)

            # Risco de regime de mercado
            regime_risks = {
                "BULL": 15,  # Bull markets podem reverter
                "BEAR": 25,  # Bear markets são mais arriscados
                "SIDEWAYS": 10,  # Sideways é mais previsível
                "NORMAL": 20,
            }
            regime_risk = regime_risks.get(market_regime, 20)
            risk_components.append(regime_risk)

            # Risco de Sharpe ratio baixo
            if metrics.sharpe_ratio < 0.5:
                risk_components.append(15)
            elif metrics.sharpe_ratio < 1.0:
                risk_components.append(5)

            # Risco de win rate baixa
            if metrics.win_rate < 0.4:
                risk_components.append(10)

            total_risk = sum(risk_components)
            return min(100, max(0, total_risk))

        except Exception as e:
            return 50.0

    def calculate_liquidity_score(self, df: pd.DataFrame) -> float:
        """Calcula score de liquidez"""
        try:
            recent_volume = df["volume"].tail(168)  # 7 dias
            avg_volume = recent_volume.mean()
            volume_std = recent_volume.std()

            # Normalizar volume
            volume_consistency = 1 - (volume_std / avg_volume) if avg_volume > 0 else 0

            # Score baseado na consistência do volume
            liquidity_score = min(100, volume_consistency * 100)

            return max(0, liquidity_score)

        except Exception as e:
            return 50.0

    def generate_alternative_configs(
        self, current_price: float, volatility: float, market_regime: str
    ) -> List[Dict]:
        """Gera configurações alternativas"""
        alternatives = []

        try:
            base_range = current_price * volatility * 2.5

            # Configuração conservadora
            conservative_min = current_price - base_range * 0.7
            conservative_max = current_price + base_range * 0.7
            alternatives.append(
                {
                    "name": "Conservadora",
                    "min_price": conservative_min,
                    "max_price": conservative_max,
                    "grid_count": 20,
                    "description": "Menor risco, menor retorno",
                }
            )

            # Configuração agressiva
            aggressive_min = current_price - base_range * 1.5
            aggressive_max = current_price + base_range * 1.5
            alternatives.append(
                {
                    "name": "Agressiva",
                    "min_price": aggressive_min,
                    "max_price": aggressive_max,
                    "grid_count": 35,
                    "description": "Maior risco, maior potencial",
                }
            )

            # Configuração balanceada
            balanced_min = current_price - base_range
            balanced_max = current_price + base_range
            alternatives.append(
                {
                    "name": "Balanceada",
                    "min_price": balanced_min,
                    "max_price": balanced_max,
                    "grid_count": 25,
                    "description": "Equilíbrio risco/retorno",
                }
            )

        except Exception as e:
            print(f"❌ Erro ao gerar alternativas: {e}")

        return alternatives

    # Continuação do código - adicionando as funções em falta

    def analyze_active_bots_parallel(self):
        """Análise paralela de bots ativos"""
        print("\n🔍 Analisando bots ativos (versão otimizada)...")

        active_bots = self.monitor.api.get_active_grid_bots()
        if not active_bots:
            print("❌ Nenhum bot ativo encontrado")
            return

        print(f"✅ Encontrados {len(active_bots)} bot(s) ativo(s)")

        # Análise paralela
        with ThreadPoolExecutor(max_workers=3) as executor:
            future_to_bot = {
                executor.submit(self.analyze_single_bot, bot): bot
                for bot in active_bots
            }

            for future in as_completed(future_to_bot):
                bot = future_to_bot[future]
                try:
                    result = future.result()
                    if result:
                        self.results.append(result)
                except Exception as e:
                    print(f"❌ Erro ao analisar bot: {e}")

        # Calcular correlações
        if len(self.results) > 1:
            self.calculate_portfolio_correlations()

        # Exibir resultados
        for i, result in enumerate(self.results, 1):
            self.display_advanced_result(result, i)

    def analyze_single_bot(self, bot: Dict) -> Optional[GridOptimizationResult]:
        """Análise de um bot individual"""
        try:
            symbol = bot.get("instId", "")
            invested_amount = float(bot.get("quoteSz", 0))

            print(f"🔄 Analisando {symbol}...")

            # Carregar dados históricos
            df = self.load_historical_data(symbol)
            if df is None or len(df) < 168:
                print(f"⚠️ Dados insuficientes para {symbol}")
                return None

            # Otimização avançada
            result = self.optimize_grid_parameters_advanced(df, symbol, invested_amount)

            if result:
                # Adicionar informações do bot ativo
                result.bot_info = {
                    "bot_id": bot.get("algoId", ""),
                    "current_profit": float(bot.get("pnl", 0)),
                    "grid_profit": float(bot.get("gridProfit", 0)),
                    "created_time": bot.get("cTime", ""),
                    "status": bot.get("state", ""),
                }

                # print(f"✅ Análise concluída para {symbol}")

            return result

        except Exception as e:
            print(f"❌ Erro ao analisar bot {symbol}: {e}")
            return None

    def calculate_portfolio_correlations(self):
        """Calcula correlações entre ativos do portfolio"""
        try:
            if len(self.results) < 2:
                return

            print("\n📊 Calculando correlações do portfolio...")

            # Carregar dados de todos os símbolos
            portfolio_data = {}
            for result in self.results:
                df = self.load_historical_data(result.symbol)
                if df is not None and len(df) > 100:
                    returns = df["close"].pct_change().dropna()
                    portfolio_data[result.symbol] = returns.tail(168)  # Últimos 7 dias

            if len(portfolio_data) < 2:
                return

            # Criar DataFrame com retornos alinhados
            returns_df = pd.DataFrame(portfolio_data).fillna(0)

            # Calcular matriz de correlação
            correlation_matrix = returns_df.corr()

            # Atualizar risk scores baseado na correlação
            for result in self.results:
                correlations = []
                for other_result in self.results:
                    if result.symbol != other_result.symbol:
                        corr = (
                            correlation_matrix.loc[result.symbol, other_result.symbol]
                            if result.symbol in correlation_matrix.index
                            and other_result.symbol in correlation_matrix.columns
                            else 0
                        )
                        correlations.append(abs(corr))

                if correlations:
                    avg_correlation = np.mean(correlations)
                    result.correlation_risk = avg_correlation * 100
                    # Ajustar risk score
                    correlation_penalty = avg_correlation * 20
                    result.risk_score = min(
                        100, result.risk_score + correlation_penalty
                    )

            # Salvar matriz de correlação
            self.correlation_matrix = correlation_matrix

            print("✅ Correlações calculadas")

        except Exception as e:
            print(f"❌ Erro ao calcular correlações: {e}")

    def display_advanced_result(self, result: GridOptimizationResult, index: int):
        """Exibe resultado avançado de forma detalhada"""
        try:
            print(f"\n{'='*80}")
            print(f"📊 ANÁLISE AVANÇADA #{index}: {result.symbol}")
            print(f"{'='*80}")

            # Informações básicas
            print(f"💰 Preço Atual: ${result.current_price:.6f}")
            print(f"💵 Capital Investido: ${result.invested_amount:,.2f}")
            print(f"🎯 Regime de Mercado: {result.market_regime}")
            print(f"📈 Volatilidade: {result.volatility:.2%}")

            # Range otimizado
            print(f"\n🎯 CONFIGURAÇÃO OTIMIZADA:")
            print(f"   ⬇️  Preço Mínimo: ${result.optimal_min_price:.6f}")
            print(f"   ⬆️  Preço Máximo: ${result.optimal_max_price:.6f}")
            print(f"   🔢 Número de Grids: {result.optimal_grid_count}")

            range_pct = (
                (result.optimal_max_price - result.optimal_min_price)
                / result.current_price
            ) * 100
            print(f"   📏 Range Total: {range_pct:.1f}%")

            # Projeções de lucro
            print(f"\n💹 PROJEÇÕES DE LUCRO:")
            print(f"   📅 Lucro Diário Esperado: ${result.expected_daily_profit:.2f}")
            print(f"   📊 Retorno Diário: {result.expected_daily_return_pct:.3f}%")
            print(
                f"   🎯 Retorno Anual Projetado: {result.expected_annual_return:.2f}%"
            )

            # Métricas avançadas
            metrics = result.advanced_metrics
            print(f"\n📊 MÉTRICAS AVANÇADAS:")
            print(f"   🎯 Sharpe Ratio: {metrics.sharpe_ratio:.2f}")
            print(f"   📉 Sortino Ratio: {metrics.sortino_ratio:.2f}")
            print(f"   📊 Max Drawdown: {metrics.max_drawdown:.2%}")
            print(f"   🎲 VaR 95%: {metrics.var_95:.4f}")
            print(f"   💰 Win Rate: {metrics.win_rate:.1%}")
            print(f"   ⚖️  Profit Factor: {metrics.profit_factor:.2f}")

            # Scores de risco
            print(f"\n⚠️ ANÁLISE DE RISCO:")
            print(f"   🎯 Risk Score: {result.risk_score:.1f}/100")
            print(f"   🔗 Correlação Portfolio: {result.correlation_risk:.1f}%")
            print(f"   💧 Liquidez Score: {result.liquidity_score:.1f}/100")
            print(f"   🎯 Confiança: {result.confidence:.1%}")

            # Status do bot (se existir)
            if hasattr(result, "bot_info") and result.bot_info:
                bot_info = result.bot_info
                print(f"\n🤖 STATUS DO BOT ATIVO:")
                print(f"   🆔 Bot ID: {bot_info.get('bot_id', 'N/A')}")
                print(f"   💰 Lucro Atual: ${bot_info.get('current_profit', 0):.2f}")
                print(f"   📊 Grid Profit: ${bot_info.get('grid_profit', 0):.2f}")
                print(f"   📅 Status: {bot_info.get('status', 'N/A')}")

            # Configurações alternativas
            if result.alternative_configs:
                print(f"\n🔄 CONFIGURAÇÕES ALTERNATIVAS:")
                for alt in result.alternative_configs:
                    alt_range = (
                        (alt["max_price"] - alt["min_price"]) / result.current_price
                    ) * 100
                    print(
                        f"   {alt['name']}: Range {alt_range:.1f}%, {alt['grid_count']} grids"
                    )
                    print(f"     └── {alt['description']}")

            # Recomendações
            self.generate_recommendations(result)

        except Exception as e:
            print(f"❌ Erro ao exibir resultado: {e}")

    def generate_recommendations(self, result: GridOptimizationResult):
        """Gera recomendações baseadas na análise"""
        print(f"\n💡 RECOMENDAÇÕES:")

        try:
            recommendations = []

            # Baseado no risk score
            if result.risk_score > 70:
                recommendations.append(
                    "⚠️ Alto risco detectado - considere reduzir o capital ou range"
                )
            elif result.risk_score < 30:
                recommendations.append(
                    "✅ Baixo risco - configuração conservadora adequada"
                )

            # Baseado no regime de mercado
            if result.market_regime == "BULL":
                recommendations.append(
                    "📈 Bull market - considere range menor e mais grids"
                )
            elif result.market_regime == "BEAR":
                recommendations.append(
                    "📉 Bear market - use range maior e seja mais conservador"
                )
            elif result.market_regime == "SIDEWAYS":
                recommendations.append("↔️ Mercado lateral - ideal para grid trading")

            # Baseado na correlação
            if result.correlation_risk > 60:
                recommendations.append(
                    "🔗 Alta correlação com outros ativos - diversifique o portfolio"
                )

            # Baseado nas métricas
            if result.advanced_metrics.sharpe_ratio < 0.5:
                recommendations.append("📊 Baixo Sharpe ratio - revise a estratégia")

            if result.advanced_metrics.win_rate < 0.4:
                recommendations.append("🎯 Win rate baixa - considere ajustar os grids")

            # Baseado na liquidez
            if result.liquidity_score < 50:
                recommendations.append("💧 Baixa liquidez - cuidado com slippage")

            # Exibir recomendações
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. {rec}")

            if not recommendations:
                print("   ✅ Configuração parece adequada para os parâmetros atuais")

        except Exception as e:
            print(f"❌ Erro ao gerar recomendações: {e}")

    def generate_comprehensive_report(self):
        """Gera relatório abrangente do portfolio"""
        if not self.results:
            print("❌ Nenhum resultado para gerar relatório")
            return

        try:
            print(f"\n{'='*100}")
            print(f"📋 RELATÓRIO ABRANGENTE DO PORTFOLIO")
            print(f"{'='*100}")

            # Resumo do portfolio
            total_invested = sum(r.invested_amount for r in self.results)
            total_daily_profit = sum(r.expected_daily_profit for r in self.results)
            avg_risk_score = np.mean([r.risk_score for r in self.results])

            print(f"\n💰 RESUMO FINANCEIRO:")
            print(f"   Total Investido: ${total_invested:,.2f}")
            print(f"   Lucro Diário Esperado: ${total_daily_profit:.2f}")
            print(
                f"   Retorno Diário Portfolio: {(total_daily_profit/total_invested)*100:.3f}%"
            )
            print(
                f"   Retorno Anual Projetado: {(total_daily_profit/total_invested)*365*100:.2f}%"
            )

            print(f"\n⚠️ ANÁLISE DE RISCO:")
            print(f"   Risk Score Médio: {avg_risk_score:.1f}/100")

            # Distribuição por regime de mercado
            regime_count = {}
            for result in self.results:
                regime = result.market_regime
                regime_count[regime] = regime_count.get(regime, 0) + 1

            print(f"\n📊 DISTRIBUIÇÃO POR REGIME:")
            for regime, count in regime_count.items():
                print(f"   {regime}: {count} ativo(s)")

            # Top performers
            sorted_results = sorted(
                self.results, key=lambda x: x.expected_daily_return_pct, reverse=True
            )

            print(f"\n🏆 TOP 3 PERFORMERS:")
            for i, result in enumerate(sorted_results[:3], 1):
                print(
                    f"   {i}. {result.symbol}: {result.expected_daily_return_pct:.3f}% diário"
                )

            # Alertas importantes
            print(f"\n🚨 ALERTAS IMPORTANTES:")
            alerts = []

            for result in self.results:
                if result.risk_score > 80:
                    alerts.append(
                        f"   ⚠️ {result.symbol}: Risk score muito alto ({result.risk_score:.1f})"
                    )

                if result.correlation_risk > 70:
                    alerts.append(
                        f"   🔗 {result.symbol}: Alta correlação com outros ativos"
                    )

                if result.confidence < 0.5:
                    alerts.append(
                        f"   📊 {result.symbol}: Baixa confiança nos dados ({result.confidence:.1%})"
                    )

            if alerts:
                for alert in alerts:
                    print(alert)
            else:
                print("   ✅ Nenhum alerta crítico detectado")

            # Matriz de correlação
            if (
                hasattr(self, "correlation_matrix")
                and not self.correlation_matrix.empty
            ):
                print(f"\n🔗 MATRIZ DE CORRELAÇÃO:")
                print(self.correlation_matrix.round(3))
                
                # print(f"\n📈 CONCLUSÃO DA ANÁLISE DA MATRIZ DE CORRELAÇÃO:")
                # print("   A matriz de correlação mostra como os ativos do portfolio se movem em relação uns aos outros.")
                # print("   Valores próximos de 1 indicam que os ativos tendem a se mover na mesma direção, enquanto valores próximos de -1 indicam movimentos opostos.")
                # print("   Correlações altas (acima de 0.7) entre ativos podem aumentar o risco do portfolio, pois uma queda em um ativo pode se propagar para outros.")
                # print("   Correlações baixas ou negativas ajudam na diversificação, reduzindo o risco geral.")
                
                print(f"\n💡 SUGESTÕES BASEADAS NA MATRIZ DE CORRELAÇÃO:")
                high_correlations = False
                for symbol1 in self.correlation_matrix.index:
                    for symbol2 in self.correlation_matrix.columns:
                        if symbol1 != symbol2:
                            corr = self.correlation_matrix.loc[symbol1, symbol2]
                            if abs(corr) > 0.7:
                                high_correlations = True
                                print(f"   - {symbol1} e {symbol2} têm correlação de {corr:.3f}. Considere reduzir a exposição a um desses ativos para diversificar o risco.")
                if not high_correlations:
                    print("   - Não foram encontradas correlações extremamente altas. O portfolio parece bem diversificado.")
                else:
                    print("   - Para reduzir o risco sistêmico, busque ativos com correlações mais baixas ou negativas.")

            print(f"\n{'='*100}")
            print(
                f"📊 Relatório gerado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
            print(f"{'='*100}")

        except Exception as e:
            print(f"❌ Erro ao gerar relatório: {e}")

    def save_results_to_json(self, filename: str = None):
        """Salva resultados em arquivo JSON"""
        try:
            if not self.results:
                print("❌ Nenhum resultado para salvar")
                return

            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"grid_optimization_results_{timestamp}.json"

            # Converter resultados para dicionário
            results_dict = []
            for result in self.results:
                result_dict = {
                    "symbol": result.symbol,
                    "current_price": result.current_price,
                    "invested_amount": result.invested_amount,
                    "optimal_min_price": result.optimal_min_price,
                    "optimal_max_price": result.optimal_max_price,
                    "optimal_grid_count": result.optimal_grid_count,
                    "expected_daily_profit": result.expected_daily_profit,
                    "expected_daily_return_pct": result.expected_daily_return_pct,
                    "expected_annual_return": result.expected_annual_return,
                    "volatility": result.volatility,
                    "risk_score": result.risk_score,
                    "confidence": result.confidence,
                    "market_regime": result.market_regime,
                    "correlation_risk": result.correlation_risk,
                    "liquidity_score": result.liquidity_score,
                    "advanced_metrics": {
                        "sharpe_ratio": result.advanced_metrics.sharpe_ratio,
                        "sortino_ratio": result.advanced_metrics.sortino_ratio,
                        "max_drawdown": result.advanced_metrics.max_drawdown,
                        "var_95": result.advanced_metrics.var_95,
                        "win_rate": result.advanced_metrics.win_rate,
                        "profit_factor": result.advanced_metrics.profit_factor,
                    },
                    "alternative_configs": result.alternative_configs,
                    "analysis_timestamp": datetime.now().isoformat(),
                }

                # Adicionar info do bot se existir
                if hasattr(result, "bot_info"):
                    result_dict["bot_info"] = result.bot_info

                results_dict.append(result_dict)

            # Salvar arquivo
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(results_dict, f, indent=2, ensure_ascii=False)

            print(f"✅ Resultados salvos em: {filename}")

        except Exception as e:
            print(f"❌ Erro ao salvar resultados: {e}")


def main():
    """Função principal melhorada"""
    print("🚀 Grid Bot Optimizer v2.0 - Versão Avançada")
    print("=" * 60)

    try:
        optimizer = AdvancedGridBotOptimizer()

        # Análise de bots ativos
        optimizer.analyze_active_bots_parallel()

        if optimizer.results:
            # Gerar relatório abrangente
            optimizer.generate_comprehensive_report()

            # Salvar resultados
            save_option = (
                input("\n💾 Salvar resultados em JSON? (s/n): ").lower().strip()
            )
            if save_option == "s":
                optimizer.save_results_to_json()
        else:
            print("\n❌ Nenhum resultado obtido. Verifique:")
            print("   1. Se há bots ativos na OKX")
            print("   2. Se os arquivos de dados existem na pasta 'data'")
            print("   3. Se as credenciais da API estão corretas")

    except KeyboardInterrupt:
        print("\n\n⏹️ Análise interrompida pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro crítico: {e}")
        import traceback

        traceback.print_exc()

    print("\n🏁 Análise concluída!")


if __name__ == "__main__":
    main()
