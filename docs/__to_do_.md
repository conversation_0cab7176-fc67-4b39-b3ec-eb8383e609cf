
__Database Usage__: While a database is initialized for storing orders, the full integration for tracking trades and performance metrics over time could be expanded.

- __Implement Risk Management__: Develop modules for risk control, including setting position limits, calculating VaR, monitoring drawdowns, and implementing circuit breakers for anomalous market conditions. This should be a priority given the TODO note and custom instructions.

- __Enhance Logging__: Expand logging to include detailed trade monitoring and risk decisions, aligning with the instruction for detailed logs.

- __Local Data Storage__: Normalize and store market data in a local SQLite time series database for faster access and analysis, as instructed.



# Spot Grid

- adicionar um triling up e um trailing down para ajustar automaticamente consoante o mercado se move
- adicionar um spike protection para proteger e evitar compras em picos rápidos
- prevenir grid inferior a 0.35% e maior que 1.25%
- ajustar o grid á volatilidade
- order size -> 


## Optuna
- testar TPE e random