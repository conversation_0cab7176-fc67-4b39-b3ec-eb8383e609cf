# GRID BOT
Optuna python
## Estrutura Base do Script

1. Configuração de Parâmetros flexível através de arquivos JSON (valor a investir, symbols, timegrame, perídos dos indicadores ...)
2. Análise de Dados de Mercado
3. Coleta de Dados Históricos (últimos 7 dias em timeframe - coleta de dados por etapas com 150 candles de cada vez até o total necessário)
4. Cálculo de Volatilidade
    ```
    def calculate_volatility(prices, period=14):
    returns = prices.pct_change()
    volatility = returns.rolling(window=period).std()
    return volatility
    ```
5. Determinação da Faixa de Preços
    ex.1: Detecção de Mercado Lateral com:
    indicador CHOP (Choppiness Index)
    ```
    def compute_CHOP(data, period=14):
    high_low = data['high'] - data['low']
    sum_hl = high_low.rolling(window=period).sum()
    high_close = abs(data['high'] - data['close'].shift(1))
    low_close = abs(data['low'] - data['close'].shift(1))
    true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
    atr = true_range.rolling(window=period).sum()
    chop = 100 * np.log10(sum_hl / atr) / np.log10(period)
    return chop
    ```
    ex.2: deteção de mercado lateral com outro indicador adequado

6. Cálculo Automático dos Grids, com base no ATR
    - Modos (definir com base no estado do merdado)
        - Modo Aritmético: Cada grid tem uma diferença de preço igual
        - Modo Geométrico: Cada grid tem um rácio de diferença de preço igual

    - Otimização do Número de Grids
    ```
    def calculate_grid_levels(price_low, price_high, num_grids, mode='arithmetic'):
    if mode == 'arithmetic':
        step = (price_high - price_low) / num_grids
        levels = [price_low + i * step for i in range(num_grids + 1)]
    elif mode == 'geometric':
        ratio = (price_high / price_low) ** (1 / num_grids)
        levels = [price_low * (ratio ** i) for i in range(num_grids + 1)]
    return levels
    ```

7. Gestão de Capital e Risco
- Cálculo de Alocação por Grid

8. Ajuste Dinâmico de Range
O script deve incluir funcionalidade para ajustar automaticamente a faixa de preços quando o mercado se move além dos limites estabelecidos, cancelando ordens fora do range e criando novas dentro da nova faixa

9. Sistema de Notificações
Integre notificações via email ou push notifications para alertar sobre execuções importantes, ajustes de parâmetros ou condições de mercado que requeiram atenção
    - envio notificação telegram
    - aviso sonoro 

10.  Monitoramento e Logging
Sistema de Logs Detalhado
Implemente logging abrangente que registre todas as operações, cálculos de parâmetros, execuções de ordens e ajustes de estratégia
    - Monitore o lucro por grid (diferença de preço de uma única grid) e o lucro da grid (lucro total de cada grid dentro do intervalo)

11. Métricas de Performance
O script deve calcular e monitorar continuamente métricas como lucro por grid, número de ciclos completados, taxa de utilização do capital e eficiência da estratégia
    - Taxas de maker: 0,080% e taker: 0,100%


12. Sincronização com Exchange
O script deve sincronizar regularmente as ordens locais com as ordens na exchange, garantindo consistência e evitando discrepâncias que podem resultar em perdas

13. Tratamento de Erros
    - Try/catch para operações críticas
    - Retry logic para falhas temporárias
    - Logging de erros detalhado