# TODO: create a new custom Roo Mode to fix Python code for algo trading, using the best practices, tools and methodologies and docstrings in portuguese. 

# TODO: criar módulo para calcular métricas após conclusão de ordens.

# TODO: coin360
# TODO: coincalendar
# TODO: https://quantifycrypto.com/coin-screener
# TODO: https://lunarcrush.com/topics
# TODO: https://cryptoquant.com/asset/btc/summary
# TODO: https://messari.io/
# TODO: https://finviz.com/
# TODO: https://www.livecoinwatch.com/

# TODO: Backtest trading strategies

# TODO: criar um template cli para criar novos scripts de trading



The scripts @/999_base_grid.py @/999_base_oco_wma.py @/999_base_trail_swing.py e @/999_base_trail_wma.py 

The task is to add PnL information to the Telegram message when closing positions. Looking at the file content, I see that the message template for closed positions is already present in two places:

1. When there's a matching buy order (with PnL calculation)
2. When there's no matching buy order (without PnL)
