import numpy as np
import talib
import threading
from typing import Optional, Tu<PERSON>, Dict, Any
from utils.exceptions import ConfigurationError
from utils.logger import TradingLogger

class TechnicalIndicator:
    """Calculadora de indicadores técnicos otimizada com cache de dados."""

    def __init__(self, client):
        self.client = client
        self.config = client.config
        self.logger = TradingLogger.get_logger(__name__)
        self._ohlcv_cache = {}  # Cache para dados OHLCV
        self._cache_order = []  # Lista para manter a ordem de inserção no cache
        self._cache_timestamps = {}  # Dicionário para armazenar timestamps de inserção
        self._max_cache_size = 100  # Limite máximo de entradas no cache (ajustável)
        self._cache_ttl = 3600  # Tempo de vida do cache em segundos (1 hora, ajustável)
        self._cache_lock = threading.Lock()  # Lock para thread safety no cache

    def _fetch_ohlcv_data(
        self, symbol: str, timeframe: str = "1h", limit: int = None
    ) -> np.ndarray:
        """
        Método base para obter dados OHLCV com cache para evitar requisições redundantes.

        Args:
            symbol: Par de trading
            timeframe: Intervalo de tempo
            limit: Número de candles

        Returns:
            Array numpy com dados OHLCV

        Raises:
            ConfigurationError: Se falhar ao obter dados
        """
        limit = limit or int(self.config.OHLCV_LIMIT)
        cache_key = (symbol, timeframe, limit)

        # Verifica se os dados estão no cache com thread safety
        import time
        current_time = time.time()
        with self._cache_lock:
            if cache_key in self._ohlcv_cache:
                if current_time - self._cache_timestamps.get(cache_key, 0) <= self._cache_ttl:
                    self.logger.debug("Dados OHLCV obtidos do cache para %s", symbol)
                    return self._ohlcv_cache[cache_key]
                else:
                    # Remove entrada expirada
                    self._cache_order.remove(cache_key)
                    del self._ohlcv_cache[cache_key]
                    del self._cache_timestamps[cache_key]
                    self.logger.debug("Entrada expirada removida ao acessar cache: %s", symbol)

        try:
            ohlcv = self.client.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            if not ohlcv:
                raise ValueError(f"Nenhum dado OHLCV disponível para {symbol}")

            self.logger.debug(
                "Dados OHLCV obtidos para %s: %d velas", symbol, len(ohlcv)
            )
            ohlcv_array = np.array(ohlcv)
            # Adiciona ao cache, mantém a ordem de inserção e registra o timestamp com thread safety
            with self._cache_lock:
                self._ohlcv_cache[cache_key] = ohlcv_array
                self._cache_order.append(cache_key)
                self._cache_timestamps[cache_key] = current_time
                
                # Remove entradas expiradas pelo TTL
                expired_keys = []
                for key in self._cache_order:
                    if current_time - self._cache_timestamps.get(key, 0) > self._cache_ttl:
                        expired_keys.append(key)
                
                for key in expired_keys:
                    self._cache_order.remove(key)
                    if key in self._ohlcv_cache:
                        del self._ohlcv_cache[key]
                        del self._cache_timestamps[key]
                        self.logger.debug("Entrada expirada removida do cache pelo TTL: %s", key)
                
                # Se o cache exceder o tamanho máximo, remove a entrada mais antiga
                if len(self._cache_order) > self._max_cache_size:
                    oldest_key = self._cache_order.pop(0)
                    if oldest_key in self._ohlcv_cache:
                        del self._ohlcv_cache[oldest_key]
                        if oldest_key in self._cache_timestamps:
                            del self._cache_timestamps[oldest_key]
                        self.logger.debug("Entrada mais antiga removida do cache: %s", oldest_key)
            
            return ohlcv_array
        except Exception as exc:
            self.logger.error("Erro ao obter OHLCV para %s: %s", symbol, str(exc))
            raise ConfigurationError(f"Falha ao obter dados para {symbol}") from exc

    def _compute_indicator(
        self, 
        symbol: str, 
        timeframe: str, 
        calc_func, 
        min_data_points: int, 
        indicator_name: str,
        limit: int = None,
        nan_handler_func=lambda x: float(x[-1]) if not np.isnan(x[-1]) else None,
        **calc_params
    ) -> Optional[float]:
        """
        Método genérico para calcular indicadores técnicos, reduzindo repetição de código.

        Args:
            symbol: Par de trading
            timeframe: Intervalo de tempo
            calc_func: Função de cálculo do indicador (ex: talib.RSI)
            min_data_points: Número mínimo de pontos de dados necessários para o cálculo
            indicator_name: Nome do indicador para logging
            limit: Número de candles
            nan_handler_func: Função para tratar valores NaN no resultado do cálculo
            **calc_params: Parâmetros adicionais para a função de cálculo

        Returns:
            Valor do indicador ou None se inválido
        """
        try:
            ohlcv_data = self._fetch_ohlcv_data(symbol, timeframe, limit)
            closes = ohlcv_data[:, 4]

            if len(closes) < min_data_points:
                self.logger.warning(
                    "Dados insuficientes para cálculo de %s: %d", indicator_name, len(closes)
                )
                return None

            result = calc_func(closes, **calc_params)
            formatted_result = nan_handler_func(result)

            if formatted_result is None:
                self.logger.warning("Cálculo de %s retornou NaN para %s", indicator_name, symbol)
                return None

            self.logger.debug("%s calculado para %s: %.2f", indicator_name, symbol, formatted_result)
            return formatted_result
        except Exception as exc:
            self.logger.error("Erro ao calcular %s para %s: %s", indicator_name, symbol, str(exc))
            return None

    def calculate_rsi(
        self, symbol: str, timeframe: str = "1h", period: int = None, limit: int = None
    ) -> Optional[float]:
        """
        Calcula RSI com validação robusta.

        Args:
            symbol: Par de trading
            timeframe: Intervalo de tempo
            period: Período para RSI
            limit: Número de candles

        Returns:
            Valor do RSI ou None se inválido
        """
        period = period or self.config.RSI_PERIOD
        return self._compute_indicator(
            symbol=symbol,
            timeframe=timeframe,
            calc_func=talib.RSI,
            min_data_points=period + 1,
            indicator_name="RSI",
            limit=limit,
            timeperiod=period
        )

    def calculate_macd(
        self,
        symbol: str,
        timeframe: str = "1h",
        fast_period: int = None,
        slow_period: int = None,
        signal_period: int = None,
        limit: int = None,
    ) -> Tuple[Optional[float], Optional[float], Optional[float]]:
        """
        Calcula MACD com validação robusta.

        Args:
            symbol: Par de trading
            timeframe: Intervalo de tempo
            fast_period: Período rápido
            slow_period: Período lento
            signal_period: Período do sinal
            limit: Número de candles

        Returns:
            Tupla (MACD, Signal, Histogram) ou (None, None, None) se inválido
        """
        fast_period = fast_period or self.config.MACD_FAST
        slow_period = slow_period or self.config.MACD_SLOW
        signal_period = signal_period or self.config.MACD_SIGNAL

        try:
            ohlcv_data = self._fetch_ohlcv_data(symbol, timeframe, limit)
            closes = ohlcv_data[:, 4]

            if len(closes) < slow_period + signal_period:
                self.logger.warning(
                    "Dados insuficientes para cálculo de MACD: %d", len(closes)
                )
                return None, None, None

            macd, signal, hist = talib.MACD(
                closes,
                fastperiod=fast_period,
                slowperiod=slow_period,
                signalperiod=signal_period,
            )
            latest_values = (macd[-1], signal[-1], hist[-1])

            if any(np.isnan(val) for val in latest_values):
                self.logger.warning("Cálculo de MACD retornou NaN para %s", symbol)
                return None, None, None

            self.logger.debug("MACD calculado para %s", symbol)
            return tuple(float(val) for val in latest_values)
        except Exception as exc:
            self.logger.error("Erro ao calcular MACD para %s: %s", symbol, str(exc))
            return None, None, None

    def calculate_adx(
        self, symbol: str, timeframe: str = "1h", period: int = None, limit: int = None
    ) -> Optional[float]:
        """
        Calcula ADX com validação robusta.

        Args:
            symbol: Par de trading
            timeframe: Intervalo de tempo
            period: Período para ADX
            limit: Número de candles

        Returns:
            Valor do ADX ou None se inválido
        """
        period = period or self.config.ADX_PERIOD
        try:
            ohlcv_data = self._fetch_ohlcv_data(symbol, timeframe, limit)
            highs = ohlcv_data[:, 2]
            lows = ohlcv_data[:, 3]
            closes = ohlcv_data[:, 4]

            if len(closes) < period * 2:
                self.logger.warning(
                    "Dados insuficientes para cálculo de ADX: %d", len(closes)
                )
                return None

            adx_values = talib.ADX(highs, lows, closes, timeperiod=period)
            latest_adx = adx_values[-1]

            if np.isnan(latest_adx):
                self.logger.warning("Cálculo de ADX retornou NaN para %s", symbol)
                return None

            self.logger.debug("ADX calculado para %s: %.2f", symbol, latest_adx)
            return float(latest_adx)
        except Exception as exc:
            self.logger.error("Erro ao calcular ADX para %s: %s", symbol, str(exc))
            return None

    def calculate_wma(
        self, symbol: str, timeframe: str = "1h", period: int = None, limit: int = None
    ) -> Optional[float]:
        """
        Calcula WMA (Weighted Moving Average) com validação robusta.

        Args:
            symbol: Par de trading
            timeframe: Intervalo de tempo
            period: Período para WMA
            limit: Número de candles

        Returns:
            Valor da WMA ou None se inválido
        """
        period = period or self.config.WMA_PERIOD
        return self._compute_indicator(
            symbol=symbol,
            timeframe=timeframe,
            calc_func=talib.WMA,
            min_data_points=period,
            indicator_name="WMA",
            limit=limit,
            timeperiod=period
        )


    def calculate_vwma(
        self, symbol: str, timeframe: str = "1h", period: int = None, limit: int = None
    ) -> Optional[float]:
        """
        Calcula VWMA (Volume Weighted Moving Average) de forma robusta.
    
        Args:
            symbol: Par de trading
            timeframe: Intervalo de tempo
            period: Período para VWMA
            limit: Número de candles
    
        Returns:
            Valor da VWMA ou None se inválido
        """
        period = period or self.config.WMA_PERIOD
        try:
            ohlcv_data = self._fetch_ohlcv_data(symbol, timeframe, limit)
            closes = ohlcv_data[:, 4]
            volumes = ohlcv_data[:, 5]
    
            if len(closes) < period or len(volumes) < period:
                self.logger.warning(
                    "Dados insuficientes para cálculo de VWMA: %d candles", len(closes)
                )
                return None
    
            # Seleciona apenas os últimos 'period' candles
            recent_closes = closes[-period:]
            recent_volumes = volumes[-period:]
    
            # Substitui volumes inválidos (<=0) por NaN para ignorar no cálculo
            valid_mask = recent_volumes > 0
            if not np.any(valid_mask):
                self.logger.warning("Todos os volumes são zero ou negativos para %s", symbol)
                return None
    
            weighted_prices = recent_closes[valid_mask] * recent_volumes[valid_mask]
            vwma = np.sum(weighted_prices) / np.sum(recent_volumes[valid_mask])
    
            if np.isnan(vwma):
                self.logger.warning("Cálculo de VWMA retornou NaN para %s", symbol)
                return None
    
            self.logger.debug("VWMA calculado para %s: %.2f", symbol, vwma)
            return float(vwma)
    
        except Exception as exc:
            self.logger.error("Erro ao calcular VWMA para %s: %s", symbol, str(exc))
            return None

    def calculate_sma(
        self, symbol: str, timeframe: str = "1h", period: int = None, limit: int = None
    ) -> Optional[float]:
        """
        Calcula SMA (Simple Moving Average) com validação robusta.

        Args:
            symbol: Par de trading
            timeframe: Intervalo de tempo
            period: Período para SMA
            limit: Número de candles

        Returns:
            Valor da SMA ou None se inválido
        """
        period = period or self.config.WMA_PERIOD
        return self._compute_indicator(
            symbol=symbol,
            timeframe=timeframe,
            calc_func=talib.SMA,
            min_data_points=period,
            indicator_name="SMA",
            limit=limit,
            timeperiod=period
        )

    def calculate_sma_rsi(
        self, symbol: str, timeframe: str = "1h", period: int = None, limit: int = None
    ) -> Optional[float]:
        """
        Calcula SMA (Simple Moving Average) do RSI com validação robusta.

        Args:
            symbol: Par de trading
            timeframe: Intervalo de tempo
            period: Período para SMA do RSI
            limit: Número de candles

        Returns:
            Valor da SMA do RSI ou None se inválido
        """
        period = period or self.config.WMA_PERIOD
        try:
            ohlcv_data = self._fetch_ohlcv_data(symbol, timeframe, limit)
            closes = ohlcv_data[:, 4]

            if len(closes) < period + 14:  # 14 é o período padrão para RSI
                self.logger.warning(
                    "Dados insuficientes para cálculo de SMA do RSI: %d", len(closes)
                )
                return None

            rsi_values = talib.RSI(closes, timeperiod=14)
            sma_rsi_values = talib.SMA(rsi_values, timeperiod=period)
            latest_sma_rsi = sma_rsi_values[-1]

            if np.isnan(latest_sma_rsi):
                self.logger.warning("Cálculo de SMA do RSI retornou NaN para %s", symbol)
                return None

            self.logger.debug("SMA do RSI calculado para %s: %.2f", symbol, latest_sma_rsi)
            return float(latest_sma_rsi)
        except Exception as exc:
            self.logger.error("Erro ao calcular SMA do RSI para %s: %s", symbol, str(exc))
            return None

    def calculate_atr(
        self, symbol: str, timeframe: str = "1h", period: int = 14, limit: int = None
    ) -> Optional[float]:
        """
        Calcula ATR (Average True Range) para medir a volatilidade.

        Args:
            symbol: Par de trading
            timeframe: Intervalo de tempo
            period: Período para ATR
            limit: Número de candles

        Returns:
            Valor do ATR ou None se inválido
        """
        try:
            ohlcv_data = self._fetch_ohlcv_data(symbol, timeframe, limit)
            highs = ohlcv_data[:, 2]
            lows = ohlcv_data[:, 3]
            closes = ohlcv_data[:, 4]

            if len(closes) < period:
                self.logger.warning(
                    "Dados insuficientes para cálculo de ATR: %d", len(closes)
                )
                return None

            atr_values = talib.ATR(highs, lows, closes, timeperiod=period)
            latest_atr = atr_values[-1]

            if np.isnan(latest_atr):
                self.logger.warning("Cálculo de ATR retornou NaN para %s", symbol)
                return None

            self.logger.debug("ATR calculado para %s: %.2f", symbol, latest_atr)
            return float(latest_atr)
        except Exception as exc:
            self.logger.error("Erro ao calcular ATR para %s: %s", symbol, str(exc))
            return None

    def calculate_chop(
        self, symbol: str, timeframe: str = "1h", period: int = 14, limit: int = None
    ) -> Optional[float]:
        """
        Calcula CHOP (Choppiness Index) para detectar mercados laterais.

        Args:
            symbol: Par de trading
            timeframe: Intervalo de tempo
            period: Período para CHOP
            limit: Número de candles

        Returns:
            Valor do CHOP ou None se inválido
        """
        try:
            ohlcv_data = self._fetch_ohlcv_data(symbol, timeframe, limit)
            highs = ohlcv_data[:, 2]
            lows = ohlcv_data[:, 3]
            closes = ohlcv_data[:, 4]

            if len(closes) < period:
                self.logger.warning(
                    "Dados insuficientes para cálculo de CHOP: %d", len(closes)
                )
                return None

            # Calcula True Range
            high_low = highs - lows
            high_close = np.abs(highs - np.roll(closes, 1))
            low_close = np.abs(lows - np.roll(closes, 1))
            high_close[0] = 0  # Ajuste para o primeiro valor após o shift
            low_close[0] = 0
            true_range = np.maximum.reduce([high_low, high_close, low_close])

            # Calcula ATR (Average True Range)
            atr = np.zeros_like(true_range)
            atr[period - 1] = np.mean(true_range[:period])
            for i in range(period, len(true_range)):
                atr[i] = ((atr[i - 1] * (period - 1)) + true_range[i]) / period

            # Calcula soma do range alto-baixo
            sum_hl = np.zeros_like(high_low)
            for i in range(period - 1, len(high_low)):
                sum_hl[i] = np.sum(high_low[i - period + 1 : i + 1])

            # Calcula CHOP
            chop = np.zeros_like(closes)
            for i in range(period - 1, len(closes)):
                if atr[i] > 0:  # Evita divisão por zero
                    chop[i] = 100 * np.log10(sum_hl[i] / atr[i]) / np.log10(period)
                else:
                    chop[i] = 0

            latest_chop = chop[-1]
            if np.isnan(latest_chop):
                self.logger.warning("Cálculo de CHOP retornou NaN para %s", symbol)
                return None

            self.logger.debug("CHOP calculado para %s: %.2f", symbol, latest_chop)
            return float(latest_chop)
        except Exception as exc:
            self.logger.error("Erro ao calcular CHOP para %s: %s", symbol, str(exc))
            return None

    def calculate_vwap(
        self, symbol: str, timeframe: str = "1h", limit: int = None
    ) -> float:
        """
        Calcula a VWAP (Volume Weighted Average Price) para o símbolo e timeframe.
        """
        ohlcv = self._fetch_ohlcv_data(symbol, timeframe, limit)
        if ohlcv.shape[0] == 0:
            return None

        typical_price = (
            ohlcv[:, 2] + ohlcv[:, 3] + ohlcv[:, 4]
        ) / 3  # (high+low+close)/3
        volume = ohlcv[:, 5]
        vwap = np.sum(typical_price * volume) / np.sum(volume)
        return float(vwap)

    # RSI e RSI Anterior e tendência de RSI

    def get_rsi_and_prev(
        self, symbol: str, timeframe: str = "1h", period: int = None, limit: int = None
    ) -> tuple:
        """
        Retorna o RSI mais recente e o anterior.
        """
        period = period or self.config.RSI_PERIOD
        ohlcv_data = self._fetch_ohlcv_data(symbol, timeframe, limit)
        closes = ohlcv_data[:, 4]

        if len(closes) < period + 2:
            return None, None

        rsi_values = talib.RSI(closes, timeperiod=period)
        return float(rsi_values[-1]), float(rsi_values[-2])

    def fetch_historical_data(
        self, symbol: str, timeframe: str, days: int = 7
    ) -> np.ndarray:
        """
        Obtem dados históricos da exchange, para os últimos 7 dias.

        Args:
            symbol: Par de trading
            timeframe: Intervalo de tempo
            days: Número de dias para obter

        Returns:
            Array numpy com dados OHLCV
        """
        limit = 150
        timeframe_ms = self.client.exchange.parse_timeframe(timeframe) * 1000
        since = self.client.exchange.milliseconds() - days * 24 * 60 * 60 * 1000

        all_ohlcv = []
        while since < self.client.exchange.milliseconds():
            ohlcv = self.client.exchange.fetch_ohlcv(
                symbol, timeframe, since=since, limit=limit
            )
            if not ohlcv:
                break
            all_ohlcv.extend(ohlcv)
            since = ohlcv[-1][0] + timeframe_ms  # Update since to the last timestamp

        return np.array(all_ohlcv)
