import os
import ccxt
import pandas as pd
from dotenv import load_dotenv
from datetime import datetime, timedelta
import time
from typing import Dict, Tuple, Union, Optional, Any
from pathlib import Path
import sys
import logging
from tqdm import tqdm

FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]
BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(BASE_DIR))


logger = logging.getLogger(__name__)

load_dotenv()

TIMEFRAME = "1h"
SYMBOL = "CRO-USDC"
TOTAL_CANDLES = 70080

# 1 ano horas  = 8760
# 1 ano em 15m = 35040
# 1 ano em 5m  = 105120
# 2 ano horas  = 178520
# 2 ano em 5m  = 210240
# 2 ano em 15m = 70080

def connect_okx():
    # Obtém credenciais do .env
    api_key = os.getenv('live_okx_apiKey')
    secret_key = os.getenv('live_okx_secret')
    passphrase = os.getenv('live_okx_password')

    # Configura a exchange OKX
    exchange = ccxt.myokx({
        'apiKey': api_key,
        'secret': secret_key,
        'password': passphrase,
        'enableRateLimit': True,
    })
    return exchange


def fetch_data_pagination(
    symbol: str, timeframe: str, total_candles: int = 20000, save_csv: bool = False
) -> pd.DataFrame:
    """
    Busca dados OHLCV de um par de trading na OKX com paginação para obter mais candles.
    :param symbol: Par de trading (ex: "CRO-USDC")
    :param timeframe: Timeframe dos candles (ex: "15m")
    :param total_candles: Número total de candles desejado
    :return: DataFrame com os dados OHLCV
    """

    # Inicializar a exchange OKX com credenciais usando a função connect_okx
    try:
        okx = connect_okx()
        logger.info(f"Conectado à exchange: OKX")
    except Exception as e:
        logger.error(f"Falha ao inicializar a exchange: {e}")
        return pd.DataFrame()

    # Calcular número de requisições necessárias
    limit_per_request = 300
    num_requests = (total_candles + limit_per_request - 1) // limit_per_request

    all_data = []
    last_timestamp = None

    try:
        # Initialize progress bar
        with tqdm(total=num_requests, desc="\033[94mObtendo data\033[0m", bar_format="{l_bar}\033[94m{bar}\033[0m|") as pbar:
            for i in range(num_requests):
                # Para a primeira requisição, não precisamos de since
                if i == 0:
                    data = okx.fetch_ohlcv(symbol, timeframe, limit=limit_per_request)
                else:
                    # Usar o timestamp do primeiro candle da última requisição como referência
                    # Make sure last_timestamp is not None before subtraction
                    if last_timestamp is not None:
                        since = last_timestamp - (
                            limit_per_request * timeframe_to_milliseconds(timeframe)
                        )
                    else:
                        # If last_timestamp is None, use a default approach
                        since = int(time.time() * 1000) - (
                            limit_per_request * timeframe_to_milliseconds(timeframe)
                        )
                    data = okx.fetch_ohlcv(
                        symbol, timeframe, since=since, limit=limit_per_request
                    )

                if not data:
                    break

                all_data.extend(data)

                # Guardar o timestamp do primeiro candle desta requisição
                last_timestamp = data[0][0]

                # Update progress bar
                pbar.update(1)

                # Pequena pausa para respeitar rate limits
                time.sleep(okx.rateLimit / 1000)  # Converter milissegundos para segundos

        # Criar DataFrame com todos os dados
        df = pd.DataFrame(
            all_data, columns=["timestamp", "open", "high", "low", "close", "volume"]
        )

        # Remover duplicatas baseado no timestamp
        df = df.drop_duplicates(subset="timestamp")

        # Ordenar por timestamp em ordem crescente
        df = df.sort_values("timestamp")

        # Limitar ao número de candles desejado
        df = df.tail(total_candles)

        # Converter timestamp para datetime
        df["timestamp"] = pd.to_datetime(df["timestamp"], unit="ms")

        # Renomear colunas para minúsculas
        df.columns = [col.lower() for col in df.columns]

        # Salvar CSV se necessário
        if save_csv:
            csv_filename = f"data/data_{symbol.replace('/', '_')}_{timeframe}.csv"
            os.makedirs("data", exist_ok=True)
            df.to_csv(csv_filename, index=False)
            print(f"Dados salvos em: {csv_filename}")

        return df

    except ccxt.NetworkError as e:
        print(f"Erro de rede ao buscar dados: {e}")
    except ccxt.ExchangeError as e:
        print(f"Erro da exchange: {e}")
    except Exception as e:
        print(f"Erro inesperado: {e}")

    return pd.DataFrame()  # Retorna DataFrame vazio em caso de erro


def timeframe_to_milliseconds(timeframe: str) -> int:
    """
    Converte timeframe para milissegundos.

    :param timeframe: String do timeframe (ex: "5m", "1h", "1d")
    :return: Milissegundos equivalentes
    """
    unit = timeframe[-1]
    number = int(timeframe[:-1])

    multipliers = {
        "m": 60 * 1000,  # minutos para ms
        "h": 60 * 60 * 1000,  # horas para ms
        "d": 24 * 60 * 60 * 1000,  # dias para ms
    }

    return number * multipliers.get(unit, 0)


if __name__ == "__main__":
    # Exemplo de uso
    df_data = fetch_data_pagination(SYMBOL, TIMEFRAME, TOTAL_CANDLES, save_csv=True)
    print(f"\nConcluído com sucesso! Obtidos {len(df_data)} candles.\n")
