"""
Bot de trading automatizado para OKX Exchange.
"""

import warnings

warnings.filterwarnings("ignore", message="pkg_resources is deprecated as an API")

import asyncio
import os
import shutil
import numpy as np
import json
import ccxt
import talib
import pandas as pd
import signal
import uuid
import time
from dataclasses import dataclass
from decimal import Decimal, ROUND_HALF_UP
from dotenv import load_dotenv
from indicators.indicators import TechnicalIndicator
from service.service_alerts import SoundNotifications
from service.service_telegram import TelegramNotifier
from typing import Any, Dict, List, Optional, Tuple
from utils.logger import TradingLogger
from utils.database import TradingDatabase
from utils.metrics import MetricsCalculator
from utils.exceptions import ConfigurationError, ExchangeConnectionError
from utils.check_orders import (
    check_regular_orders,
    display_regular_orders,
    check_oco_orders,
    display_oco_orders,
    check_trailing_stop_orders,
    display_trailing_stop_orders,
)
from utils.error_handler import <PERSON>rror<PERSON>and<PERSON>
from utils.order_validator import OrderValidator
from tabulate import tabulate
from signals.swing import SwingSignalGenerator
import json


load_dotenv()
FIAT_CURRENCIES = ["EUR", "USD"]


def load_parameters():
    """Carrega os parâmetros do arquivo parameters.json."""
    with open("parameters.json", "r") as f:
        return json.load(f)


@dataclass
class BotConfig:
    """Configurações centralizadas para o bot de trading."""

    params = load_parameters()

    SANDBOX_MODE: bool = True

    # Ajustar símbolos de trading para modo SANDBOX, se necessário
    TRADING_SYMBOLS = params["swing"]["TRADING_SYMBOLS"]
    TIMEFRAME = params["swing"]["TIMEFRAME"]
    RSI_PERIOD: int = params.get("swing", {}).get("RSI_PERIOD", 22)
    WMA_PERIOD: int = params.get("swing", {}).get("WMA_PERIOD", 84)
    OHLCV_LIMIT: int = params.get("swing", {}).get("OHLCV_LIMIT", 300)
    SWING_BARS: int = 3
    SWING_BARS2: int = 3
    CLUSTER_BARS: int = 20
    NUM_CLUSTERS: int = 3
    ADX_PERIOD: int = 14

    SL_MULTIPLIER = 1.55
    ATR_MULTIPLIER: float = 1.5
    # Configurações para gestão dinâmica de posição baseada em volatilidade
    ENABLE_DYNAMIC_POSITION_SIZING: bool = True
    ENABLE_DYNAMIC_TRAILING_RATIO: bool = True
    POSITION_SIZE_BASE: float = 0.1  # 10% do saldo por padrão
    POSITION_SIZE_MIN: float = 0.05  # 5% mínimo em alta volatilidade
    POSITION_SIZE_MAX: float = 0.15  # 15% máximo em baixa volatilidade
    CALLBACK_RATIO_BASE: float = 0.03  # 3% por padrão para trailing stop
    CALLBACK_RATIO_MIN: float = 0.015  # 1.5% mínimo em baixa volatilidade
    CALLBACK_RATIO_MAX: float = 0.08  # 8% máximo em alta volatilidade
    ATR_LOOKBACK_PERIODS: int = 20  # Períodos para média histórica de ATR
    VOLATILITY_THRESHOLD_HIGH: float = (
        1.2  # Fator para alta volatilidade (ATR atual / média)
    )
    VOLATILITY_THRESHOLD_LOW: float = (
        0.8  # Fator para baixa volatilidade (ATR atual / média)
    )
    STABLECOINS: List[str] = None
    FIAT_DECIMAL_PLACES: int = 2
    CRYPTO_DECIMAL_PLACES: int = 8
    ENABLE_SOUND_NOTIFICATIONS: bool = True
    ENABLE_TELEGRAM_NOTIFICATIONS: bool = True
    SCRIPT_NAME: str = os.path.splitext(os.path.basename(__file__))[0].replace("_", " ")

    def __post_init__(self):
        if self.TRADING_SYMBOLS is None:
            self.TRADING_SYMBOLS = BotConfig.SYMBOLS
        if self.STABLECOINS is None:
            self.STABLECOINS = ["USDC", "USDT", "BUSD", "FDUSD"]
        self._initialize_defaults()

    def _initialize_defaults(self):
        """Inicializa valores padrão de OKXClient para atributos não definidos."""
        from core.okx_client import OKXClient

        default_config = OKXClient().config
        # Obtém todos os atributos disponíveis no DefaultConfig
        default_attrs = [attr for attr in dir(default_config) if attr.isupper()]
        for attr in default_attrs:
            if (
                not hasattr(self, attr)
                or getattr(self, attr) is None
                or (
                    isinstance(getattr(self, attr), (int, float))
                    and getattr(self, attr) == 0
                )
            ):
                default_val = getattr(default_config, attr, None)
                if default_val is not None:
                    setattr(self, attr, default_val)
        # Inicializa o cache de ordens
        self.cache_duration = 60  # Duração em segundos antes de atualizar o cache
        self.open_orders: Dict[str, List[Dict]] = {}
        self.closed_orders: Dict[str, List[Dict]] = {}
        self.last_updated: Dict[str, float] = {}

    def get_open_orders(self, symbol: str) -> Optional[List[Dict]]:
        """
        Obtém ordens abertas do cache se estiverem atualizadas.

        Args:
            symbol: Símbolo do par de trading.

        Returns:
            Lista de ordens abertas ou None se o cache estiver desatualizado.
        """
        if symbol in self.open_orders and symbol in self.last_updated:
            if time.time() - self.last_updated[symbol] < self.cache_duration:
                return self.open_orders[symbol]
        return None

    def get_closed_orders(self, symbol: str) -> Optional[List[Dict]]:
        """
        Obtém ordens fechadas do cache se estiverem atualizadas.

        Args:
            symbol: Símbolo do par de trading.

        Returns:
            Lista de ordens fechadas ou None se o cache estiver desatualizado.
        """
        if symbol in self.closed_orders and symbol in self.last_updated:
            if time.time() - self.last_updated[symbol] < self.cache_duration:
                return self.closed_orders[symbol]
        return None

    def update_orders(
        self, symbol: str, open_orders: List[Dict], closed_orders: List[Dict]
    ) -> None:
        """
        Atualiza o cache com novas ordens.

        Args:
            symbol: Símbolo do par de trading.
            open_orders: Lista de ordens abertas.
            closed_orders: Lista de ordens fechadas.
        """
        self.open_orders[symbol] = open_orders
        self.closed_orders[symbol] = closed_orders
        self.last_updated[symbol] = time.time()

    @classmethod
    def load_config(cls, file_path: str = "config.json") -> "BotConfig":
        """Carrega a configuração de um arquivo JSON."""
        try:
            with open(file_path, "r") as f:
                config_data = json.load(f)
            return cls(**config_data)
        except Exception as e:
            print(f"Falha ao carregar configuração: {e}")
            return cls()


from core.okx_client import OKXClient


class OKXOrder:
    _instance = None

    def __new__(cls, client: OKXClient = None):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, client: OKXClient = None):
        if getattr(self, "_initialized", False):
            return
        self._initialized = True
        self.client = client or OKXClient()
        self.logger = TradingLogger.get_logger(__name__)
        self.validator = OrderValidator(self.client)

    def validate_order_conditions(self, symbol: str) -> bool:
        """
        Valida se as condições estão adequadas para colocar uma ordem.

        Args:
            symbol: Par de trading

        Returns:
            True se condições estão OK, False caso contrário
        """
        return self.validator.validate_order_conditions(
            symbol, strategy_type="trailing"
        )

    def _generate_client_order_id(self) -> str:
        return uuid.uuid4().hex[:32]

    def has_active_trailing_stop(self, symbol: str) -> bool:
        try:
            trailing_orders = check_trailing_stop_orders(self.client, symbol)
            active_count = len(trailing_orders)
            if active_count > 0:
                # Removed logging of active trailing stop orders as per user request
                for order in trailing_orders:
                    order_id = order.get("id", "N/A")
                    status = order.get("status", "N/A")
                    self.logger.debug(
                        "Trailing stop ativo: ID=%s, Status=%s", order_id, status
                    )
                return True
            return False
        except Exception as exc:
            self.logger.error(
                "Erro ao verificar trailing stops para %s: %s", symbol, str(exc)
            )
            return True

    async def wait_for_order_execution(
        self, order_id: str, symbol: str, timeout: int = 60, max_retries: int = 3
    ) -> Optional[Dict]:
        start_time = time.time()
        retries = 0
        while time.time() - start_time < timeout:
            try:
                order = self.client.exchange.fetch_order(order_id, symbol)
                if order["status"] == "closed":
                    self.logger.info(f"Ordem {order_id} executada com sucesso.")
                    return order
                elif order["status"] == "canceled":
                    self.logger.warning(f"Ordem {order_id} foi cancelada.")
                    return None
                await asyncio.sleep(2)  # Aumentar o intervalo entre tentativas
            except Exception as exc:
                retries += 1
                self.logger.error(
                    f"Erro ao verificar status da ordem {order_id} (Tentativa {retries}/{max_retries}): {exc}"
                )
                if retries >= max_retries:
                    self.logger.error(
                        f"Limite de tentativas atingido para a ordem {order_id}. Abortando."
                    )
                    return None
                await asyncio.sleep(3)  # Aguardar mais tempo antes de nova tentativa
        self.logger.warning(
            f"Tempo esgotado ao esperar pela execução da ordem {order_id}."
        )
        return None

    async def place_buy_order_with_trailing_stop(
        self, symbol: str, indicator: TechnicalIndicator, timeframe: str = "15m"
    ) -> Optional[Dict]:
        try:
            if check_trailing_stop_orders(self.client, symbol):
                self.logger.info(
                    "Já existem ordens abertas para %s, pulando criação", symbol
                )
                return None

            best_bid = self.client.get_best_bid(symbol)
            if not best_bid:
                self.logger.error("Não foi possível obter melhor bid para %s", symbol)
                return None
            entry_price = best_bid
            self.logger.info("Melhor bid obtido para %s: %s", symbol, entry_price)
            balance = self.client.get_balance()
            quote_currency = symbol.split("/")[1]
            available_balance = balance["total"].get(quote_currency, 0)
            if available_balance <= 0:
                self.logger.error(
                    "Saldo insuficiente em %s para %s", quote_currency, symbol
                )
                return None
            # Ajuste dinâmico do tamanho da posição baseado em volatilidade
            position_size = 0.1  # Valor padrão
            if (
                hasattr(self.client, "config")
                and hasattr(self.client.config, "ENABLE_DYNAMIC_POSITION_SIZING")
                and self.client.config.ENABLE_DYNAMIC_POSITION_SIZING
            ):
                atr = indicator.calculate_atr(symbol, timeframe, period=14)
                if atr is not None:
                    # Calcular média histórica de ATR para comparação
                    ohlcv_data = indicator.fetch_historical_data(
                        symbol,
                        timeframe,
                        limit=getattr(self.client.config, "ATR_LOOKBACK_PERIODS", 20)
                        + 14,
                    )
                    if ohlcv_data and len(ohlcv_data) > 14:
                        atr_values = []
                        for i in range(len(ohlcv_data) - 14, len(ohlcv_data)):
                            high = np.array(
                                [candle[2] for candle in ohlcv_data[i - 14 : i]]
                            )
                            low = np.array(
                                [candle[3] for candle in ohlcv_data[i - 14 : i]]
                            )
                            close = np.array(
                                [candle[4] for candle in ohlcv_data[i - 14 : i]]
                            )
                            atr_val = talib.ATR(high, low, close, timeperiod=14)[-1]
                            atr_values.append(atr_val)
                        atr_mean = np.mean(atr_values) if atr_values else atr
                        volatility_ratio = atr / atr_mean if atr_mean > 0 else 1.0
                        self.logger.info(
                            "Volatilidade para %s: ATR atual=%.2f, ATR médio=%.2f, Razão=%.2f",
                            symbol,
                            atr,
                            atr_mean,
                            volatility_ratio,
                        )

                        if volatility_ratio > getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_HIGH", 1.2
                        ):
                            position_size = getattr(
                                self.client.config, "POSITION_SIZE_MIN", 0.05
                            )
                            self.logger.info(
                                "Alta volatilidade detectada para %s, reduzindo tamanho da posição para %.1f%%",
                                symbol,
                                position_size * 100,
                            )
                        elif volatility_ratio < getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_LOW", 0.8
                        ):
                            position_size = getattr(
                                self.client.config, "POSITION_SIZE_MAX", 0.15
                            )
                            self.logger.info(
                                "Baixa volatilidade detectada para %s, aumentando tamanho da posição para %.1f%%",
                                symbol,
                                position_size * 100,
                            )
                        else:
                            position_size = getattr(
                                self.client.config, "POSITION_SIZE_BASE", 0.1
                            )
                            self.logger.info(
                                "Volatilidade normal para %s, tamanho da posição padrão %.1f%%",
                                symbol,
                                position_size * 100,
                            )
                    else:
                        self.logger.warning(
                            "Dados históricos insuficientes para calcular média ATR de %s, usando tamanho padrão",
                            symbol,
                        )
                else:
                    self.logger.warning(
                        "Não foi possível calcular ATR para %s, usando tamanho padrão",
                        symbol,
                    )
            else:
                position_size = 0.1  # Padrão fixo se desativado

            balance_to_use = available_balance * position_size
            amount = balance_to_use / entry_price
            formatted_amount = self.client.format_amount_with_precision(symbol, amount)
            formatted_price = self.client.format_price_with_precision(
                symbol, entry_price
            )
            if float(formatted_amount) <= 0:
                self.logger.error(
                    "Quantidade calculada insuficiente para %s: %s",
                    symbol,
                    formatted_amount,
                )
                return None
            self.logger.info(
                "Calculado para %s - Quantidade: %s, Preço: %s",
                symbol,
                formatted_amount,
                formatted_price,
            )

            limit_order = self.client.exchange.create_order(
                symbol=symbol,
                type="limit",
                side="buy",
                amount=float(formatted_amount),
                price=float(formatted_price),
                params={"tdMode": "cash", "clOrdId": self._generate_client_order_id()},
            )
            if "amount" not in limit_order or limit_order["amount"] is None:
                limit_order["amount"] = float(formatted_amount)
            self.logger.info(
                "Ordem limite criada com sucesso: %s", limit_order.get("id")
            )

            # Aguardar execução da ordem limite
            executed_order = await self.wait_for_order_execution(
                limit_order["id"], symbol
            )
            if not executed_order:
                self.logger.error(
                    f"Ordem de compra {limit_order['id']} não foi executada."
                )
                return None

            entry_price_real = (
                executed_order["average"]
                if "average" in executed_order
                else executed_order["price"]
            )

            # Enviar notificação de compra via Telegram
            bot = TradingBot()  # Certifique-se de que a instância do bot está acessível
            await bot.send_telegram_notification(
                f"🚨 Ordem de COMPRA executada para {symbol}\n"
                f"Quantidade: {formatted_amount}\n"
                f"Preço: {entry_price_real}"
            )
            self.logger.info(
                "Notificação de compra enviada via Telegram para %s", symbol
            )

            # Calcular callback ratio com 1.75 * ATR
            atr = indicator.calculate_atr(symbol, timeframe, period=14)
            if atr is None:
                self.logger.error("Não foi possível calcular ATR para %s", symbol)
                return None

            # Ajuste dinâmico do callback_ratio baseado em volatilidade
            callback_ratio_base = (1.75 * atr) / entry_price_real
            if (
                hasattr(self.client, "config")
                and hasattr(self.client.config, "ENABLE_DYNAMIC_TRAILING_RATIO")
                and self.client.config.ENABLE_DYNAMIC_TRAILING_RATIO
            ):
                if atr is not None:
                    # Calcular média histórica de ATR para comparação
                    ohlcv_data = indicator.fetch_historical_data(
                        symbol,
                        timeframe,
                        limit=getattr(self.client.config, "ATR_LOOKBACK_PERIODS", 20)
                        + 14,
                    )
                    if ohlcv_data and len(ohlcv_data) > 14:
                        atr_values = []
                        for i in range(len(ohlcv_data) - 14, len(ohlcv_data)):
                            high = np.array(
                                [candle[2] for candle in ohlcv_data[i - 14 : i]]
                            )
                            low = np.array(
                                [candle[3] for candle in ohlcv_data[i - 14 : i]]
                            )
                            close = np.array(
                                [candle[4] for candle in ohlcv_data[i - 14 : i]]
                            )
                            atr_val = talib.ATR(high, low, close, timeperiod=14)[-1]
                            atr_values.append(atr_val)
                        atr_mean = np.mean(atr_values) if atr_values else atr
                        volatility_ratio = atr / atr_mean if atr_mean > 0 else 1.0

                        if volatility_ratio > getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_HIGH", 1.2
                        ):
                            callback_ratio = max(
                                getattr(self.client.config, "CALLBACK_RATIO_MAX", 0.08),
                                callback_ratio_base,
                            )
                            self.logger.info(
                                "Alta volatilidade detectada para %s, aumentando callback ratio para %.2f%%",
                                symbol,
                                callback_ratio * 100,
                            )
                        elif volatility_ratio < getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_LOW", 0.8
                        ):
                            callback_ratio = min(
                                getattr(
                                    self.client.config, "CALLBACK_RATIO_MIN", 0.015
                                ),
                                callback_ratio_base,
                            )
                            self.logger.info(
                                "Baixa volatilidade detectada para %s, reduzindo callback ratio para %.2f%%",
                                symbol,
                                callback_ratio * 100,
                            )
                        else:
                            callback_ratio = max(
                                getattr(
                                    self.client.config, "CALLBACK_RATIO_BASE", 0.03
                                ),
                                min(
                                    callback_ratio_base,
                                    getattr(
                                        self.client.config, "CALLBACK_RATIO_MAX", 0.08
                                    ),
                                ),
                            )
                            self.logger.info(
                                "Volatilidade normal para %s, callback ratio padrão %.2f%%",
                                symbol,
                                callback_ratio * 100,
                            )
                    else:
                        self.logger.warning(
                            "Dados históricos insuficientes para calcular média ATR de %s, usando callback ratio base",
                            symbol,
                        )
                        callback_ratio = max(0.015, min(0.08, callback_ratio_base))
                else:
                    self.logger.warning(
                        "Não foi possível calcular ATR para %s, usando limites padrão para callback ratio",
                        symbol,
                    )
                    callback_ratio = max(0.015, min(0.08, callback_ratio_base))
            else:
                callback_ratio = max(
                    0.015, min(0.08, callback_ratio_base)
                )  # Padrão fixo se desativado

            trailing_params = {
                "instId": self.client.exchange.markets[symbol]["id"],
                "tdMode": "cash",
                "side": "sell",
                "ordType": "move_order_stop",
                "sz": formatted_amount,
                "callbackRatio": str(callback_ratio),
                "activePx": "",
                "clOrdId": self._generate_client_order_id(),
            }

            trailing_order = self.client.exchange.private_post_trade_order_algo(
                trailing_params
            )

            if trailing_order and trailing_order.get("code") == "0":
                algo_id = trailing_order.get("data", [{}])[0].get("algoId", "N/A")
                self.logger.info("Trailing stop criado com sucesso: %s", algo_id)
                # Salvar o trailing stop no banco de dados
                trailing_stop_data = {
                    "algo_id": algo_id,
                    "order_id": limit_order.get("id"),
                    "symbol": symbol,
                    "callback_ratio": callback_ratio,
                    "status": "active",
                }
                bot = (
                    TradingBot()
                )  # Certifique-se de que a instância do bot está acessível
                bot.db.save_trailing_stop(trailing_stop_data)
                # Enviar notificação de trailing stop via Telegram
                await bot.send_telegram_notification(
                    f"🛑 Trailing Stop criado para {symbol}\n"
                    f"Callback Ratio: {callback_ratio*100:.2f}%"
                )
                self.logger.info(
                    "Notificação de trailing stop enviada via Telegram para %s", symbol
                )
            else:
                self.logger.error("Falha ao criar trailing stop: %s", trailing_order)

        except Exception as exc:
            self.logger.error("Erro ao colocar ordem para %s: %s", symbol, str(exc))
            return None

    def display_order_status_summary(self) -> None:
        """
        Exibe um resumo consolidado do status das ordens para todos os símbolos de trading.
        """
        print("\n📊 Resumo do Status das Ordens:")
        print("=" * 50)
        for symbol in self.client.config.TRADING_SYMBOLS:
            try:
                regular_count = len(check_regular_orders(self.client, symbol))
                oco_count = len(self.check_oco_orders(symbol))
                trailing_count = len(check_trailing_stop_orders(self.client, symbol))
                total_orders = regular_count + oco_count + trailing_count

                if total_orders > 0:
                    status_emoji = "🔴" if trailing_count > 0 else "🟡"
                    print(f" {status_emoji} {symbol}:")
                    print(f"• Ordens Regulares: {regular_count}")
                    print(f"• Ordens OCO: {oco_count}")
                    print(f"• Trailing Stops: {trailing_count}")
                    print(f"• Total: {total_orders}")
                else:
                    print(f" 🟢 {symbol}: Sem ordens ativas")
            except Exception as exc:
                print(f" ❌ {symbol}: Erro ao verificar status - {exc}")
        print()

    def check_oco_orders(self, symbol: str) -> List[Dict]:
        return check_oco_orders(self.client, symbol)

    def log_order_prevention_details(self, symbol: str) -> None:
        try:
            trailing_orders = check_trailing_stop_orders(self.client, symbol)
            if trailing_orders:
                self.logger.info("=== PREVENÇÃO DE ORDEM ATIVADA ===")
                self.logger.info("Símbolo: %s", symbol)
                self.logger.info("Trailing stops ativos: %d", len(trailing_orders))
                for i, order in enumerate(trailing_orders, 1):
                    self.logger.info("Trailing Stop #%d:", i)
                    self.logger.info("  - ID: %s", order.get("id", "N/A"))
                    self.logger.info("  - Status: %s", order.get("status", "N/A"))
                    self.logger.info("  - Lado: %s", order.get("side", "N/A"))
                    self.logger.info("  - Quantidade: %s", order.get("amount", "N/A"))
                    self.logger.info(
                        "  - Trigger: %s", order.get("triggerPrice", "N/A")
                    )
                self.logger.info("Nova ordem BLOQUEADA para %s", symbol)
                self.logger.info("=====================================")
        except Exception as exc:
            self.logger.error("Erro ao registrar detalhes de prevenção: %s", str(exc))


class TradingBot:
    def __init__(self, config: Optional[BotConfig] = None):
        self.config = config or BotConfig()
        self.logger = TradingLogger.get_logger(__name__)
        self.sound_alert = self._initialize_sound_notifications()
        self.notifier = self._initialize_telegram_notifier()
        self.created_orders = []
        self.db = self._initialize_database()
        self.metrics_calculator = self._initialize_metrics_calculator()

    def _initialize_sound_notifications(self) -> Optional[SoundNotifications]:
        if self.config.ENABLE_SOUND_NOTIFICATIONS:
            try:
                return SoundNotifications()
            except Exception as exc:
                self.logger.warning(
                    "Falha ao inicializar notificações sonoras: %s", str(exc)
                )
        return None

    def _initialize_telegram_notifier(self) -> Optional[TelegramNotifier]:
        if self.config.ENABLE_TELEGRAM_NOTIFICATIONS:
            try:
                return TelegramNotifier()
            except Exception as exc:
                self.logger.warning(
                    "Falha ao inicializar notificador Telegram: %s", str(exc)
                )
        return None

    def _initialize_database(self) -> TradingDatabase:
        db_name = "sandbox_trades.db" if self.config.SANDBOX_MODE else "live_trades.db"
        db_path = os.path.join("trades", db_name)
        try:
            return TradingDatabase(db_path)
        except Exception as exc:
            self.logger.error("Falha ao inicializar banco de dados: %s", str(exc))
            raise

    def _initialize_metrics_calculator(self) -> MetricsCalculator:
        """Inicializa o calculador de métricas para análise de desempenho."""
        db_name = "sandbox_trades.db" if self.config.SANDBOX_MODE else "live_trades.db"
        db_path = os.path.join("trades", db_name)
        try:
            return MetricsCalculator(db_path)
        except Exception as exc:
            self.logger.warning(
                "Falha ao inicializar calculador de métricas: %s", str(exc)
            )
            return None

    async def play_alert(self, alert_type: str, volume: float = 0.5) -> None:
        if self.sound_alert:
            try:
                self.sound_alert.play_notification(alert_type, volume=volume)
            except Exception as exc:
                self.logger.warning("Falha ao reproduzir alerta: %s", str(exc))

    async def send_telegram_notification(self, message: str) -> None:
        if self.notifier:
            try:
                await self.notifier.send_message(message)
            except Exception as exc:
                self.logger.warning(
                    "Falha ao enviar notificação Telegram: %s", str(exc)
                )

    def save_order_to_db(self, order: Dict) -> None:
        try:
            self.db.save_order(order)
            self.logger.info(
                "Ordem salva no banco de dados: %s", order.get("id", "N/A")
            )
        except Exception as exc:
            self.logger.error("Erro ao salvar ordem no banco de dados: %s", str(exc))

    async def _calculate_all_indicators(
        self, symbol: str, indicator: TechnicalIndicator
    ) -> Dict[str, Any]:
        return {
            "rsi": indicator.calculate_rsi(symbol),
            "adx": indicator.calculate_adx(symbol),
            "wma": indicator.calculate_wma(symbol),
            "sma": indicator.calculate_sma(symbol),
            "atr": indicator.calculate_atr(symbol),
        }

    async def display_orders(self) -> None:
        print("\n📋 Ordens Recentes:")
        print("=" * 50)
        if not self.created_orders:
            print(" Nenhuma ordem criada ainda.")
            return
        for order in self.created_orders[-5:]:
            order_id = order.get("id", "N/A")
            symbol = order.get("symbol", "N/A")
            order_type = order.get("type", "N/A")
            side = order.get("side", "N/A")
            amount = order.get("amount", "N/A")
            price = order.get("price", "N/A")
            status = order.get("status", "N/A")
            print(f" ID: {order_id}")
            print(f" Símbolo: {symbol}")
            print(f" Tipo: {order_type}")
            print(f" Lado: {side}")
            print(f" Quantidade: {amount}")
            print(f" Preço: {price}")
            print(f" Estado: {status}")
            print(" " + "-" * 30)

    async def sync_orders(self, client: OKXClient, symbol: str):
        # Synchronize orders from the exchange with local database
        exchange_orders = await client.exchange.fetch_open_orders(symbol)
        local_orders = self.db.get_open_orders(symbol)
        for order in exchange_orders:
            if order["id"] not in [o["id"] for o in local_orders]:
                self.db.add_order(order)
        self.logger.info("Orders synchronized for %s", symbol)

        # Update order cache
        open_orders = [order for order in exchange_orders if order["status"] == "open"]
        closed_orders = [
            order for order in exchange_orders if order["status"] == "closed"
        ]
        client.config.update_orders(symbol, open_orders, closed_orders)
        self.logger.info("Order cache updated for %s", symbol)

        # Check for executed or canceled trailing stops and update status
        trailing_orders = check_trailing_stop_orders(client, symbol)
        for order in trailing_orders:
            algo_id = order.get("algoId", order.get("id", "N/A"))
            status = order.get("status", "N/A")
            if status in ["executed", "canceled"] and algo_id != "N/A":
                try:
                    self.db.update_trailing_stop_status(algo_id, status)
                    self.logger.info(
                        "Trailing stop status updated to %s for algo_id: %s",
                        status,
                        algo_id,
                    )
                    await self.send_telegram_notification(
                        f"🔄 Trailing Stop atualizado para {symbol}\n"
                        f"Status: {status}\n"
                        f"Algo ID: {algo_id}"
                    )
                except Exception as exc:
                    self.logger.error(
                        "Error updating trailing stop status for algo_id %s: %s",
                        algo_id,
                        str(exc),
                    )

        # Check for closed sell orders to calculate PnL
        for order in closed_orders:
            if order["side"] == "sell" and not self.db.is_order_notified(order["id"]):
                buy_order = self.db.get_matching_buy_order(order["id"], symbol)
                if buy_order:
                    buy_price = buy_order["price"]
                    sell_price = order.get("average", order["price"])
                    amount = min(buy_order["amount"], order["amount"])
                    pnl = (
                        (sell_price - buy_price) * amount
                        if buy_price and sell_price
                        else 0
                    )
                    order["pnl"] = pnl
                    self.db.save_order(order)  # Update order with PnL

                    # Save to trade history
                    trade_data = {
                        "symbol": symbol,
                        "buy_order_id": buy_order["id"],
                        "sell_order_id": order["id"],
                        "buy_price": buy_price,
                        "sell_price": sell_price,
                        "amount": amount,
                        "pnl": pnl,
                        "open_timestamp": buy_order["timestamp"],
                        "close_timestamp": order.get(
                            "timestamp", datetime.now().isoformat()
                        ),
                    }
                    with sqlite3.connect(self.db.db_path) as conn:
                        cursor = conn.cursor()
                        cursor.execute(
                            """
                            INSERT INTO trade_history 
                            (symbol, buy_order_id, sell_order_id, buy_price, sell_price, amount, pnl, open_timestamp, close_timestamp)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                            """,
                            (
                                trade_data["symbol"],
                                trade_data["buy_order_id"],
                                trade_data["sell_order_id"],
                                trade_data["buy_price"],
                                trade_data["sell_price"],
                                trade_data["amount"],
                                trade_data["pnl"],
                                trade_data["open_timestamp"],
                                trade_data["close_timestamp"],
                            ),
                        )
                        conn.commit()
                        self.logger.info(
                            "Trade history saved for sell order %s", order["id"]
                        )

                    # Send notification
                    await self.send_telegram_notification(
                        f"💰 Ordem de VENDA fechada para {symbol}\n"
                        f"Preço de Compra: {buy_price}\n"
                        f"Preço de Venda: {sell_price}\n"
                        f"Quantidade: {amount}\n"
                        f"PnL: {pnl:.2f}"
                    )
                    self.db.mark_order_as_notified(order["id"])
                    self.logger.info(
                        "PnL calculated and notified for sell order %s", order["id"]
                    )


async def initialize_bot_components() -> Tuple[OKXClient, OKXOrder, TradingBot]:
    try:
        config = BotConfig()
        client = OKXClient(config)
        # Ensure TRADING_SYMBOLS from config are used in client
        client.config.TRADING_SYMBOLS = config.TRADING_SYMBOLS
        order_manager = OKXOrder(client)
        bot = TradingBot(config)
        return client, order_manager, bot
    except Exception as exc:
        logger = TradingLogger.get_logger(__name__)
        logger.error("Falha ao inicializar componentes do bot: %s", str(exc))
        raise ConfigurationError("Falha na inicialização do bot") from exc


async def send_startup_notification(client: OKXClient, bot: TradingBot) -> None:
    if not bot.config.ENABLE_TELEGRAM_NOTIFICATIONS:
        return
    try:
        balance = client.get_balance()
        message = await build_startup_message(balance, bot.config)
        await bot.send_telegram_notification(message)
    except Exception as exc:
        logger = TradingLogger.get_logger(__name__)
        logger.error("Erro ao enviar notificação de inicialização: %s", str(exc))


async def build_startup_message(balance: Optional[Dict], config: BotConfig) -> str:
    mode_text = "🟢 LIVE TRADING" if not config.SANDBOX_MODE else "🔴 SANDBOX MODE"
    message = f"""🚀 * {config.SCRIPT_NAME} Iniciado*
-----------------------------
• *Mode*: {mode_text}
• *Balance*:
💰 Available Balance:
"""
    if balance and balance.get("total"):
        for asset, amount in balance["total"].items():
            if amount > 0:
                message += f" 🪙 {asset}: {amount:.8f}\n"
    return message


async def main() -> None:
    logger = TradingLogger.get_logger(__name__)

    def handle_sigstp(signum, frame):
        logger.info("Bot interrompido manualmente pelo usuário (SIGTSTP).")
        print("\n🛑 Bot interrompido manualmente (Ctrl+Z).")
        print("A finalizar o bot. Até breve!")
        exit(0)

    try:
        client, order_manager, bot = await initialize_bot_components()
        await send_startup_notification(client, bot)
        await bot.play_alert("start", volume=0.25)
        signal.signal(signal.SIGTSTP, handle_sigstp)
        indicator = TechnicalIndicator(client)
        # Verificar se os símbolos são válidos no modo SANDBOX
        valid_symbols = []
        for symbol in client.config.TRADING_SYMBOLS:
            try:
                # Tentar buscar informações do mercado para validar o símbolo
                market_info = client.exchange.markets.get(symbol)
                if market_info:
                    valid_symbols.append(symbol)
                    ohlcv_data = indicator.fetch_historical_data(
                        symbol, client.config.TIMEFRAME
                    )
                    # logger.info("Símbolo %s validado com sucesso.", symbol)
            except Exception as exc:
                logger.debug("Erro ao buscar dados para %s: %s", symbol, str(exc))
                logger.info("O símbolo %s não existe na exchange.", symbol)
        if not valid_symbols:
            logger.error(
                "Nenhum símbolo válido encontrado para trading no modo SANDBOX."
            )
            print(
                "❌ Erro: Nenhum símbolo válido encontrado. Verifique a configuração."
            )
            return
        client.config.TRADING_SYMBOLS = valid_symbols
        cycle_count = 0
        while True:
            cycle_count += 1
            if cycle_count == 1:
                client._print_mode_message()
            print(
                f"\n🔄 Ciclo #{cycle_count} - {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
            client.display_prices_with_precision()
            client.display_balance_with_precision()
            # Commented out display of order tables as per user request
            # await bot.display_orders()
            # display_regular_orders(client)
            # display_trailing_stop_orders(client)

            print("\n🤖 Verificando Sinais de Trading:")
            print("=" * 50)
            checker = SwingSignalGenerator(indicator, client)
            for symbol in client.config.TRADING_SYMBOLS:
                try:
                    has_buy_signal = checker.check_entry_signal(symbol, timeframe=None)
                    checker.print_signal(symbol, timeframe=None)
                    if order_manager.has_active_trailing_stop(symbol):
                        print(
                            f"⏳ {symbol}: Trailing stop ativo detectado. Aguardando fechamento..."
                        )
                        continue
                    if has_buy_signal:
                        logger.info(f"\n🚨 SINAL DE COMPRA DETECTADO PARA {symbol}!")
                        if order_manager.validate_order_conditions(symbol):
                            logger.info(f"✅ Condições validadas para {symbol}")
                            order_result = (
                                await order_manager.place_buy_order_with_trailing_stop(
                                    symbol=symbol, indicator=indicator
                                )
                            )
                            if order_result:
                                print(f"🎯 Trail Order -  ORDEM EXECUTADA COM SUCESSO!")
                                print(f"• Símbolo: {symbol}")
                                print(f"• Quantidade: {order_result['amount']}")
                                print(f"• Preço: ${order_result['entry_price']}")
                                print(
                                    f"• ID da ordem: {order_result['limit_order'].get('id', 'N/A')}"
                                )

                                bot.save_order_to_db(order_result["limit_order"])
                                bot.created_orders.append(order_result["limit_order"])
                                await bot.play_alert("success", volume=0.7)
                            else:
                                logger.error(
                                    f"❌ Falha ao executar ordem para {symbol}"
                                )
                                await bot.play_alert("error", volume=0.5)
                        else:
                            logger.warning(f"⚠️ Condições não atendidas para {symbol}")
                            logger.info(
                                f"    (Pode haver ordens ativas ou saldo insuficiente)"
                            )
                    else:
                        logger.info(
                            f"Swing | {symbol} -> Wait"
                        )  # Assuming logger supports color formatting
                        print()
                except Exception as exc:
                    logger.error("Erro ao processar %s: %s", symbol, str(exc))
                    logger.error(f"❌ Erro ao processar {symbol}: {exc}")
            print(f"\n⏰ Aguardando próximo ciclo (60 segundos)...")
            await asyncio.sleep(60)
    except KeyboardInterrupt:
        logger.info("Bot interrompido manualmente pelo usuário.")
        print("\n🛑 Bot interrompido manualmente.")
        if "bot" in locals():
            await bot.play_alert("exit", volume=0.9)
            await bot.send_telegram_notification(
                "🛑 Bot de trading interrompido manualmente."
            )
            logger.info("Notificação de encerramento enviada via Telegram")
        print("A finalizar o bot. Até breve!")
        return
    except (ConfigurationError, ExchangeConnectionError) as exc:
        logger.error("Erro de configuração/conexão: %s", exc)
        logger.error(f"❌ Erro: {exc}")
        logger.error("Por favor, verifique as suas credenciais e configuração.")
    except Exception as exc:
        logger.error("Erro inesperado: %s", exc)
        logger.exception(exc)
        logger.error(f"❌ Erro inesperado: {exc}")
        logger.error("Verifique os logs para mais detalhes.")


if __name__ == "__main__":
    asyncio.run(main())