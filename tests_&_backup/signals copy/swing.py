import numpy as np
import pandas as pd
import talib
import os
import json
from dataclasses import dataclass

@dataclass
class SwingConfig:
    """Configuration parameters for swing strategy with JSON fallback"""
    swing_bars: int = 3
    swing_bars2: int = 3
    cluster_bars: int = 20
    num_clusters: int = 3
    wma_period: int = 84
    rsi_period: int = 22
    adx_period: int = 14
    ohlcv_limit: int = 300
    
    def __post_init__(self):
        """Load parameters from JSON file if available"""
        try:
            if os.path.exists("parameters.json"):
                with open("parameters.json", "r") as f:
                    params = json.load(f).get("general", {})
                    
                # Update parameters from JSON
                param_mapping = {
                    "RSI_PERIOD": "rsi_period",
                    "WMA_PERIOD": "wma_period",
                    "ADX_PERIOD": "adx_period",
                    "OHLCV_LIMIT": "ohlcv_limit"
                }
                
                for json_param, attr_name in param_mapping.items():
                    if json_param in params:
                        setattr(self, attr_name, int(params[json_param]))
        except Exception as e:
            print(f"Error loading parameters: {e}. Using default values")

def validate_data(df, min_rows=100):
    """
    Validate DataFrame has sufficient data for technical indicators
    """
    if len(df) < min_rows:
        print(f"Warning: Only {len(df)} rows available. Need at least {min_rows} for reliable indicators.")
        return False
    return True

def calculate_technical_indicators(df, config: SwingConfig):
    """
    Calculate technical indicators for swing trading
    Parameters:
        df (pd.DataFrame): OHLCV data
        config (SwingConfig): Configuration parameters
    Returns:
        pd.DataFrame: DataFrame with technical indicators
    """
    result = df.copy()
    
    # Validate sufficient data
    min_period = max(config.rsi_period, config.adx_period, config.wma_period)
    if len(df) < min_period:
        print(f"Warning: Insufficient data ({len(df)} rows) for period {min_period}")
        return result
    
    # Calculate indicators with error handling
    try:
        result['rsi'] = talib.RSI(df['close'].astype(float), timeperiod=config.rsi_period)
        result['adx'] = talib.ADX(
            df['high'].astype(float), 
            df['low'].astype(float), 
            df['close'].astype(float), 
            timeperiod=config.adx_period
        )
        result['wma'] = talib.WMA(df['close'].astype(float), timeperiod=config.wma_period)
    except Exception as e:
        print(f"Error calculating indicators: {e}")
        # Fill with NaN if calculation fails
        for col in ['rsi', 'adx', 'wma']:
            if col not in result.columns:
                result[col] = np.nan
    
    return result

def generate_swing_signals(df, config: SwingConfig):
    """
    Generate swing trading signals using AI-enhanced logic
    Parameters:
        df (pd.DataFrame): OHLCV data
        config (SwingConfig): Configuration parameters
    Returns:
        pd.DataFrame: DataFrame with swing signals
    """
    if df is None or df.empty:
        raise ValueError("DataFrame is empty or None")
    
    # Ensure required columns
    required_cols = ['open', 'high', 'low', 'close']
    if not all(col in df.columns for col in required_cols):
        raise ValueError(f"DataFrame must contain columns: {required_cols}")
    
    # Create a copy to avoid modifying original
    result = df.copy()
    
    # Validate data types and convert if necessary
    for col in required_cols:
        result[col] = pd.to_numeric(result[col], errors='coerce')
    
    # Remove rows with NaN values
    result = result.dropna(subset=required_cols)
    
    if len(result) == 0:
        raise ValueError("No valid data after cleaning")
    
    # Calculate technical indicators
    result = calculate_technical_indicators(result, config)
    
    # Limit to recent data for performance
    result = result.tail(min(config.ohlcv_limit, len(result)))
    
    # Reset index to avoid issues with calculations
    result = result.reset_index(drop=True)
    
    # Calculate swing levels with minimum window size
    window_size = min(config.swing_bars, len(result))
    if window_size > 0:
        result['res'] = result['high'].rolling(window=window_size, min_periods=1).max()
        result['sup'] = result['low'].rolling(window=window_size, min_periods=1).min()
    else:
        result['res'] = result['high']
        result['sup'] = result['low']
    
    # Swing direction logic with bounds checking
    shift_periods = min(config.swing_bars2, len(result) - 1)
    if shift_periods > 0:
        res_shifted = result['res'].shift(shift_periods)
        sup_shifted = result['sup'].shift(shift_periods)
    else:
        res_shifted = result['res']
        sup_shifted = result['sup']
    
    # Use numpy.where with proper conditions
    result['avd'] = np.where(
        result['close'] > res_shifted, 1,
        np.where(result['close'] < sup_shifted, -1, 0)
    )
    
    # Fill forward non-zero avd values
    result['avn'] = result['avd'].replace(0, np.nan).ffill().fillna(0)
    result['tsl'] = np.where(result['avn'] == 1, result['sup'], result['res'])
    
    # Cluster-based price analysis
    cluster_window = min(config.cluster_bars, len(result))
    result['price_high'] = result['high'].rolling(window=cluster_window, min_periods=1).max()
    result['price_low'] = result['low'].rolling(window=cluster_window, min_periods=1).min()
    result['price_range'] = result['price_high'] - result['price_low']
    
    # Avoid division by zero
    result['cluster_step'] = np.where(
        result['price_range'] > 0, 
        result['price_range'] / config.num_clusters,
        result['close'] * 0.01  # 1% fallback
    )
    
    # Assign price to clusters with bounds checking
    result['current_cluster'] = np.where(
        result['cluster_step'] > 0,
        np.floor((result['close'] - result['price_low']) / result['cluster_step']),
        0
    )
    result['current_cluster'] = np.clip(result['current_cluster'], 0, config.num_clusters-1)
    
    # Volatility adjustments with safe division
    result['volatility'] = np.where(
        cluster_window > 0,
        result['price_range'] / cluster_window,
        result['price_range']
    )
    
    safe_denominator = result['price_range'] + np.finfo(float).eps  # Avoid division by zero
    result['cluster_adjust'] = result['cluster_step'] * (0.3 + 0.7 * (result['volatility'] / safe_denominator))
    
    result['cluster_weight'] = 1.0 - np.abs(result['current_cluster'] - (config.num_clusters / 2.0)) / (config.num_clusters / 2.0)
    
    # Calculate adjusted trailing stop
    cluster_adjustment = result['cluster_adjust'] * result['cluster_weight'] * (result['current_cluster'] / config.num_clusters)
    
    result['tsl_adjusted'] = np.where(
        result['avn'] == 1,
        result['sup'] + cluster_adjustment,
        result['res'] - cluster_adjustment
    )
    
    # Generate trading signals with additional safety checks
    # Create individual conditions first
    close_above_tsl = result['close'] > result['tsl_adjusted']
    close_prev_below_tsl = result['close'].shift(1) <= result['tsl_adjusted'].shift(1)
    close_above_wma = result['close'] > result['wma']
    rsi_above_50 = result['rsi'] > 50
    rsi_valid = result['rsi'].notna()
    wma_valid = result['wma'].notna()
    
    # Combine conditions using & operator (element-wise AND)
    result['swing_long_entry'] = (
        close_above_tsl & 
        close_prev_below_tsl & 
        close_above_wma & 
        rsi_above_50 &
        rsi_valid &
        wma_valid
    )
    
    # Short entry signals
    close_below_tsl = result['close'] < result['tsl_adjusted']
    close_prev_above_tsl = result['close'].shift(1) >= result['tsl_adjusted'].shift(1)
    close_below_wma = result['close'] < result['wma']
    rsi_below_50 = result['rsi'] < 50
    
    result['swing_short_entry'] = (
        close_below_tsl & 
        close_prev_above_tsl & 
        close_below_wma & 
        rsi_below_50 &
        rsi_valid &
        wma_valid
    )
    
    # Position signals
    result['swing_long'] = close_above_tsl
    result['swing_short'] = close_below_tsl
    
    return result

class SwingSignalGenerator:
    """
    Classe para gerar sinais de swing trading com base na função generate_swing_signals.
    """
    def __init__(self, indicator, client):
        self.indicator = indicator
        self.client = client
        self.config = SwingConfig()

    def check_entry_signal(self, symbol, timeframe=None):
        """
        Verifica se há um sinal de entrada (compra) para o símbolo especificado.
        
        Args:
            symbol (str): Símbolo do par de trading.
            timeframe (str, optional): Timeframe para os dados OHLCV. Se None, usa o padrão do client.
            
        Returns:
            bool: True se houver sinal de compra, False caso contrário.
        """
        try:
            if timeframe is None:
                timeframe = self.client.config.TIMEFRAME
            
            # Obter dados OHLCV com tratamento de erro para símbolos inválidos
            try:
                ohlcv_data = self.indicator.fetch_historical_data(symbol, timeframe)
                if ohlcv_data is None or len(ohlcv_data) == 0:
                    print(f"Sem dados disponíveis para {symbol}")
                    return False
            except Exception as e:
                print(f"Erro ao buscar dados para {symbol}: {e}")
                return False
            
            # Converter para DataFrame
            df = pd.DataFrame(ohlcv_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # Verificar se há dados suficientes
            if len(df) < 2:
                print(f"Dados insuficientes para {symbol}: {len(df)} linhas")
                return False
            
            # Gerar sinais de swing
            result = generate_swing_signals(df, self.config)
            
            # Verificar se há sinal de compra na última linha
            if not result.empty and 'swing_long_entry' in result.columns:
                # Obter apenas o último valor da série
                last_signal = result['swing_long_entry'].iloc[-1]
                
                # Verificar se é um valor válido (não NaN) e converter para bool
                if pd.isna(last_signal):
                    has_signal = False
                else:
                    # Converter explicitamente para python bool
                    has_signal = bool(last_signal)
                
                if has_signal:
                    print(f"Sinal de COMPRA detectado para {symbol} em {timeframe}")
                    return True
            
            return False
            
        except Exception as e:
            print(f"Erro ao verificar sinal para {symbol}: {e}")
            import traceback
            traceback.print_exc()  # Para debug - remover em produção
            return False
            
    def print_signal(self, symbol, timeframe=None, tickers=None):
        """
        Exibe informações sobre o sinal de trading para o símbolo especificado.
        
        Args:
            symbol (str): Símbolo do par de trading.
            timeframe (str, optional): Timeframe para os dados OHLCV. Se None, usa o padrão do client.
            tickers (dict, optional): Dicionário de tickers para obter o preço atual.
        """
        if timeframe is None:
            timeframe = self.client.config.TIMEFRAME
        
        has_signal = self.check_entry_signal(symbol, timeframe)
        signal_text = "BUY" if has_signal else "HOLD"
        color_code = "\033[32m" if has_signal else "\033[33m"  # Green for BUY, Yellow for HOLD
        reset_code = "\033[0m"
        
        current_price = 0.0
        if tickers and symbol in tickers:
            ticker = tickers.get(symbol, {})
            current_price = ticker.get("last", 0.0) if ticker else 0.0
        
        print(f"{color_code} • {symbol}: SINAL = {signal_text}{reset_code}")
        if current_price > 0.0:
            print(f"   Preço: ${current_price:,.2f}")
        else:
            print(f"   Preço: Indisponível")
