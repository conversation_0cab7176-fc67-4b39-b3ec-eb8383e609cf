import ccxt
import os
 

from dotenv import load_dotenv

load_dotenv()

credentials = {
    "api_key": os.getenv(f"demo_okx_apiKey"),
    "secret": os.getenv(f"demo_okx_secret"),
    "password": os.getenv(f"demo_okx_password"),
}

exchange = ccxt.myokx(
    {
        "apiKey": credentials["api_key"],
        "secret": credentials["secret"],
        "password": credentials["password"],
        "enableRateLimit": True,
        "options": {"defaultType": "spot"},
    }
)

exchange.set_sandbox_mode(True)
markets = exchange.load_markets()

limit_maker = exchange.fetch_open_orders(symbol = 'BTC/USDC', params = {})
print(limit_maker)

oco_orders = exchange.fetch_open_orders(symbol = 'BTC/USDC', params={"trigger": True, "ordType": "oco"})
print(oco_orders)

trailing_orders = exchange.fetch_open_orders(symbol = 'BTC/USDC', params={"trigger": True, "ordType": "move_order_stop"})
print(trailing_orders)
