"""
Optuna Parameter Optimizer for Trading Strategies
Otimizador de parâmetros para estratégias de trading usando Optuna

Este módulo fornece classes e funções para otimizar parâmetros de estratégias
de trading usando a biblioteca Optuna, com suporte a backtesting e métricas
de performance avançadas.

Author: Trading Team
Version: 1.0.0
"""

import warnings
warnings.filterwarnings("ignore", message="pkg_resources is deprecated as an API")

import optuna
import numpy as np
import pandas as pd
import json
import time
import asyncio
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor
import talib
import ccxt

# Suprimir warnings do Optuna para saída mais limpa
optuna.logging.set_verbosity(optuna.logging.WARNING)


@dataclass
class OptimizationConfig:
    """Configuração para o processo de otimização com Optuna."""
    study_name: str
    n_trials: int = 100
    storage: str = "sqlite:///optuna_studies.db"
    direction: str = "maximize"
    sampler: Optional[optuna.samplers.BaseSampler] = None
    pruner: Optional[optuna.pruners.BasePruner] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte a configuração para um dicionário."""
        return asdict(self)


@dataclass
class StrategyParameters:
    """Parâmetros de uma estratégia de trading para otimização."""
    name: str
    params: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte os parâmetros para um dicionário."""
        return asdict(self)


class TradingOptimizer:
    """
    Classe principal para otimização de parâmetros de estratégias de trading usando Optuna.
    Suporta backtesting e métricas de performance avançadas.
    """
    
    def __init__(
        self, 
        config: OptimizationConfig, 
        strategy_func: Callable, 
        backtest_data: pd.DataFrame,
        metrics: List[str] = ["sharpe_ratio"]
    ):
        """
        Inicializa o otimizador de trading.
        
        Args:
            config (OptimizationConfig): Configuração do estudo Optuna.
            strategy_func (Callable): Função da estratégia de trading a ser otimizada.
            backtest_data (pd.DataFrame): Dados históricos para backtesting (OHLCV).
            metrics (List[str]): Métricas de performance para otimização.
        """
        self.config = config
        self.strategy_func = strategy_func
        self.backtest_data = backtest_data
        self.metrics = metrics
        self.study = None
        self.best_params = None
        self.best_value = None
        
    def create_study(self) -> optuna.Study:
        """
        Cria um estudo Optuna com base na configuração fornecida.
        
        Returns:
            optuna.Study: Estudo Optuna configurado.
        """
        study = optuna.create_study(
            study_name=self.config.study_name,
            storage=self.config.storage,
            direction=self.config.direction,
            sampler=self.config.sampler,
            pruner=self.config.pruner,
            load_if_exists=True
        )
        self.study = study
        return study
    
    def objective(self, trial: optuna.Trial) -> float:
        """
        Função objetivo para otimização com Optuna.
        Define os parâmetros a serem otimizados e executa o backtest da estratégia de trailing stop.
        
        Args:
            trial (optuna.Trial): Tentativa atual do Optuna.
            
        Returns:
            float: Valor da métrica principal para otimização.
        """
        # Parâmetros a otimizar para a estratégia de trailing stop
        params = {
            "rsi_period": trial.suggest_int("rsi_period", 5, 20),
            "WMA_PERIDOD": trial.suggest_int("WMA_PERIDOD", 20, 100),
            "atr_period": trial.suggest_int("atr_period", 5, 20),
            "sl_multiplier": trial.suggest_float("sl_multiplier", 1.0, 3.0, step=0.05),
            "atr_multiplier": trial.suggest_float("atr_multiplier", 1.0, 3.0, step=0.05)
        }
        
        # Executa a estratégia com os parâmetros sugeridos
        results = self.strategy_func(self.backtest_data, params)
        
        # Calcula métricas de performance
        performance_metrics = self.calculate_performance_metrics(results)
        
        # Retorna a métrica principal para otimização
        main_metric = performance_metrics[self.metrics[0]]
        return main_metric
    
    def calculate_performance_metrics(self, results: Dict[str, Any]) -> Dict[str, float]:
        """
        Calcula métricas de performance com base nos resultados do backtest.
        
        Args:
            results (Dict[str, Any]): Resultados do backtest da estratégia.
            
        Returns:
            Dict[str, float]: Dicionário com métricas de performance.
        """
        metrics = {}
        
        # Calcula retornos cumulativos
        if "returns" in results:
            returns = np.array(results["returns"])
            if len(returns) > 0:
                cumulative_returns = np.cumprod(1 + returns) - 1
                
                # Sharpe Ratio (assumindo taxa livre de risco = 0)
                mean_return = np.mean(returns)
                std_return = np.std(returns)
                if std_return > 0:
                    sharpe_ratio = (mean_return * 252) / (std_return * np.sqrt(252))
                else:
                    sharpe_ratio = 0.0
                metrics["sharpe_ratio"] = sharpe_ratio
                
                # Sortino Ratio
                downside_returns = returns[returns < 0]
                downside_std = np.std(downside_returns) if len(downside_returns) > 0 else 0.0
                if downside_std > 0:
                    sortino_ratio = (mean_return * 252) / (downside_std * np.sqrt(252))
                else:
                    sortino_ratio = 0.0
                metrics["sortino_ratio"] = sortino_ratio
                
                # Maximum Drawdown
                peak = np.maximum.accumulate(cumulative_returns)
                drawdown = (cumulative_returns - peak) / (1 + peak)
                max_drawdown = np.min(drawdown) if len(drawdown) > 0 else 0.0
                metrics["max_drawdown"] = max_drawdown
        
        # Adiciona outras métricas conforme necessário
        for metric in self.metrics:
            if metric not in metrics:
                metrics[metric] = 0.0
                
        return metrics
    
    def optimize(self) -> Dict[str, Any]:
        """
        Executa o processo de otimização com Optuna.
        
        Returns:
            Dict[str, Any]: Melhores parâmetros encontrados e valor da métrica.
        """
        if self.study is None:
            self.create_study()
            
        self.study.optimize(self.objective, n_trials=self.config.n_trials)
        
        self.best_params = self.study.best_params
        self.best_value = self.study.best_value
        
        return {
            "best_params": self.best_params,
            "best_value": self.best_value,
            "study": self.study
        }
    
    def save_results(self, filepath: str) -> None:
        """
        Salva os resultados da otimização em um arquivo JSON.
        
        Args:
            filepath (str): Caminho do arquivo para salvar os resultados.
        """
        results = {
            "study_name": self.config.study_name,
            "best_params": self.best_params,
            "best_value": self.best_value,
            "timestamp": datetime.now().isoformat()
        }
        
        with open(filepath, "w") as f:
            json.dump(results, f, indent=2)
            
    def load_results(self, filepath: str) -> Dict[str, Any]:
        """
        Carrega resultados de otimização de um arquivo JSON.
        
        Args:
            filepath (str): Caminho do arquivo com os resultados.
            
        Returns:
            Dict[str, Any]: Resultados carregados.
        """
        with open(filepath, "r") as f:
            return json.load(f)


def calculate_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """
    Calcula indicadores técnicos para um DataFrame de dados OHLCV.
    
    Args:
        df (pd.DataFrame): DataFrame com colunas 'open', 'high', 'low', 'close', 'volume'.
        
    Returns:
        pd.DataFrame: DataFrame com indicadores técnicos calculados.
    """
    df = df.copy()
    
    # RSI
    df["rsi"] = talib.RSI(df["close"], timeperiod=14)
    
    # MACD
    df["macd"], df["macd_signal"], df["macd_hist"] = talib.MACD(
        df["close"], fastperiod=12, slowperiod=26, signalperiod=9
    )
    
    # ADX
    df["adx"] = talib.ADX(
        df["high"], df["low"], df["close"], timeperiod=14
    )
    
    # WMA
    df["wma"] = talib.WMA(df["close"], timeperiod=30)
    
    # SMA
    df["sma"] = talib.SMA(df["close"], timeperiod=30)
    
    return df


def run_backtest(
    data: pd.DataFrame, 
    strategy_func: Callable, 
    params: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Executa um backtest de uma estratégia de trading com os parâmetros fornecidos.
    
    Args:
        data (pd.DataFrame): Dados históricos para backtest.
        strategy_func (Callable): Função da estratégia de trading.
        params (Dict[str, Any]): Parâmetros da estratégia.
        
    Returns:
        Dict[str, Any]: Resultados do backtest.
    """
    # Adiciona indicadores técnicos aos dados
    data_with_indicators = calculate_indicators(data)
    
    # Executa a estratégia
    results = strategy_func(data_with_indicators, params)
    
    return results


if __name__ == "__main__":
    # Exemplo de uso do otimizador com a estratégia de trailing stop
    def trailing_stop_strategy(data: pd.DataFrame, params: Dict[str, Any]) -> Dict[str, Any]:
        """Estratégia de trading com trailing stop para otimização."""
        signals = np.zeros(len(data))
        returns = np.zeros(len(data))
        positions = np.zeros(len(data))
        entry_price = 0.0
        trailing_stop = 0.0
        
        # Calcular indicadores necessários
        data["rsi"] = talib.RSI(data["close"], timeperiod=params["rsi_period"])
        data["wma"] = talib.WMA(data["close"], timeperiod=params["WMA_PERIDOD"])
        data["atr"] = talib.ATR(data["high"], data["low"], data["close"], timeperiod=params["atr_period"])
        
        for i in range(1, len(data)):
            # Sinal de compra: RSI > 50 e preço > WMA
            if positions[i-1] == 0 and data["rsi"].iloc[i] > 50 and data["close"].iloc[i] > data["wma"].iloc[i]:
                signals[i] = 1  # Compra
                positions[i] = 1
                entry_price = data["close"].iloc[i]
                trailing_stop = entry_price - (data["atr"].iloc[i] * params["sl_multiplier"])
            # Manter posição
            elif positions[i-1] == 1:
                positions[i] = 1
                # Atualizar trailing stop
                new_stop = data["close"].iloc[i] - (data["atr"].iloc[i] * params["atr_multiplier"])
                trailing_stop = max(trailing_stop, new_stop)
                # Sair se o preço cair abaixo do trailing stop
                if data["close"].iloc[i] < trailing_stop:
                    signals[i] = -1  # Venda
                    positions[i] = 0
                    returns[i] = (data["close"].iloc[i] / entry_price) - 1
                    entry_price = 0.0
                    trailing_stop = 0.0
                elif signals[i-1] == 1:
                    returns[i] = (data["close"].iloc[i] / data["close"].iloc[i-1]) - 1
            else:
                positions[i] = 0
                
        return {"signals": signals, "returns": returns, "positions": positions}
    
    # Carregar dados históricos para backtest (substitua pelo caminho dos seus dados)
    # Exemplo: Carregar dados de um arquivo CSV
    try:
        data_path = "data/data_BTC_USDC_15m.csv"  # Ajuste o caminho conforme necessário
        backtest_data = pd.read_csv(data_path)
        # Garantir que as colunas necessárias estejam presentes e renomear se necessário
        backtest_data = backtest_data.rename(columns={
            "timestamp": "timestamp",
            "open": "open",
            "high": "high",
            "low": "low",
            "close": "close",
            "volume": "volume"
        })
        # Converter timestamp para datetime se necessário
        if "timestamp" in backtest_data.columns:
            backtest_data["timestamp"] = pd.to_datetime(backtest_data["timestamp"])
            backtest_data.set_index("timestamp", inplace=True)
        print("Dados históricos carregados com sucesso.")
    except FileNotFoundError:
        print("Arquivo de dados não encontrado. Usando dados de exemplo.")
        # Dados de exemplo como fallback (substituir por dados reais)
        dates = pd.date_range(start="2023-01-01", end="2023-12-31", freq="H")
        backtest_data = pd.DataFrame({
            "open": np.random.normal(100, 10, len(dates)),
            "high": np.random.normal(102, 10, len(dates)),
            "low": np.random.normal(98, 10, len(dates)),
            "close": np.random.normal(100, 10, len(dates)),
            "volume": np.random.randint(100, 1000, len(dates))
        }, index=dates)
    
    # Configuração da otimização
    opt_config = OptimizationConfig(
        study_name="trailing_stop_strategy_optimization",
        n_trials=100,  # Ajuste o número de tentativas conforme necessário
        direction="maximize"
    )
    
    # Inicializa o otimizador
    optimizer = TradingOptimizer(opt_config, trailing_stop_strategy, backtest_data)
    
    # Executa a otimização
    print("Iniciando processo de otimização com Optuna...")
    results = optimizer.optimize()
    
    print("Otimização concluída!")
    print("Melhores parâmetros encontrados:", results["best_params"])
    print("Melhor valor da métrica (Sharpe Ratio):", results["best_value"])
    
    # Salva os resultados
    optimizer.save_results("trailing_stop_optimization_results.json")
    print("Resultados salvos em 'trailing_stop_optimization_results.json'.")
