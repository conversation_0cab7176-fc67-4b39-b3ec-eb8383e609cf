import numpy as np
from typing import List, Dict
from utils.logger import TradingLogger

class PerformanceTracker:
    """
    Classe para rastrear e analisar o desempenho de estratégias de trading.
    Calcula métricas como Sharpe Ratio, Sortino Ratio e Máximo Drawdown,
    e sugere rebalanceamento do portfólio com base em thresholds definidos.
    """

    def __init__(self, config):
        """
        Inicializa o PerformanceTracker com a configuração fornecida.

        Args:
            config: Instância de BotConfig contendo as configurações do bot.
        """
        self.config = config
        self.returns = []  # Lista para armazenar retornos periódicos
        self.max_drawdown = 0.0  # Máximo drawdown observado
        self.peak_value = 0.0  # Valor máximo do portfólio
        self.portfolio_value = 0.0  # Valor atual do portfólio
        self.logger = TradingLogger.get_logger(__name__)

    def update_returns(self, net_profit: float):
        """
        Atualiza a lista de retornos com o lucro líquido fornecido e recalcula o drawdown.

        Args:
            net_profit (float): Lucro líquido da ordem executada.
        """
        if self.portfolio_value > 0:
            period_return = net_profit / self.portfolio_value
            self.returns.append(period_return)
        elif net_profit > 0:
            self.returns.append(1.0)  # Retorno arbitrário se o portfólio for zero
        else:
            self.returns.append(0.0)
        self.update_drawdown()

    def update_drawdown(self):
        """
        Atualiza o valor do portfólio e o máximo drawdown com base nos retornos acumulados.
        """
        if not self.returns:
            return
        cumulative_returns = np.cumprod(1 + np.array(self.returns))
        self.portfolio_value = cumulative_returns[-1]
        if self.portfolio_value > self.peak_value:
            self.peak_value = self.portfolio_value
        drawdown = (
            (self.peak_value - self.portfolio_value) / self.peak_value
            if self.peak_value > 0
            else 0
        )
        self.max_drawdown = max(self.max_drawdown, drawdown)

    def calculate_sharpe_ratio(self, risk_free_rate: float = 0.02) -> float:
        """
        Calcula o Sharpe Ratio anualizado com base nos retornos.

        Args:
            risk_free_rate (float): Taxa livre de risco anual (padrão: 0.02).

        Returns:
            float: Sharpe Ratio calculado ou 0.0 se não houver dados suficientes.
        """
        if len(self.returns) < 2:
            self.logger.warning("Dados insuficientes para calcular o Sharpe Ratio.")
            return 0.0
        mean_return = np.mean(self.returns)
        std_return = np.std(self.returns)
        annualized_return = mean_return * 252  # Assumindo retornos diários
        annualized_volatility = std_return * np.sqrt(252)
        sharpe = (
            (annualized_return - risk_free_rate) / annualized_volatility
            if annualized_volatility > 0
            else 0.0
        )
        return sharpe

    def calculate_sortino_ratio(self, risk_free_rate: float = 0.02) -> float:
        """
        Calcula o Sortino Ratio anualizado, focando no risco de downside.

        Args:
            risk_free_rate (float): Taxa livre de risco anual (padrão: 0.02).

        Returns:
            float: Sortino Ratio calculado ou 0.0 se não houver dados suficientes.
        """
        if len(self.returns) < 2:
            self.logger.warning("Dados insuficientes para calcular o Sortino Ratio.")
            return 0.0
        mean_return = np.mean(self.returns)
        downside_returns = [r for r in self.returns if r < 0]
        downside_std = np.std(downside_returns) if downside_returns else 0.0
        annualized_return = mean_return * 252
        annualized_downside_volatility = downside_std * np.sqrt(252)
        sortino = (
            (annualized_return - risk_free_rate) / annualized_downside_volatility
            if annualized_downside_volatility > 0
            else 0.0
        )
        return sortino

    def get_max_drawdown(self) -> float:
        """
        Retorna o máximo drawdown observado em porcentagem.

        Returns:
            float: Máximo drawdown em porcentagem.
        """
        return self.max_drawdown * 100

    def check_rebalancing_needed(
        self, sharpe_threshold: float = 1.0, drawdown_threshold: float = 10.0
    ) -> bool:
        """
        Verifica se o rebalanceamento do portfólio é necessário com base nas métricas de desempenho.

        Args:
            sharpe_threshold (float): Limite mínimo para o Sharpe Ratio (padrão: 1.0).
            drawdown_threshold (float): Limite máximo para o drawdown em porcentagem (padrão: 10.0).

        Returns:
            bool: True se o rebalanceamento for recomendado, False caso contrário.
        """
        sharpe = self.calculate_sharpe_ratio()
        drawdown = self.get_max_drawdown()
        if sharpe < sharpe_threshold:
            self.logger.warning(
                f"Sharpe Ratio {sharpe:.2f} abaixo do limite {sharpe_threshold}. Rebalanceamento recomendado."
            )
            return True
        if drawdown > drawdown_threshold:
            self.logger.warning(
                f"Máximo Drawdown {drawdown:.2f}% excede o limite {drawdown_threshold}%. Rebalanceamento recomendado."
            )
            return True
        return False

    def suggest_rebalancing(
        self, symbols: List[str], balances: Dict[str, float]
    ) -> Dict[str, float]:
        """
        Sugere novas alocações para o portfólio com base no desempenho.

        Args:
            symbols (List[str]): Lista de símbolos no portfólio.
            balances (Dict[str, float]): Saldos atuais dos símbolos.

        Returns:
            Dict[str, float]: Sugestões de novas alocações para cada símbolo.
        """
        if not self.check_rebalancing_needed():
            self.logger.info("Nenhum rebalanceamento necessário com base nas métricas atuais.")
            return {symbol: balances.get(symbol, 0.0) for symbol in symbols}
        total_balance = sum(balances.values())
        if total_balance == 0:
            return {symbol: 0.0 for symbol in symbols}
        sharpe = self.calculate_sharpe_ratio()
        target_allocation_per_symbol = total_balance / len(symbols)
        new_allocations = {}
        for symbol in symbols:
            current_balance = balances.get(symbol, 0.0)
            if sharpe < 1.0:
                new_allocations[symbol] = target_allocation_per_symbol
            else:
                new_allocations[symbol] = current_balance
        self.logger.info("Sugestões de rebalanceamento geradas para o portfólio.")
        return new_allocations

    def display_performance_metrics(self):
        """
        Exibe as métricas de desempenho do portfólio no console.
        """
        print("\n📊 Métricas de Desempenho da Carteira:")
        print("=" * 50)
        sharpe = self.calculate_sharpe_ratio()
        sortino = self.calculate_sortino_ratio()
        drawdown = self.get_max_drawdown()
        print(f" Rácio Sharpe: {sharpe:.2f}")
        print(f" Rácio Sortino: {sortino:.2f}")
        print(f" Máximo Drawdown: {drawdown:.2f}%")
        print()