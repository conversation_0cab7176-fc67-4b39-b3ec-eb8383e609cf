"""
Bot de trading automatizado para OKX Exchange.
"""

import warnings

warnings.filterwarnings("ignore", message="pkg_resources is deprecated as an API")

import asyncio
import os
import shutil
import numpy as np
import json
import ccxt
import talib
import pandas as pd
import signal
import uuid
import time
from dataclasses import dataclass
from decimal import Decimal, ROUND_HALF_UP
from dotenv import load_dotenv
from indicators.indicators import TechnicalIndicator
from service.service_alerts import SoundNotifications
from service.service_telegram import TelegramNotifier
from typing import Any, Dict, List, Optional, Tuple
from utils.logger import TradingLogger
from utils.database import TradingDatabase
from utils.exceptions import ConfigurationError, ExchangeConnectionError
from utils.check_orders import (
    check_regular_orders,
    display_regular_orders,
    check_oco_orders,
    display_oco_orders,
    check_trailing_stop_orders,
    display_trailing_stop_orders,
)
from utils.error_handler import ErrorHandler
from utils.order_validator import OrderValidator
from tabulate import tabulate

load_dotenv()
FIAT_CURRENCIES = ["EUR", "USD"]


@dataclass
class BotConfig:
    """Configurações centralizadas para o bot de trading."""

    SANDBOX_MODE: bool = True
    TRADING_SYMBOLS = ["BTC/USDC", "ETH/USDC", "SOL/USDC"]
    TIMEFRAME = "15m"

    RSI_PERIOD: int = 30
    WMA_PERIOD: int = 84
    SL_MULTIPLIER = 1.55
    ATR_MULTIPLIER: float = 1.5
    # Configurações para gestão dinâmica de posição baseada em volatilidade
    ENABLE_DYNAMIC_POSITION_SIZING: bool = True
    ENABLE_DYNAMIC_TRAILING_RATIO: bool = True
    POSITION_SIZE_BASE: float = 0.1  # 10% do saldo por padrão
    POSITION_SIZE_MIN: float = 0.05  # 5% mínimo em alta volatilidade
    POSITION_SIZE_MAX: float = 0.15  # 15% máximo em baixa volatilidade
    CALLBACK_RATIO_BASE: float = 0.03  # 3% por padrão para trailing stop
    CALLBACK_RATIO_MIN: float = 0.015  # 1.5% mínimo em baixa volatilidade
    CALLBACK_RATIO_MAX: float = 0.08  # 8% máximo em alta volatilidade
    ATR_LOOKBACK_PERIODS: int = 20  # Períodos para média histórica de ATR
    VOLATILITY_THRESHOLD_HIGH: float = (
        1.2  # Fator para alta volatilidade (ATR atual / média)
    )
    VOLATILITY_THRESHOLD_LOW: float = (
        0.8  # Fator para baixa volatilidade (ATR atual / média)
    )
    STABLECOINS: List[str] = None
    FIAT_DECIMAL_PLACES: int = 2
    CRYPTO_DECIMAL_PLACES: int = 8
    ENABLE_SOUND_NOTIFICATIONS: bool = True
    ENABLE_TELEGRAM_NOTIFICATIONS: bool = True

    def __post_init__(self):
        if self.TRADING_SYMBOLS is None:
            self.TRADING_SYMBOLS = BotConfig.SYMBOLS
        if self.STABLECOINS is None:
            self.STABLECOINS = ["USDC", "USDT", "BUSD", "FDUSD"]
        self._initialize_defaults()

    def _initialize_defaults(self):
        """Inicializa valores padrão de OKXClient para atributos não definidos."""
        from core.okx_client import OKXClient

        default_config = OKXClient().config
        # Obtém todos os atributos disponíveis no DefaultConfig
        default_attrs = [attr for attr in dir(default_config) if attr.isupper()]
        for attr in default_attrs:
            if (
                not hasattr(self, attr)
                or getattr(self, attr) is None
                or (
                    isinstance(getattr(self, attr), (int, float))
                    and getattr(self, attr) == 0
                )
            ):
                default_val = getattr(default_config, attr, None)
                if default_val is not None:
                    setattr(self, attr, default_val)
        # Comentado o código de cache de ordens para evitar erro de 'cache_duration' não definido
        """
        Inicializa o cache de ordens.
        
        Args:
            cache_duration: Duração em segundos antes de atualizar o cache.
        """
        # self.cache_duration = cache_duration
        # self.open_orders: Dict[str, List[Dict]] = {}
        # self.closed_orders: Dict[str, List[Dict]] = {}
        # self.last_updated: Dict[str, float] = {}

    def get_open_orders(self, symbol: str) -> Optional[List[Dict]]:
        """
        Obtém ordens abertas do cache se estiverem atualizadas.
        Comentado para evitar erro de 'cache_duration' não definido.

        Args:
            symbol: Símbolo do par de trading.

        Returns:
            Lista de ordens abertas ou None se o cache estiver desatualizado.
        """
        # if symbol in self.open_orders and symbol in self.last_updated:
        #     if time.time() - self.last_updated[symbol] < self.cache_duration:
        #         return self.open_orders[symbol]
        return None

    def get_closed_orders(self, symbol: str) -> Optional[List[Dict]]:
        """
        Obtém ordens fechadas do cache se estiverem atualizadas.
        Comentado para evitar erro de 'cache_duration' não definido.

        Args:
            symbol: Símbolo do par de trading.

        Returns:
            Lista de ordens fechadas ou None se o cache estiver desatualizado.
        """
        # if symbol in self.closed_orders and symbol in self.last_updated:
        #     if time.time() - self.last_updated[symbol] < self.cache_duration:
        #         return self.closed_orders[symbol]
        return None

    def update_orders(
        self, symbol: str, open_orders: List[Dict], closed_orders: List[Dict]
    ) -> None:
        """
        Atualiza o cache com novas ordens.
        Comentado para evitar erro de 'cache_duration' não definido.

        Args:
            symbol: Símbolo do par de trading.
            open_orders: Lista de ordens abertas.
            closed_orders: Lista de ordens fechadas.
        """
        # self.open_orders[symbol] = open_orders
        # self.closed_orders[symbol] = closed_orders
        # self.last_updated[symbol] = time.time()
        default_config = OKXClient().config
        # Obtém todos os atributos disponíveis no DefaultConfig
        default_attrs = [attr for attr in dir(default_config) if attr.isupper()]
        for attr in default_attrs:
            if (
                not hasattr(self, attr)
                or getattr(self, attr) is None
                or (
                    isinstance(getattr(self, attr), (int, float))
                    and getattr(self, attr) == 0
                )
            ):
                default_val = getattr(default_config, attr, None)
                if default_val is not None:
                    setattr(self, attr, default_val)
                    # print(f"Atributo {attr} inicializado com valor padrão: {default_val}")

    @classmethod
    def load_config(cls, file_path: str = "config.json") -> "BotConfig":
        """Carrega a configuração de um arquivo JSON."""
        try:
            with open(file_path, "r") as f:
                config_data = json.load(f)
            return cls(**config_data)
        except Exception as e:
            print(f"Falha ao carregar configuração: {e}")
            return cls()


from core.okx_client import OKXClient


class OKXOrder:
    _instance = None

    def __new__(cls, client: OKXClient = None):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, client: OKXClient = None):
        if getattr(self, "_initialized", False):
            return
        self._initialized = True
        self.client = client or OKXClient()
        self.logger = TradingLogger.get_logger(__name__)
        self.validator = OrderValidator(self.client)

    def validate_order_conditions(self, symbol: str) -> bool:
        """
        Valida se as condições estão adequadas para colocar uma ordem.

        Args:
            symbol: Par de trading

        Returns:
            True se condições estão OK, False caso contrário
        """
        return self.validator.validate_order_conditions(
            symbol, strategy_type="trailing"
        )

    def _generate_client_order_id(self) -> str:
        return uuid.uuid4().hex[:32]

    def has_active_trailing_stop(self, symbol: str) -> bool:
        try:
            trailing_orders = check_trailing_stop_orders(self.client, symbol)
            active_count = len(trailing_orders)
            if active_count > 0:
                self.logger.info(
                    "Encontradas %d ordens trailing stop ativas para %s",
                    active_count,
                    symbol,
                )
                for order in trailing_orders:
                    order_id = order.get("id", "N/A")
                    status = order.get("status", "N/A")
                    self.logger.debug(
                        "Trailing stop ativo: ID=%s, Status=%s", order_id, status
                    )
                return True
            return False
        except Exception as exc:
            self.logger.error(
                "Erro ao verificar trailing stops para %s: %s", symbol, str(exc)
            )
            return True

    async def wait_for_order_execution(
        self, order_id: str, symbol: str, timeout: int = 60, max_retries: int = 3
    ) -> Optional[Dict]:
        start_time = time.time()
        retries = 0
        while time.time() - start_time < timeout:
            try:
                order = self.client.exchange.fetch_order(order_id, symbol)
                if order["status"] == "closed":
                    self.logger.info(f"Ordem {order_id} executada com sucesso.")
                    return order
                elif order["status"] == "canceled":
                    self.logger.warning(f"Ordem {order_id} foi cancelada.")
                    return None
                await asyncio.sleep(2)  # Aumentar o intervalo entre tentativas
            except Exception as exc:
                retries += 1
                self.logger.error(
                    f"Erro ao verificar status da ordem {order_id} (Tentativa {retries}/{max_retries}): {exc}"
                )
                if retries >= max_retries:
                    self.logger.error(
                        f"Limite de tentativas atingido para a ordem {order_id}. Abortando."
                    )
                    return None
                await asyncio.sleep(3)  # Aguardar mais tempo antes de nova tentativa
        self.logger.warning(
            f"Tempo esgotado ao esperar pela execução da ordem {order_id}."
        )
        return None

    async def place_buy_order_with_trailing_stop(
        self, symbol: str, indicator: TechnicalIndicator, timeframe: str = "15m"
    ) -> Optional[Dict]:
        try:
            if check_trailing_stop_orders(self.client, symbol):
                self.logger.info(
                    "Já existem ordens abertas para %s, pulando criação", symbol
                )
                return None

            best_bid = self.client.get_best_bid(symbol)
            if not best_bid:
                self.logger.error("Não foi possível obter melhor bid para %s", symbol)
                return None
            entry_price = best_bid
            self.logger.info("Melhor bid obtido para %s: %s", symbol, entry_price)
            balance = self.client.get_balance()
            quote_currency = symbol.split("/")[1]
            available_balance = balance["total"].get(quote_currency, 0)
            if available_balance <= 0:
                self.logger.error(
                    "Saldo insuficiente em %s para %s", quote_currency, symbol
                )
                return None
            # Ajuste dinâmico do tamanho da posição baseado em volatilidade
            position_size = 0.1  # Valor padrão
            if (
                hasattr(self.client, "config")
                and hasattr(self.client.config, "ENABLE_DYNAMIC_POSITION_SIZING")
                and self.client.config.ENABLE_DYNAMIC_POSITION_SIZING
            ):
                atr = indicator.calculate_atr(symbol, timeframe, period=14)
                if atr is not None:
                    # Calcular média histórica de ATR para comparação
                    ohlcv_data = indicator.fetch_historical_data(
                        symbol,
                        timeframe,
                        limit=getattr(self.client.config, "ATR_LOOKBACK_PERIODS", 20)
                        + 14,
                    )
                    if ohlcv_data and len(ohlcv_data) > 14:
                        atr_values = []
                        for i in range(len(ohlcv_data) - 14, len(ohlcv_data)):
                            high = np.array(
                                [candle[2] for candle in ohlcv_data[i - 14 : i]]
                            )
                            low = np.array(
                                [candle[3] for candle in ohlcv_data[i - 14 : i]]
                            )
                            close = np.array(
                                [candle[4] for candle in ohlcv_data[i - 14 : i]]
                            )
                            atr_val = talib.ATR(high, low, close, timeperiod=14)[-1]
                            atr_values.append(atr_val)
                        atr_mean = np.mean(atr_values) if atr_values else atr
                        volatility_ratio = atr / atr_mean if atr_mean > 0 else 1.0
                        self.logger.info(
                            "Volatilidade para %s: ATR atual=%.2f, ATR médio=%.2f, Razão=%.2f",
                            symbol,
                            atr,
                            atr_mean,
                            volatility_ratio,
                        )

                        if volatility_ratio > getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_HIGH", 1.2
                        ):
                            position_size = getattr(
                                self.client.config, "POSITION_SIZE_MIN", 0.05
                            )
                            self.logger.info(
                                "Alta volatilidade detectada para %s, reduzindo tamanho da posição para %.1f%%",
                                symbol,
                                position_size * 100,
                            )
                        elif volatility_ratio < getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_LOW", 0.8
                        ):
                            position_size = getattr(
                                self.client.config, "POSITION_SIZE_MAX", 0.15
                            )
                            self.logger.info(
                                "Baixa volatilidade detectada para %s, aumentando tamanho da posição para %.1f%%",
                                symbol,
                                position_size * 100,
                            )
                        else:
                            position_size = getattr(
                                self.client.config, "POSITION_SIZE_BASE", 0.1
                            )
                            self.logger.info(
                                "Volatilidade normal para %s, tamanho da posição padrão %.1f%%",
                                symbol,
                                position_size * 100,
                            )

                        # Notificação de ajuste de volatilidade
                        if (
                            hasattr(self.client.config, "ENABLE_TELEGRAM_NOTIFICATIONS")
                            and self.client.config.ENABLE_TELEGRAM_NOTIFICATIONS
                            and volatility_ratio
                            > getattr(
                                self.client.config, "VOLATILITY_THRESHOLD_HIGH", 1.2
                            )
                        ):
                            message = f"⚠️ *Alta Volatilidade Detectada - {symbol}*\n• Tamanho da posição reduzido para {position_size * 100:.1f}%\n• Razão ATR: {volatility_ratio:.2f}"
                            asyncio.run_coroutine_threadsafe(
                                bot.send_telegram_notification(message),
                                asyncio.get_event_loop(),
                            )
                    else:
                        self.logger.warning(
                            "Dados históricos insuficientes para calcular média ATR de %s, usando tamanho padrão",
                            symbol,
                        )
                else:
                    self.logger.warning(
                        "Não foi possível calcular ATR para %s, usando tamanho padrão",
                        symbol,
                    )
            else:
                position_size = 0.1  # Padrão fixo se desativado

            balance_to_use = available_balance * position_size
            amount = balance_to_use / entry_price
            formatted_amount = self.client.format_amount_with_precision(symbol, amount)
            formatted_price = self.client.format_price_with_precision(
                symbol, entry_price
            )
            if float(formatted_amount) <= 0:
                self.logger.error(
                    "Quantidade calculada insuficiente para %s: %s",
                    symbol,
                    formatted_amount,
                )
                return None
            self.logger.info(
                "Calculado para %s - Quantidade: %s, Preço: %s",
                symbol,
                formatted_amount,
                formatted_price,
            )

            limit_order = self.client.exchange.create_order(
                symbol=symbol,
                type="limit",
                side="buy",
                amount=float(formatted_amount),
                price=float(formatted_price),
                params={"tdMode": "cash", "clOrdId": self._generate_client_order_id()},
            )
            if "amount" not in limit_order or limit_order["amount"] is None:
                limit_order["amount"] = float(formatted_amount)
            self.logger.info(
                "Ordem limite criada com sucesso: %s", limit_order.get("id")
            )

            # Aguardar execução da ordem limite
            executed_order = await self.wait_for_order_execution(
                limit_order["id"], symbol
            )
            if not executed_order:
                self.logger.error(
                    f"Ordem de compra {limit_order['id']} não foi executada."
                )
                return None

            entry_price_real = (
                executed_order["average"]
                if "average" in executed_order
                else executed_order["price"]
            )

            # Calcular callback ratio com 1.75 * ATR
            atr = indicator.calculate_atr(symbol, timeframe, period=14)
            if atr is None:
                self.logger.error("Não foi possível calcular ATR para %s", symbol)
                return None

            # Ajuste dinâmico do callback_ratio baseado em volatilidade
            callback_ratio_base = (1.75 * atr) / entry_price_real
            if (
                hasattr(self.client, "config")
                and hasattr(self.client.config, "ENABLE_DYNAMIC_TRAILING_RATIO")
                and self.client.config.ENABLE_DYNAMIC_TRAILING_RATIO
            ):
                if atr is not None:
                    # Calcular média histórica de ATR para comparação
                    ohlcv_data = indicator.fetch_historical_data(
                        symbol,
                        timeframe,
                        limit=getattr(self.client.config, "ATR_LOOKBACK_PERIODS", 20)
                        + 14,
                    )
                    if ohlcv_data and len(ohlcv_data) > 14:
                        atr_values = []
                        for i in range(len(ohlcv_data) - 14, len(ohlcv_data)):
                            high = np.array(
                                [candle[2] for candle in ohlcv_data[i - 14 : i]]
                            )
                            low = np.array(
                                [candle[3] for candle in ohlcv_data[i - 14 : i]]
                            )
                            close = np.array(
                                [candle[4] for candle in ohlcv_data[i - 14 : i]]
                            )
                            atr_val = talib.ATR(high, low, close, timeperiod=14)[-1]
                            atr_values.append(atr_val)
                        atr_mean = np.mean(atr_values) if atr_values else atr
                        volatility_ratio = atr / atr_mean if atr_mean > 0 else 1.0

                        if volatility_ratio > getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_HIGH", 1.2
                        ):
                            callback_ratio = max(
                                getattr(self.client.config, "CALLBACK_RATIO_MAX", 0.08),
                                callback_ratio_base,
                            )
                            self.logger.info(
                                "Alta volatilidade detectada para %s, aumentando callback ratio para %.2f%%",
                                symbol,
                                callback_ratio * 100,
                            )
                        elif volatility_ratio < getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_LOW", 0.8
                        ):
                            callback_ratio = min(
                                getattr(
                                    self.client.config, "CALLBACK_RATIO_MIN", 0.015
                                ),
                                callback_ratio_base,
                            )
                            self.logger.info(
                                "Baixa volatilidade detectada para %s, reduzindo callback ratio para %.2f%%",
                                symbol,
                                callback_ratio * 100,
                            )
                        else:
                            callback_ratio = max(
                                getattr(
                                    self.client.config, "CALLBACK_RATIO_BASE", 0.03
                                ),
                                min(
                                    callback_ratio_base,
                                    getattr(
                                        self.client.config, "CALLBACK_RATIO_MAX", 0.08
                                    ),
                                ),
                            )
                            self.logger.info(
                                "Volatilidade normal para %s, callback ratio padrão %.2f%%",
                                symbol,
                                callback_ratio * 100,
                            )
                    else:
                        self.logger.warning(
                            "Dados históricos insuficientes para calcular média ATR de %s, usando callback ratio base",
                            symbol,
                        )
                        callback_ratio = max(0.015, min(0.08, callback_ratio_base))
                else:
                    self.logger.warning(
                        "Não foi possível calcular ATR para %s, usando limites padrão para callback ratio",
                        symbol,
                    )
                    callback_ratio = max(0.015, min(0.08, callback_ratio_base))
            else:
                callback_ratio = max(
                    0.015, min(0.08, callback_ratio_base)
                )  # Padrão fixo se desativado

            trailing_params = {
                "instId": self.client.exchange.markets[symbol]["id"],
                "tdMode": "cash",
                "side": "sell",
                "ordType": "move_order_stop",
                "sz": formatted_amount,
                "callbackRatio": str(callback_ratio),
                "activePx": "",
                "clOrdId": self._generate_client_order_id(),
            }

            trailing_order = self.client.exchange.private_post_trade_order_algo(
                trailing_params
            )

            if trailing_order and trailing_order.get("code") == "0":
                algo_id = trailing_order.get("data", [{}])[0].get("algoId", "N/A")
                self.logger.info("Trailing stop criado com sucesso: %s", algo_id)
                # Salvar o trailing stop no banco de dados
                trailing_stop_data = {
                    "algo_id": algo_id,
                    "order_id": limit_order.get("id"),
                    "symbol": symbol,
                    "callback_ratio": callback_ratio,
                }
                bot = (
                    TradingBot()
                )  # Certifique-se de que a instância do bot está acessível
                bot.db.save_trailing_stop(trailing_stop_data)
            else:
                self.logger.error("Falha ao criar trailing stop: %s", trailing_order)

        except Exception as exc:
            self.logger.error("Erro ao colocar ordem para %s: %s", symbol, str(exc))
            return None

    def display_order_status_summary(self) -> None:
        """
        Exibe um resumo consolidado do status das ordens para todos os símbolos de trading.
        """
        print("\n📊 Resumo do Status das Ordens:")
        print("=" * 50)
        for symbol in self.client.config.TRADING_SYMBOLS:
            try:
                regular_count = len(check_regular_orders(self.client, symbol))
                oco_count = len(self.check_oco_orders(symbol))
                trailing_count = len(check_trailing_stop_orders(self.client, symbol))
                total_orders = regular_count + oco_count + trailing_count

                if total_orders > 0:
                    status_emoji = "🔴" if trailing_count > 0 else "🟡"
                    print(f" {status_emoji} {symbol}:")
                    print(f"• Ordens Regulares: {regular_count}")
                    print(f"• Ordens OCO: {oco_count}")
                    print(f"• Trailing Stops: {trailing_count}")
                    print(f"• Total: {total_orders}")
                else:
                    print(f" 🟢 {symbol}: Sem ordens ativas")
            except Exception as exc:
                print(f" ❌ {symbol}: Erro ao verificar status - {exc}")
        print()

    def check_oco_orders(self, symbol: str) -> List[Dict]:
        return check_oco_orders(self.client, symbol)

    def log_order_prevention_details(self, symbol: str) -> None:
        try:
            trailing_orders = check_trailing_stop_orders(self.client, symbol)
            if trailing_orders:
                self.logger.info("=== PREVENÇÃO DE ORDEM ATIVADA ===")
                self.logger.info("Símbolo: %s", symbol)
                self.logger.info("Trailing stops ativos: %d", len(trailing_orders))
                for i, order in enumerate(trailing_orders, 1):
                    self.logger.info("Trailing Stop #%d:", i)
                    self.logger.info("  - ID: %s", order.get("id", "N/A"))
                    self.logger.info("  - Status: %s", order.get("status", "N/A"))
                    self.logger.info("  - Lado: %s", order.get("side", "N/A"))
                    self.logger.info("  - Quantidade: %s", order.get("amount", "N/A"))
                    self.logger.info(
                        "  - Trigger: %s", order.get("triggerPrice", "N/A")
                    )
                self.logger.info("Nova ordem BLOQUEADA para %s", symbol)
                self.logger.info("=====================================")
        except Exception as exc:
            self.logger.error("Erro ao registrar detalhes de prevenção: %s", str(exc))


class Sinais:
    def __init__(self, indicator: TechnicalIndicator, client: OKXClient):
        self.indicator = indicator
        self.client = client
        self.logger = TradingLogger.get_logger(__name__)

    def check_entry_signal(self, symbol: str, timeframe: str = None) -> bool:
        if timeframe is None:
            timeframe = self.client.config.TIMEFRAME
        try:
            ticker = self.client.get_ticker(symbol)
            if not ticker or "last" not in ticker:
                self.logger.error(f"Não há dados de ticker para {symbol}")
                return False
            price = ticker["last"]
            rsi = self.indicator.calculate_rsi(
                symbol, timeframe, period=self.client.config.RSI_PERIOD
            )
            wma = self.indicator.calculate_wma(
                symbol, timeframe, period=self.client.config.WMA_PERIOD
            )
            if rsi is None or wma is None:
                self.logger.debug(f"Dados do indicador em falta para {symbol}")
                return False
            if rsi > 50 and price > wma:
                self.logger.info(
                    f"\033[92mSinal de entrada detetado para {symbol}\033[0m"
                )
                return True
            return False
        except Exception as exc:
            self.logger.error(f"Erro ao verificar sinal para {symbol}: {exc}")
            return False

    def print_signal(self, symbol: str, timeframe: str = None) -> None:
        if timeframe is None:
            timeframe = self.client.config.TIMEFRAME
        try:
            ticker = self.client.get_ticker(symbol)
            if not ticker or "last" not in ticker:
                self.logger.warning(f" • {symbol}: Dados de preço indisponíveis")
                return
            price = ticker["last"]
            rsi = self.indicator.calculate_rsi(
                symbol, timeframe, period=self.client.config.RSI_PERIOD
            )
            wma = self.indicator.calculate_wma(
                symbol, timeframe, period=self.client.config.WMA_PERIOD
            )
            if rsi is None or wma is None:
                self.logger.warning(f" • {symbol}: Indicadores indisponíveis")
                return
            if rsi > 50 and price > wma:
                signal = "BUY"
                color = "\033[92m"
            else:
                signal = "HOLD"
                color = "\033[93m"
            self.logger.info(f" • TRAIL |  {symbol}: SINAL = {signal}")
            self.logger.info(f" • {symbol}: {color}{signal}\033[0m")
            self.logger.info(f"   Preço: ${price:.2f}")
            # self.logger.info(f"   RSI: {rsi:.2f}")
            # self.logger.info(f"   WMA50: ${wma50:.2f}")
        except Exception as exc:
            self.logger.error(f"Erro ao imprimir sinal para {symbol}: {exc}")


class TradingBot:
    def __init__(self, config: Optional[BotConfig] = None):
        self.config = config or BotConfig()
        self.logger = TradingLogger.get_logger(__name__)
        self.sound_alert = self._initialize_sound_notifications()
        self.notifier = self._initialize_telegram_notifier()
        self.created_orders = []
        self.db = self._initialize_database()

    def _initialize_sound_notifications(self) -> Optional[SoundNotifications]:
        if self.config.ENABLE_SOUND_NOTIFICATIONS:
            try:
                return SoundNotifications()
            except Exception as exc:
                self.logger.warning(
                    "Falha ao inicializar notificações sonoras: %s", str(exc)
                )
        return None

    def _initialize_telegram_notifier(self) -> Optional[TelegramNotifier]:
        if self.config.ENABLE_TELEGRAM_NOTIFICATIONS:
            try:
                return TelegramNotifier()
            except Exception as exc:
                self.logger.warning(
                    "Falha ao inicializar notificador Telegram: %s", str(exc)
                )
        return None

    def _initialize_database(self) -> TradingDatabase:
        db_name = "sandbox_trades.db" if self.config.SANDBOX_MODE else "live_trades.db"
        db_path = os.path.join("trades", db_name)
        try:
            return TradingDatabase(db_path)
        except Exception as exc:
            self.logger.error("Falha ao inicializar banco de dados: %s", str(exc))
            raise

    async def play_alert(self, alert_type: str, volume: float = 0.5) -> None:
        if self.sound_alert:
            try:
                self.sound_alert.play_notification(alert_type, volume=volume)
            except Exception as exc:
                self.logger.warning("Falha ao reproduzir alerta: %s", str(exc))

    async def send_telegram_notification(self, message: str) -> None:
        if self.notifier:
            try:
                await self.notifier.send_message(message)
            except Exception as exc:
                self.logger.warning(
                    "Falha ao enviar notificação Telegram: %s", str(exc)
                )

    def save_order_to_db(self, order: Dict) -> None:
        try:
            self.db.save_order(order)
            self.logger.info(
                "Ordem salva no banco de dados: %s", order.get("id", "N/A")
            )
        except Exception as exc:
            self.logger.error("Erro ao salvar ordem no banco de dados: %s", str(exc))

    async def sync_and_calculate_metrics(self, client, cycle_count: int = 1):
        """
        Sincroniza ordens abertas e fechadas da exchange para o DB local
        e, em seguida, executa o cálculo de métricas. Usa cache para minimizar chamadas à API.
        Comentado o uso de OrderCache para evitar erro de 'OrderCache' não definido.
        """
        # if not hasattr(self, 'order_cache'):
        #     self.order_cache = OrderCache(cache_duration=30)

        for symbol in client.config.TRADING_SYMBOLS:
            # Tentar obter ordens do cache - comentado
            # open_orders = self.order_cache.get_open_orders(symbol)
            # closed_orders = self.order_cache.get_closed_orders(symbol)

            # if open_orders is None or closed_orders is None:
            # Cache desatualizado ou inexistente, buscar da exchange
            max_retries = 3
            open_orders = []
            closed_orders = []
            for attempt in range(max_retries):
                try:
                    open_orders = client.exchange.fetch_open_orders(symbol)
                    break
                except Exception as exc:
                    self.logger.error(
                        "Erro ao obter ordens abertas para %s (tentativa %d/%d): %s",
                        symbol,
                        attempt + 1,
                        max_retries,
                        str(exc),
                    )
                    if attempt == max_retries - 1:
                        self.logger.error(
                            "Falha ao obter ordens abertas para %s após %d tentativas",
                            symbol,
                            max_retries,
                        )
                    time.sleep(2)
            for attempt in range(max_retries):
                try:
                    closed_orders = client.exchange.fetch_closed_orders(symbol)
                    break
                except Exception as exc:
                    self.logger.error(
                        "Erro ao obter ordens fechadas para %s (tentativa %d/%d): %s",
                        symbol,
                        attempt + 1,
                        max_retries,
                        str(exc),
                    )
                    if attempt == max_retries - 1:
                        self.logger.error(
                            "Falha ao obter ordens fechadas para %s após %d tentativas",
                            symbol,
                            max_retries,
                        )
                    time.sleep(2)
            # Atualizar cache - comentado
            # self.order_cache.update_orders(symbol, open_orders, closed_orders)
            # self.logger.info("Cache de ordens atualizado para %s", symbol)
            # else:
            #     self.logger.info("Usando ordens do cache para %s", symbol)

            all_orders = open_orders + closed_orders
            # Salvar ordens novas no banco de dados
            for order in all_orders:
                if not self.db.get_order_by_id(order["id"]):
                    self.db.save_order(order)
        # Calcular métricas após sincronização
        await self.calculate_investment_metrics(client, cycle_count)

    def calculate_advanced_metrics(self, client: OKXClient) -> Dict[str, Any]:
        """Calcula métricas avançadas como Sharpe Ratio, Máximo Drawdown, Win Rate e Profit Factor."""
        metrics = {}
        for symbol in self.config.TRADING_SYMBOLS:
            try:
                # Obter ordens fechadas do banco de dados
                all_orders = self.db.get_orders(limit=1000)
                symbol_orders = [
                    o
                    for o in all_orders
                    if o.get("symbol") == symbol
                    and o.get("status", "")
                    and o.get("status", "").lower() == "closed"
                ]
                if not symbol_orders:
                    metrics[symbol] = {
                        "sharpe_ratio": 0.0,
                        "max_drawdown": 0.0,
                        "win_rate": 0.0,
                        "profit_factor": 0.0,
                    }
                    continue

                # Calcular retornos para Sharpe Ratio
                buy_orders = [
                    o
                    for o in symbol_orders
                    if o.get("side", "") and o.get("side", "").lower() == "buy"
                ]
                sell_orders = [
                    o
                    for o in symbol_orders
                    if o.get("side", "") and o.get("side", "").lower() == "sell"
                ]
                returns = []
                equity_curve = []
                total_equity = 0.0
                for sell_order in sorted(
                    sell_orders, key=lambda x: x.get("timestamp", 0)
                ):
                    sell_price = float(sell_order.get("price", 0.0))
                    sell_amount = float(sell_order.get("amount", 0.0))
                    buy_amount_total = 0.0
                    buy_cost_total = 0.0
                    for buy_order in sorted(
                        buy_orders, key=lambda x: x.get("timestamp", 0)
                    ):
                        if (
                            buy_order.get("timestamp", 0)
                            < sell_order.get("timestamp", 0)
                            and buy_amount_total < sell_amount
                        ):
                            buy_amount = float(buy_order.get("amount", 0.0))
                            buy_price = float(buy_order.get("price", 0.0))
                            amount_to_use = min(
                                buy_amount, sell_amount - buy_amount_total
                            )
                            buy_amount_total += amount_to_use
                            buy_cost_total += amount_to_use * buy_price
                    if buy_amount_total > 0:
                        avg_buy_price = buy_cost_total / buy_amount_total
                        profit = (sell_price - avg_buy_price) * sell_amount
                        total_equity += profit
                        equity_curve.append(total_equity)
                        if len(equity_curve) > 1:
                            returns.append(
                                (equity_curve[-1] - equity_curve[-2]) / equity_curve[-2]
                                if equity_curve[-2] > 0
                                else 0.0
                            )

                # Sharpe Ratio (assumindo taxa livre de risco = 0 para simplificação)
                if returns:
                    mean_return = np.mean(returns)
                    std_return = np.std(returns) if len(returns) > 1 else 0.0
                    sharpe_ratio = (
                        mean_return / std_return * np.sqrt(252)
                        if std_return > 0
                        else 0.0
                    )  # Anualizado
                else:
                    sharpe_ratio = 0.0

                # Máximo Drawdown
                max_drawdown = 0.0
                peak = equity_curve[0] if equity_curve else 0.0
                for value in equity_curve:
                    if value > peak:
                        peak = value
                    drawdown = (peak - value) / peak if peak > 0 else 0.0
                    if drawdown > max_drawdown:
                        max_drawdown = drawdown

                # Win Rate e Profit Factor
                wins = sum(
                    1
                    for sell_order in sell_orders
                    for buy_order in buy_orders
                    if buy_order.get("timestamp", 0) < sell_order.get("timestamp", 0)
                    and float(sell_order.get("price", 0.0))
                    > float(buy_order.get("price", 0.0))
                )
                total_trades = len(sell_orders)
                win_rate = wins / total_trades * 100 if total_trades > 0 else 0.0

                gross_profit = sum(
                    (
                        float(sell_order.get("price", 0.0))
                        - float(buy_order.get("price", 0.0))
                    )
                    * float(sell_order.get("amount", 0.0))
                    for sell_order in sell_orders
                    for buy_order in buy_orders
                    if buy_order.get("timestamp", 0) < sell_order.get("timestamp", 0)
                    and float(sell_order.get("price", 0.0))
                    > float(buy_order.get("price", 0.0))
                )
                gross_loss = abs(
                    sum(
                        (
                            float(sell_order.get("price", 0.0))
                            - float(buy_order.get("price", 0.0))
                        )
                        * float(sell_order.get("amount", 0.0))
                        for sell_order in sell_orders
                        for buy_order in buy_orders
                        if buy_order.get("timestamp", 0)
                        < sell_order.get("timestamp", 0)
                        and float(sell_order.get("price", 0.0))
                        <= float(buy_order.get("price", 0.0))
                    )
                )
                profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0.0

                metrics[symbol] = {
                    "sharpe_ratio": sharpe_ratio,
                    "max_drawdown": max_drawdown * 100,  # Em porcentagem
                    "win_rate": win_rate,
                    "profit_factor": profit_factor,
                }

                self.logger.info(f"📊 Métricas Avançadas para {symbol}:")
                self.logger.info(f"• Sharpe Ratio: {sharpe_ratio:.2f}")
                self.logger.info(f"• Máximo Drawdown: {max_drawdown * 100:.2f}%")
                self.logger.info(f"• Win Rate: {win_rate:.2f}%")
                self.logger.info(f"• Profit Factor: {profit_factor:.2f}")
            except Exception as exc:
                self.logger.error(
                    f"Erro ao calcular métricas avançadas para {symbol}: {str(exc)}"
                )
                metrics[symbol] = {
                    "sharpe_ratio": 0.0,
                    "max_drawdown": 0.0,
                    "win_rate": 0.0,
                    "profit_factor": 0.0,
                }
        return metrics

    async def calculate_investment_metrics(
        self, client: OKXClient, cycle_count: int = 1
    ) -> None:
        """
        Calcula e exibe métricas de investimento por símbolo, incluindo valores investidos e lucros reais.
        """
        print("\n📊 Métricas de Investimento por Símbolo:")
        print("=" * 50)
        try:
            # Obter todas as ordens do banco de dados
            all_orders = self.db.get_orders(limit=1000)
            if not all_orders:
                print(" Nenhuma ordem encontrada no banco de dados.")
                return

            # Agrupar ordens por símbolo
            orders_by_symbol = {}
            for order in all_orders:
                symbol = order.get("symbol", "N/A")
                if symbol not in orders_by_symbol:
                    orders_by_symbol[symbol] = []
                orders_by_symbol[symbol].append(order)

            total_invested = 0
            total_realized_pnl = 0
            total_unrealized_pnl = 0

            for symbol, orders in orders_by_symbol.items():
                if symbol == "N/A":
                    continue

                # Calcular valor investido (somente ordens de compra)
                buy_orders = [
                    o
                    for o in orders
                    if o.get("side", "") and o.get("side", "").lower() == "buy"
                ]
                invested_amount = sum(
                    float(o.get("amount", 0)) * float(o.get("price", 0))
                    for o in buy_orders
                    if o.get("amount") is not None and o.get("price") is not None
                )

                # Calcular lucro/prejuízo realizado (ordens fechadas de venda)
                sell_orders = [
                    o
                    for o in orders
                    if o.get("side", "")
                    and o.get("side", "").lower() == "sell"
                    and o.get("status", "")
                    and o.get("status", "").lower() == "closed"
                ]
                realized_pnl = 0
                used_buy_amounts = {o.get("id", "N/A"): 0 for o in buy_orders}
                for sell_order in sorted(
                    sell_orders, key=lambda x: x.get("timestamp", 0)
                ):
                    sell_amount = (
                        float(sell_order.get("amount", 0))
                        if sell_order.get("amount") is not None
                        else 0.0
                    )
                    sell_price = (
                        float(sell_order.get("price", 0))
                        if sell_order.get("price") is not None
                        else 0.0
                    )
                    sell_timestamp = sell_order.get("timestamp", 0)
                    buy_amount_total = 0
                    buy_cost_total = 0
                    for buy_order in sorted(
                        buy_orders, key=lambda x: x.get("timestamp", 0)
                    ):
                        buy_id = buy_order.get("id", "N/A")
                        buy_amount = (
                            float(buy_order.get("amount", 0))
                            if buy_order.get("amount") is not None
                            else 0.0
                        )
                        buy_price = (
                            float(buy_order.get("price", 0))
                            if buy_order.get("price") is not None
                            else 0.0
                        )
                        buy_timestamp = buy_order.get("timestamp", 0)
                        if (
                            buy_timestamp < sell_timestamp
                            and used_buy_amounts[buy_id] < buy_amount
                        ):
                            available_amount = buy_amount - used_buy_amounts[buy_id]
                            amount_to_use = min(
                                available_amount, sell_amount - buy_amount_total
                            )
                            if amount_to_use > 0:
                                buy_amount_total += amount_to_use
                                buy_cost_total += amount_to_use * buy_price
                                used_buy_amounts[buy_id] += amount_to_use
                                if buy_amount_total >= sell_amount:
                                    break
                    if buy_amount_total > 0:
                        avg_buy_price = buy_cost_total / buy_amount_total
                        realized_pnl += (sell_price - avg_buy_price) * sell_amount

                # Calcular lucro/prejuízo não realizado (posições abertas)
                open_buy_orders = [
                    o
                    for o in buy_orders
                    if o.get("status", "") and o.get("status", "").lower() != "closed"
                ]
                open_amount = sum(
                    float(o.get("amount", 0))
                    for o in open_buy_orders
                    if o.get("amount") is not None
                )
                if open_amount > 0:
                    ticker = client.get_ticker(symbol)
                    current_price = (
                        float(ticker.get("last", 0))
                        if ticker
                        and "last" in ticker
                        and ticker.get("last") is not None
                        else 0.0
                    )
                    open_cost = sum(
                        float(o.get("amount", 0)) * float(o.get("price", 0))
                        for o in open_buy_orders
                        if o.get("amount") is not None and o.get("price") is not None
                    )
                    avg_open_price = open_cost / open_amount if open_amount > 0 else 0
                    unrealized_pnl = (current_price - avg_open_price) * open_amount
                else:
                    unrealized_pnl = 0

                total_invested += invested_amount
                total_realized_pnl += realized_pnl
                total_unrealized_pnl += unrealized_pnl

                self.logger.info(f" 🪙 {symbol}:")
                self.logger.info(f"• Valor Investido: ${invested_amount:.2f}")
                self.logger.info(f"• Lucro/Prejuízo Realizado: ${realized_pnl:.2f}")
                self.logger.info(
                    f"• Lucro/Prejuízo Não Realizado: ${unrealized_pnl:.2f}"
                )
                self.logger.info(" " + "-" * 30)

            print("\n📈 Resumo Geral:")
            print("=" * 50)
            self.logger.info(f" • Total Investido: ${total_invested:.2f}")
            self.logger.info(
                f" • Lucro/Prejuízo Realizado Total: ${total_realized_pnl:.2f}"
            )
            self.logger.info(
                f" • Lucro/Prejuízo Não Realizado Total: ${total_unrealized_pnl:.2f}"
            )
            self.logger.info(
                f" • Lucro/Prejuízo Total: ${(total_realized_pnl + total_unrealized_pnl):.2f}"
            )

            # Calcular e exibir métricas avançadas
            metrics = self.calculate_advanced_metrics(client)
            print("\n📊 Métricas Avançadas:")
            print("=" * 50)
            for symbol in self.config.TRADING_SYMBOLS:
                metric = metrics.get(symbol, {})
                print(f" • {symbol}:")
                print(f"   - Sharpe Ratio: {metric.get('sharpe_ratio', 0.0):.2f}")
                print(f"   - Máximo Drawdown: {metric.get('max_drawdown', 0.0):.2f}%")
                print(f"   - Win Rate: {metric.get('win_rate', 0.0):.2f}%")
                print(f"   - Profit Factor: {metric.get('profit_factor', 0.0):.2f}")

            # Enviar alertas de performance via Telegram foi desativado conforme solicitação do usuário
            pass

        except Exception as exc:
            self.logger.error("Erro ao calcular métricas de investimento: %s", str(exc))
            print(f" ❌ Erro ao calcular métricas: {exc}")

    async def _calculate_all_indicators(
        self, symbol: str, indicator: TechnicalIndicator
    ) -> Dict[str, Any]:
        return {
            "rsi": indicator.calculate_rsi(symbol),
            "adx": indicator.calculate_adx(symbol),
            "wma": indicator.calculate_wma(symbol),
            "sma": indicator.calculate_sma(symbol),
            "atr": indicator.calculate_atr(symbol),
        }

    async def display_orders(self) -> None:
        print("\n📋 Ordens Recentes:")
        print("=" * 50)
        if not self.created_orders:
            print(" Nenhuma ordem criada ainda.")
            return
        for order in self.created_orders[-5:]:
            order_id = order.get("id", "N/A")
            symbol = order.get("symbol", "N/A")
            order_type = order.get("type", "N/A")
            side = order.get("side", "N/A")
            amount = order.get("amount", "N/A")
            price = order.get("price", "N/A")
            status = order.get("status", "N/A")
            # self.logger.info(f" ID: {order_id}")
            # self.logger.info(f" Símbolo: {symbol}")
            # self.logger.info(f" Tipo: {order_type}")
            # self.logger.info(f" Lado: {side}")
            # self.logger.info(f" Quantidade: {amount}")
            # self.logger.info(f" Preço: {price}")
            # self.logger.info(f" Status: {status}")
            # self.logger.info(" " + "-" * 30)
            print(f" ID: {order_id}")
            print(f" Símbolo: {symbol}")
            print(f" Tipo: {order_type}")
            print(f" Lado: {side}")
            print(f" Quantidade: {amount}")
            print(f" Preço: {price}")
            print(f" Estado: {status}")
            print(" " + "-" * 30)

    async def sync_orders(self, client: OKXClient, symbol: str):
        exchange_orders = await client.exchange.fetch_open_orders(symbol)
        local_orders = self.db.get_open_orders(symbol)
        for order in exchange_orders:
            if order["id"] not in [o["id"] for o in local_orders]:
                self.db.add_order(order)
        self.logger.info("Orders synchronized for %s", symbol)


async def initialize_bot_components() -> Tuple[OKXClient, OKXOrder, TradingBot]:
    try:
        config = BotConfig()
        client = OKXClient(config)
        order_manager = OKXOrder(client)
        bot = TradingBot(config)
        return client, order_manager, bot
    except Exception as exc:
        logger = TradingLogger.get_logger(__name__)
        logger.error("Falha ao inicializar componentes do bot: %s", str(exc))
        raise ConfigurationError("Falha na inicialização do bot") from exc


async def send_startup_notification(client: OKXClient, bot: TradingBot) -> None:
    if not bot.config.ENABLE_TELEGRAM_NOTIFICATIONS:
        return
    try:
        balance = client.get_balance()
        message = await build_startup_message(balance, bot.config)
        await bot.send_telegram_notification(message)
    except Exception as exc:
        logger = TradingLogger.get_logger(__name__)
        logger.error("Erro ao enviar notificação de inicialização: %s", str(exc))


async def build_startup_message(balance: Optional[Dict], config: BotConfig) -> str:
    mode_text = "🟢 LIVE TRADING" if not config.SANDBOX_MODE else "🔴 SANDBOX MODE"
    message = f"""🚀 *BOT Trailing 01 Iniciado*
-----------------------------
• *Mode*: {mode_text}
• *Balance*:
💰 Available Balance:
"""
    if balance and balance.get("total"):
        for asset, amount in balance["total"].items():
            if amount > 0:
                message += f" 🪙 {asset}: {amount:.8f}\n"
    return message


async def main() -> None:
    logger = TradingLogger.get_logger(__name__)

    def handle_sigstp(signum, frame):
        logger.info("Bot interrompido manualmente pelo usuário (SIGTSTP).")
        print("\n🛑 Bot interrompido manualmente (Ctrl+Z).")
        print("A finalizar o bot. Até breve!")
        exit(0)

    try:
        client, order_manager, bot = await initialize_bot_components()
        await send_startup_notification(client, bot)
        await bot.play_alert("start", volume=0.25)
        signal.signal(signal.SIGTSTP, handle_sigstp)
        indicator = TechnicalIndicator(client)
        for symbol in client.config.TRADING_SYMBOLS:
            ohlcv_data = indicator.fetch_historical_data(
                symbol, client.config.TIMEFRAME
            )
        cycle_count = 0
        while True:
            cycle_count += 1
            if cycle_count == 1:
                client._print_mode_message()
            print(
                f"\n🔄 Ciclo #{cycle_count} - {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
            client.display_prices_with_precision()
            client.display_balance_with_precision()
            await bot.display_orders()
            await bot.sync_and_calculate_metrics(client)
            # Garantir que as métricas avançadas sejam calculadas e enviadas apenas no primeiro ciclo
            try:
                metrics = bot.calculate_advanced_metrics(client)
                if (
                    bot.config.ENABLE_TELEGRAM_NOTIFICATIONS and cycle_count == 1
                ):  # Enviar apenas no primeiro ciclo
                    message_lines = [
                        "📊 *Relatório de Performance* - Ciclo " + str(cycle_count)
                    ]
                    for symbol in bot.config.TRADING_SYMBOLS:
                        metric = metrics.get(symbol, {})
                        message_lines.append("• *" + symbol + ":*")
                        message_lines.append(
                            "  - Sharpe Ratio: "
                            + str(round(metric.get("sharpe_ratio", 0.0), 2))
                        )
                        message_lines.append(
                            "  - Máximo Drawdown: "
                            + str(round(metric.get("max_drawdown", 0.0), 2))
                            + "%"
                        )
                        message_lines.append(
                            "  - Win Rate: "
                            + str(round(metric.get("win_rate", 0.0), 2))
                            + "%"
                        )
                        message_lines.append(
                            "  - Profit Factor: "
                            + str(round(metric.get("profit_factor", 0.0), 2))
                        )
                    message = "\n".join(message_lines)
                    await bot.send_telegram_notification(message)
            except Exception as e:
                logger.error("Erro ao calcular ou enviar métricas: %s", str(e))
            display_regular_orders(client)
            # display_oco_orders(client)
            display_trailing_stop_orders(client)

            # Verificar ordens de trailing stop fechadas para notificação de PnL
            for symbol in client.config.TRADING_SYMBOLS:
                try:
                    trailing_orders = check_trailing_stop_orders(client, symbol)
                    closed_orders = client.exchange.fetch_closed_orders(symbol, limit=5)
                    for order in closed_orders:
                        if (
                            order["side"].lower() == "sell"
                            and order["type"] == "move_order_stop"
                            and order["status"] == "closed"
                        ):
                            order_id = order.get("id", "N/A")
                            # Verificar se esta ordem já foi notificada
                            if order_id not in bot.created_orders:
                                bot.created_orders.append(order_id)
                                # Calcular PnL
                                buy_orders = [
                                    o
                                    for o in bot.db.get_orders(limit=100)
                                    if o["symbol"] == symbol
                                    and o["side"].lower() == "buy"
                                    and o["status"] == "closed"
                                ]
                                sell_price = float(
                                    order.get("average", order.get("price", 0))
                                )
                                sell_amount = float(order.get("amount", 0))
                                buy_amount_total = 0
                                buy_cost_total = 0
                                for buy_order in buy_orders:
                                    buy_amount = float(buy_order.get("amount", 0))
                                    if buy_amount_total + buy_amount <= sell_amount:
                                        buy_amount_total += buy_amount
                                        buy_cost_total += buy_amount * float(
                                            buy_order.get("price", 0)
                                        )
                                    else:
                                        remaining = sell_amount - buy_amount_total
                                        buy_amount_total += remaining
                                        buy_cost_total += remaining * float(
                                            buy_order.get("price", 0)
                                        )
                                        break
                                if buy_amount_total > 0:
                                    avg_buy_price = buy_cost_total / buy_amount_total
                                    pnl = (sell_price - avg_buy_price) * sell_amount
                                    percentage_pnl = (
                                        (pnl / (avg_buy_price * sell_amount)) * 100
                                        if avg_buy_price > 0
                                        else 0
                                    )
                                    bot.save_order_to_db(order)
                                    message = f"""🔒 *TRAIL \\- POSIÇÃO FECHADA*
• *Símbolo*: {symbol}
• *Tipo*: Trailing Stop (Venda)
• *Quantidade*: {sell_amount}
• *Preço de Entrada*: ${avg_buy_price:.2f}
• *Preço de Saída*: ${sell_price:.2f}
• *PnL*: ${pnl:.2f} ({percentage_pnl:.2f}%)
• *ID*: {order_id}
"""
                                    # Notificação desativada conforme solicitação do usuário
                                    await bot.play_alert("success", volume=0.7)
                                    logger.info(
                                        f"Posição fechada para {symbol} com PnL: ${pnl:.2f} ({percentage_pnl:.2f}%)"
                                    )
                except Exception as exc:
                    logger.error(
                        f"Erro ao verificar ordens fechadas para {symbol}: {str(exc)}"
                    )

            print("\n🤖 Verificando Sinais de Trading:")
            print("=" * 50)
            checker = Sinais(indicator, client)
            for symbol in client.config.TRADING_SYMBOLS:
                try:
                    has_buy_signal = checker.check_entry_signal(symbol, timeframe=None)
                    checker.print_signal(symbol, timeframe=None)
                    if order_manager.has_active_trailing_stop(symbol):
                        print(
                            f"⏳ {symbol}: Trailing stop ativo detectado. Aguardando fechamento..."
                        )
                        continue
                    if has_buy_signal:
                        logger.info(f"\n🚨 SINAL DE COMPRA DETECTADO PARA {symbol}!")
                        if order_manager.validate_order_conditions(symbol):
                            logger.info(f"✅ Condições validadas para {symbol}")
                            order_result = (
                                await order_manager.place_buy_order_with_trailing_stop(
                                    symbol=symbol, indicator=indicator
                                )
                            )
                            if order_result:
                                print(f"🎯 Trail Order -  ORDEM EXECUTADA COM SUCESSO!")
                                print(f"• Símbolo: {symbol}")
                                print(f"• Quantidade: {order_result['amount']}")
                                print(f"• Preço: ${order_result['entry_price']}")
                                print(
                                    f"• Valor usado: ${order_result['ten_percent_used']:.2f}"
                                )
                                print(
                                    f"• ID da ordem: {order_result['limit_order'].get('id', 'N/A')}"
                                )

                                bot.save_order_to_db(order_result["limit_order"])
                                bot.created_orders.append(order_result["limit_order"])
                                await bot.play_alert("success", volume=0.7)
                                message = f"""🎯 *Trail Order -  ORDEM EXECUTADA*
• *Símbolo*: {symbol}
• *Tipo*: Trailing Stop
• *Quantidade*: {order_result['amount']}
• *Preço*: ${order_result['entry_price']}
• *Valor*: ${order_result['ten_percent_used']:.2f}
• *ID*: ${order_result['limit_order'].get('id', 'N/A')}
"""
                                # Notificação desativada conforme solicitação do usuário
                            else:
                                logger.error(
                                    f"❌ Falha ao executar ordem para {symbol}"
                                )
                                await bot.play_alert("error", volume=0.5)
                        else:
                            # logger.warning(f"⚠️ Condições não atendidas para {symbol}")
                            # logger.info(
                            #     f"    (Pode haver ordens ativas ou saldo insuficiente)"
                            # )
                    else:
                        logger.info(f"   → Sem sinal de compra para {symbol}")
                        print()
                except Exception as exc:
                    logger.error("Erro ao processar %s: %s", symbol, str(exc))
                    logger.error(f"❌ Erro ao processar {symbol}: {exc}")
            print(f"\n⏰ Aguardando próximo ciclo (60 segundos)...")
            await asyncio.sleep(60)
    except KeyboardInterrupt:
        logger.info("Bot interrompido manualmente pelo usuário.")
        print("\n🛑 Bot interrompido manualmente.")
        if "bot" in locals():
            await bot.play_alert("exit", volume=0.9)
        print("A finalizar o bot. Até breve!")
        return
    except (ConfigurationError, ExchangeConnectionError) as exc:
        logger.error("Erro de configuração/conexão: %s", exc)
        logger.error(f"❌ Erro: {exc}")
        logger.error("Por favor, verifique as suas credenciais e configuração.")
    except Exception as exc:
        logger.error("Erro inesperado: %s", exc)
        logger.exception(exc)
        logger.error(f"❌ Erro inesperado: {exc}")
        logger.error("Verifique os logs para mais detalhes.")


if __name__ == "__main__":
    asyncio.run(main())
