"""
Bot de trading automatizado para OKX Exchange.
"""

import warnings

warnings.filterwarnings("ignore", message="pkg_resources is deprecated as an API")

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
import os
import shutil
import numpy as np
import json
import sqlite3
import ccxt
import talib
import pandas as pd
import signal
import uuid
import time
from dataclasses import dataclass
from decimal import Decimal, ROUND_HALF_UP
from dotenv import load_dotenv
from indicators.indicators import TechnicalIndicator
from service.service_alerts import SoundNotifications
from service.service_telegram import TelegramNotifier
from typing import Any, Dict, List, Optional, Tuple
from utils.logger import TradingLogger
from utils.database import TradingDatabase
from utils.metrics import MetricsCalculator
from utils.exceptions import ConfigurationError, ExchangeConnectionError
from utils.check_orders import (
    check_regular_orders,
    display_regular_orders,
    check_oco_orders,
    display_oco_orders,
)
from utils.error_handler import <PERSON>rror<PERSON>andler
from utils.order_validator import OrderValidator
from tabulate import tabulate
from signals.signal_wma_rsi import SinaisWmaRsi
import json


class OrderCache:
    """Cache local para ordens, reduzindo chamadas à API da exchange."""

    def __init__(self, cache_duration: int = 30):
        """
        Inicializa o cache de ordens.

        Args:
            cache_duration: Duração em segundos antes de atualizar o cache.
        """
        self.cache_duration = cache_duration
        self.open_orders: Dict[str, List[Dict]] = {}
        self.closed_orders: Dict[str, List[Dict]] = {}
        self.last_updated: Dict[str, float] = {}

    def get_open_orders(self, symbol: str) -> Optional[List[Dict]]:
        """
        Obtém ordens abertas do cache se estiverem atualizadas.

        Args:
            symbol: Símbolo do par de trading.

        Returns:
            Lista de ordens abertas ou None se o cache estiver desatualizado.
        """
        if symbol in self.open_orders and symbol in self.last_updated:
            if time.time() - self.last_updated[symbol] < self.cache_duration:
                return self.open_orders[symbol]
        return None

    def get_closed_orders(self, symbol: str) -> Optional[List[Dict]]:
        """
        Obtém ordens fechadas do cache se estiverem atualizadas.

        Args:
            symbol: Símbolo do par de trading.

        Returns:
            Lista de ordens fechadas ou None se o cache estiver desatualizado.
        """
        if symbol in self.closed_orders and symbol in self.last_updated:
            if time.time() - self.last_updated[symbol] < self.cache_duration:
                return self.closed_orders[symbol]
        return None

    def update_orders(
        self, symbol: str, open_orders: List[Dict], closed_orders: List[Dict]
    ) -> None:
        """
        Atualiza o cache com novas ordens.

        Args:
            symbol: Símbolo do par de trading.
            open_orders: Lista de ordens abertas.
            closed_orders: Lista de ordens fechadas.
        """
        self.open_orders[symbol] = open_orders
        self.closed_orders[symbol] = closed_orders
        self.last_updated[symbol] = time.time()


load_dotenv()
FIAT_CURRENCIES = ["EUR", "USD"]


def load_parameters():
    """Carrega os parâmetros do arquivo parameters.json."""
    with open("parameters.json", "r") as f:
        return json.load(f)


@dataclass
class BotConfig:
    """Configurações centralizadas para o bot de trading."""

    params = load_parameters()

    SANDBOX_MODE: bool = True

    TRADING_SYMBOLS = params["OCO_wma_rsi"]["TRADING_SYMBOLS"]
    TIMEFRAME = params["OCO_wma_rsi"]["TIMEFRAME"]
    RSI_PERIOD: int = params["OCO_wma_rsi"]["RSI_PERIOD"]
    WMA_PERIOD: int = params["OCO_wma_rsi"]["WMA_PERIOD"]
    OHLCV_LIMIT: int = params["OCO_wma_rsi"]["OHLCV_LIMIT"]
    ATR_SL_FACTOR: float = params["OCO_wma_rsi"]["ATR_SL_FACTOR"]
    ATR_TP_FACTOR: float = params["OCO_wma_rsi"]["ATR_TP_FACTOR"]

    ENABLE_DYNAMIC_POSITION_SIZING: bool = True
    ENABLE_DYNAMIC_ATR_MULTIPLIERS: bool = True
    POSITION_SIZE_BASE: float = 0.1  # 10% do saldo por padrão
    POSITION_SIZE_MIN: float = 0.05  # 5% mínimo em alta volatilidade
    POSITION_SIZE_MAX: float = 0.15  # 15% máximo em baixa volatilidade
    ATR_SL_FACTOR_MIN: float = 1.8  # Multiplicador SL mínimo
    ATR_SL_FACTOR_MAX: float = 3.0  # Multiplicador SL máximo
    ATR_TP_FACTOR_MIN: float = 1.8  # Multiplicador TP mínimo
    ATR_TP_FACTOR_MAX: float = 3.5  # Multiplicador TP máximo
    ATR_LOOKBACK_PERIODS: int = 20  # Períodos para média histórica de ATR
    VOLATILITY_THRESHOLD_HIGH: float = (
        1.2  # Fator para alta volatilidade (ATR atual / média)
    )
    VOLATILITY_THRESHOLD_LOW: float = (
        0.8  # Fator para baixa volatilidade (ATR atual / média)
    )
    STABLECOINS: List[str] = None
    FIAT_DECIMAL_PLACES: int = 2
    CRYPTO_DECIMAL_PLACES: int = 8
    ENABLE_SOUND_NOTIFICATIONS: bool = True
    ENABLE_TELEGRAM_NOTIFICATIONS: bool = True

    SCRIPT_NAME: str = os.path.splitext(os.path.basename(__file__))[0].replace("_", " ")

    def __post_init__(self):
        """Inicializa valores padrão após criação."""
        if self.TRADING_SYMBOLS is None:
            self.TRADING_SYMBOLS = BotConfig.SYMBOLS
        if self.STABLECOINS is None:
            self.STABLECOINS = ["USDC", "USDT", "BUSD", "FDUSD"]
        self._initialize_defaults()

    def _initialize_defaults(self):
        """Inicializa valores padrão de OKXClient para atributos não definidos."""
        from core.okx_client import OKXClient

        default_config = OKXClient().config
        # Obtém todos os atributos disponíveis no DefaultConfig
        default_attrs = [attr for attr in dir(default_config) if attr.isupper()]
        for attr in default_attrs:
            if (
                not hasattr(self, attr)
                or getattr(self, attr) is None
                or (
                    isinstance(getattr(self, attr), (int, float))
                    and getattr(self, attr) == 0
                )
            ):
                default_val = getattr(default_config, attr, None)
                if default_val is not None:
                    setattr(self, attr, default_val)
                    # print(f"Atributo {attr} inicializado com valor padrão: {default_val}")
        # Comentado o código de cache de ordens para evitar erro de 'cache_duration' não definido
        """
        Inicializa o cache de ordens.
        
        Args:
            cache_duration: Duração em segundos antes de atualizar o cache.
        """
        # self.cache_duration = cache_duration
        # self.open_orders: Dict[str, List[Dict]] = {}
        # self.closed_orders: Dict[str, List[Dict]] = {}
        # self.last_updated: Dict[str, float] = {}

    @classmethod
    def load_config(cls, file_path: str = "config.json") -> "BotConfig":
        """Carrega a configuração de um arquivo JSON."""
        try:
            with open(file_path, "r") as f:
                config_data = json.load(f)
            return cls(**config_data)
        except Exception as e:
            print(f"Falha ao carregar configuração: {e}")
            return cls()


from core.okx_client import OKXClient


class OKXOrder:
    """Classe para gerenciar operações de ordens na exchange OKX."""

    def __init__(self, client: OKXClient):
        self.client = client
        self.logger = TradingLogger.get_logger(__name__)
        self.error_handler = ErrorHandler(logger_name=__name__)
        self.validator = OrderValidator(client)
        # Garantir que o cache de ordens esteja inicializado no cliente
        if not hasattr(self.client, "order_cache"):
            self.client.order_cache = OrderCache(cache_duration=30)

    def validate_order_conditions(self, symbol: str) -> bool:
        """
        Valida se as condições estão adequadas para colocar uma ordem.
        Inclui verificação de ordens tp/sl (oco).

        Args:
            symbol: Par de trading

        Returns:
            True se condições estão OK, False caso contrário
        """
        return self.validator.validate_order_conditions(symbol, strategy_type="oco")

    def _generate_client_order_id(self) -> str:
        """Gera um ID único para a ordem."""
        return uuid.uuid4().hex[:32]

    def has_active_oco_orders(self, symbol: str) -> bool:
        """
        Verifica especificamente se há ordens tp/sl (oco) ativas.

        Args:
            symbol: Par de trading

        Returns:
            True se há ordens OCO ativas, False caso contrário
        """
        try:
            # Verificar primeiro no cache
            open_orders = self.client.order_cache.get_open_orders(symbol)
            if open_orders is not None:
                oco_orders = [
                    order
                    for order in open_orders
                    if order.get("info", {}).get("oco_details")
                ]
                active_count = len(oco_orders)
                if active_count > 0:
                    # Removed logging of active OCO orders as per user request
                    return True
                return False

            # Se não estiver no cache, buscar da exchange
            open_orders = check_oco_orders(self.client, symbol)
            active_count = len(open_orders)

            if active_count > 0:
                # Removed logging of active OCO orders as per user request
                for order in open_orders:
                    order_id = order.get("id", "N/A")
                    status = order.get("status", "N/A")
                    self.logger.debug(
                        "Ordem OCO ativa: ID=%s, Status=%s", order_id, status
                    )
                return True

            return False
        except Exception as exc:
            self.logger.debug("Erro ao buscar dados para %s: %s", symbol, str(exc))
            self.logger.info("O símbolo %s não existe na exchange.", symbol)
            return (
                True  # Em caso de erro, assume que há ordens ativas para ser cauteloso
            )

    def place_buy_order_with_oco(
        self, symbol: str, indicator: TechnicalIndicator, timeframe: str = "15m"
    ) -> Optional[Dict]:
        """
        Coloca ordem de compra com OCO (One Cancels the Other) usando 10% do saldo.
        O Stop Loss (SL) é definido como 1.75x o ATR abaixo do preço de entrada.
        O Take Profit (TP) é definido como 1.75x o ATR acima do preço de entrada.
        """
        try:
            existing_orders = check_oco_orders(self.client, symbol)
            if existing_orders:
                self.logger.info(
                    "Já existem ordens abertas para %s, pulando criação", symbol
                )
                return None
            best_bid = self.client.get_best_bid(symbol)
            if not best_bid:
                self.logger.error("Não foi possível obter melhor bid para %s", symbol)
                return None
            entry_price = best_bid
            self.logger.info("Melhor bid obtido para %s: %s", symbol, entry_price)
            balance = self.client.get_balance()
            quote_currency = symbol.split("/")[1]
            available_balance = balance["total"].get(quote_currency, 0)
            if available_balance <= 0:
                self.logger.error(
                    "Saldo insuficiente em %s para %s", quote_currency, symbol
                )
                return None
            # Ajuste dinâmico do tamanho da posição baseado em volatilidade
            position_size = 0.1  # Valor padrão
            if (
                hasattr(self.client.config, "ENABLE_DYNAMIC_POSITION_SIZING")
                and self.client.config.ENABLE_DYNAMIC_POSITION_SIZING
            ):
                atr = indicator.calculate_atr(symbol, timeframe, period=14)
                if atr is not None:
                    # Calcular média histórica de ATR para comparação
                    lookback_periods = getattr(
                        self.client.config, "ATR_LOOKBACK_PERIODS", 20
                    )
                    ohlcv_data = indicator.fetch_historical_data(
                        symbol, timeframe, limit=lookback_periods + 14
                    )
                    if ohlcv_data and len(ohlcv_data) > 14:
                        atr_values = []
                        for i in range(len(ohlcv_data) - 14, len(ohlcv_data)):
                            high = np.array(
                                [candle[2] for candle in ohlcv_data[i - 14 : i]]
                            )
                            low = np.array(
                                [candle[3] for candle in ohlcv_data[i - 14 : i]]
                            )
                            close = np.array(
                                [candle[4] for candle in ohlcv_data[i - 14 : i]]
                            )
                            atr_val = talib.ATR(high, low, close, timeperiod=14)[-1]
                            atr_values.append(atr_val)
                        atr_mean = np.mean(atr_values) if atr_values else atr
                        volatility_ratio = atr / atr_mean if atr_mean > 0 else 1.0
                        self.logger.info(
                            "Volatilidade para %s: ATR atual=%.2f, ATR médio=%.2f, Razão=%.2f",
                            symbol,
                            atr,
                            atr_mean,
                            volatility_ratio,
                        )

                        high_threshold = getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_HIGH", 1.2
                        )
                        low_threshold = getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_LOW", 0.8
                        )
                        if volatility_ratio > high_threshold:
                            position_size = getattr(
                                self.client.config, "POSITION_SIZE_MIN", 0.05
                            )
                            self.logger.info(
                                "Alta volatilidade detectada para %s, reduzindo tamanho da posição para %.1f%%",
                                symbol,
                                position_size * 100,
                            )
                        elif volatility_ratio < low_threshold:
                            position_size = getattr(
                                self.client.config, "POSITION_SIZE_MAX", 0.15
                            )
                            self.logger.info(
                                "Baixa volatilidade detectada para %s, aumentando tamanho da posição para %.1f%%",
                                symbol,
                                position_size * 100,
                            )
                        else:
                            position_size = getattr(
                                self.client.config, "POSITION_SIZE_BASE", 0.1
                            )
                            self.logger.info(
                                "Volatilidade normal para %s, tamanho da posição padrão %.1f%%",
                                symbol,
                                position_size * 100,
                            )

                        # Notificação de ajuste de volatilidade
                        if (
                            hasattr(self.client.config, "ENABLE_TELEGRAM_NOTIFICATIONS")
                            and self.client.config.ENABLE_TELEGRAM_NOTIFICATIONS
                            and volatility_ratio > high_threshold
                        ):
                            message = f"⚠️ *Alta Volatilidade Detectada - {symbol}*\n• Tamanho da posição reduzido para {position_size * 100:.1f}%\n• Razão ATR: {volatility_ratio:.2f}"
                            asyncio.run_coroutine_threadsafe(
                                bot.send_telegram_notification(message),
                                asyncio.get_event_loop(),
                            )
                    else:
                        self.logger.warning(
                            "Dados históricos insuficientes para calcular média ATR de %s, usando tamanho padrão",
                            symbol,
                        )
                else:
                    self.logger.warning(
                        "Não foi possível calcular ATR para %s, usando tamanho padrão",
                        symbol,
                    )
            else:
                position_size = 0.1  # Padrão fixo se desativado

            balance_to_use = available_balance * position_size
            ten_percent_balance = balance_to_use  # Definindo o valor usado como 10% do saldo ajustado pelo position_size
            amount = balance_to_use / entry_price
            formatted_amount = self.client.format_amount_with_precision(symbol, amount)
            formatted_price = self.client.format_price_with_precision(
                symbol, entry_price
            )
            if float(formatted_amount) <= 0:
                self.logger.error(
                    "Quantidade calculada insuficiente para %s: %s",
                    symbol,
                    formatted_amount,
                )
                return None
            self.logger.info(
                "Calculado para %s - Quantidade: %s, Preço: %s",
                symbol,
                formatted_amount,
                formatted_price,
            )
            # Calcular ATR
            atr = indicator.calculate_atr(symbol, timeframe, period=14)
            if atr is None:
                self.logger.error("Não foi possível calcular ATR para %s", symbol)
                return None
            # Ajuste dinâmico dos multiplicadores de ATR para SL e TP baseado em volatilidade
            atr_sl_factor = getattr(self.client.config, "ATR_SL_FACTOR", 1.5)
            atr_tp_factor = getattr(self.client.config, "ATR_TP_FACTOR", 2.0)
            if (
                hasattr(self.client.config, "ENABLE_DYNAMIC_ATR_MULTIPLIERS")
                and self.client.config.ENABLE_DYNAMIC_ATR_MULTIPLIERS
            ):
                if atr is not None:
                    # Calcular média histórica de ATR para comparação
                    lookback_periods = getattr(
                        self.client.config, "ATR_LOOKBACK_PERIODS", 20
                    )
                    ohlcv_data = indicator.fetch_historical_data(
                        symbol, timeframe, limit=lookback_periods + 14
                    )
                    if ohlcv_data and len(ohlcv_data) > 14:
                        atr_values = []
                        for i in range(len(ohlcv_data) - 14, len(ohlcv_data)):
                            high = np.array(
                                [candle[2] for candle in ohlcv_data[i - 14 : i]]
                            )
                            low = np.array(
                                [candle[3] for candle in ohlcv_data[i - 14 : i]]
                            )
                            close = np.array(
                                [candle[4] for candle in ohlcv_data[i - 14 : i]]
                            )
                            atr_val = talib.ATR(high, low, close, timeperiod=14)[-1]
                            atr_values.append(atr_val)
                        atr_mean = np.mean(atr_values) if atr_values else atr
                        volatility_ratio = atr / atr_mean if atr_mean > 0 else 1.0

                        high_threshold = getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_HIGH", 1.2
                        )
                        low_threshold = getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_LOW", 0.8
                        )
                        if volatility_ratio > high_threshold:
                            atr_sl_factor = getattr(
                                self.client.config, "ATR_SL_FACTOR_MAX", 2.0
                            )
                            atr_tp_factor = getattr(
                                self.client.config, "ATR_TP_FACTOR_MAX", 3.0
                            )
                            self.logger.info(
                                "Alta volatilidade detectada para %s, aumentando multiplicadores SL/TP para %.1f/%.1f",
                                symbol,
                                atr_sl_factor,
                                atr_tp_factor,
                            )
                        elif volatility_ratio < low_threshold:
                            atr_sl_factor = getattr(
                                self.client.config, "ATR_SL_FACTOR_MIN", 1.2
                            )
                            atr_tp_factor = getattr(
                                self.client.config, "ATR_TP_FACTOR_MIN", 1.8
                            )
                            self.logger.info(
                                "Baixa volatilidade detectada para %s, reduzindo multiplicadores SL/TP para %.1f/%.1f",
                                symbol,
                                atr_sl_factor,
                                atr_tp_factor,
                            )
                        else:
                            atr_sl_factor = getattr(
                                self.client.config, "ATR_SL_FACTOR", 1.5
                            )
                            atr_tp_factor = getattr(
                                self.client.config, "ATR_TP_FACTOR", 2.0
                            )
                            self.logger.info(
                                "Volatilidade normal para %s, multiplicadores SL/TP padrão %.1f/%.1f",
                                symbol,
                                atr_sl_factor,
                                atr_tp_factor,
                            )
                    else:
                        self.logger.warning(
                            "Dados históricos insuficientes para calcular média ATR de %s, usando multiplicadores padrão",
                            symbol,
                        )
                else:
                    self.logger.warning(
                        "Não foi possível calcular ATR para %s, usando multiplicadores padrão",
                        symbol,
                    )

            # Calcular Stop Loss (SL) e Take Profit (TP) com base no ATR ajustado
            sl_trigger = entry_price - (atr * atr_sl_factor)
            sl_price = sl_trigger
            tp_trigger = entry_price + (atr * atr_tp_factor)
            tp_price = tp_trigger
            formatted_sl_trigger = self.client.format_price_with_precision(
                symbol, sl_trigger
            )
            formatted_sl_price = self.client.format_price_with_precision(
                symbol, sl_price
            )
            formatted_tp_trigger = self.client.format_price_with_precision(
                symbol, tp_trigger
            )
            formatted_tp_price = self.client.format_price_with_precision(
                symbol, tp_price
            )
            self.logger.info(
                "Parâmetros OCO calculados para %s: SL Trigger=%s, SL Price=%s, TP Trigger=%s, TP Price=%s (ATR=%.2f)",
                symbol,
                formatted_sl_trigger,
                formatted_sl_price,
                formatted_tp_trigger,
                formatted_tp_price,
                atr,
            )
            # Definir parâmetros da ordem OCO
            order_params = {
                "orderType": "oco",
                "postOnly": False,
                "stopLoss": {
                    "triggerPrice": formatted_sl_trigger,
                    "price": formatted_sl_price,
                },
                "takeProfit": {
                    "triggerPrice": formatted_tp_trigger,
                    "price": formatted_tp_price,
                },
            }
            limit_order = self.client.exchange.create_order(
                symbol=symbol,
                type="limit",
                side="buy",
                amount=float(formatted_amount) if formatted_amount is not None else 0.0,
                price=entry_price,
                params=order_params,
            )
            if "amount" not in limit_order or limit_order["amount"] is None:
                limit_order["amount"] = float(formatted_amount)
            self.logger.info(
                "Ordem limite com OCO criada com sucesso: %s", limit_order.get("id")
            )
            # Adicionar informações OCO ao campo additional_info para armazenamento no banco de dados
            oco_info = {
                "oco_details": {
                    "sl_trigger": float(formatted_sl_trigger),
                    "sl_price": float(formatted_sl_price),
                    "tp_trigger": float(formatted_tp_trigger),
                    "tp_price": float(formatted_tp_price),
                }
            }
            if "info" not in limit_order:
                limit_order["info"] = {}
            limit_order["info"].update(oco_info)
            result = {
                "limit_order": limit_order,
                "entry_price": float(formatted_price),
                "amount": float(formatted_amount),
                "symbol": symbol,
                "ten_percent_used": ten_percent_balance,
                "sl_trigger": float(formatted_sl_trigger),
                "sl_price": float(formatted_sl_price),
                "tp_trigger": float(formatted_tp_trigger),
                "tp_price": float(formatted_tp_price),
            }
            self.logger.info("Ordem OCO processada com sucesso para %s", symbol)
            return result
        except Exception as exc:
            self.logger.error("Erro ao colocar ordem OCO para %s: %s", symbol, str(exc))
            return None

    def log_order_prevention_details(self, symbol: str) -> None:
        """
        Registra detalhes sobre prevenção de criação de ordens.

        Args:
            symbol: Par de trading
        """
        try:
            trailing_orders = check_oco_orders(self.client, symbol)

            if trailing_orders:
                self.logger.info("=== PREVENÇÃO DE ORDEM ATIVADA ===")
                self.logger.info("Símbolo: %s", symbol)
                self.logger.info("Trailing stops ativos: %d", len(trailing_orders))

                for i, order in enumerate(trailing_orders, 1):
                    self.logger.info("Trailing Stop #%d:", i)
                    self.logger.info("  - ID: %s", order.get("id", "N/A"))
                    self.logger.info("  - Status: %s", order.get("status", "N/A"))
                    self.logger.info("  - Lado: %s", order.get("side", "N/A"))
                    self.logger.info("  - Quantidade: %s", order.get("amount", "N/A"))
                    self.logger.info(
                        "  - Trigger: %s", order.get("triggerPrice", "N/A")
                    )

                self.logger.info("Nova ordem BLOQUEADA para %s", symbol)
                self.logger.info("=====================================")

        except Exception as exc:
            self.logger.error("Erro ao registrar detalhes de prevenção: %s", str(exc))


class TradingBot:
    """Bot de trading principal."""

    def __init__(self, config: Optional[BotConfig] = None):
        self.config = config or BotConfig()
        self.logger = TradingLogger.get_logger(__name__)
        self.error_handler = ErrorHandler(logger_name=__name__)
        self.sound_alert = self._initialize_sound_notifications()
        self.notifier = self._initialize_telegram_notifier()
        self.created_orders = []
        self.db = self._initialize_database()
        self.metrics_calculator = self._initialize_metrics_calculator()
        self.order_cache = OrderCache(cache_duration=30)

    def _initialize_sound_notifications(self) -> Optional[SoundNotifications]:
        """Inicializa notificações sonoras se habilitadas."""
        if self.config.ENABLE_SOUND_NOTIFICATIONS:
            try:
                return SoundNotifications()
            except Exception as exc:
                self.logger.warning(
                    "Falha ao inicializar notificações sonoras: %s", str(exc)
                )
        return None

    def _initialize_telegram_notifier(self) -> Optional[TelegramNotifier]:
        """Inicializa notificador Telegram se habilitado."""
        if self.config.ENABLE_TELEGRAM_NOTIFICATIONS:
            try:
                return TelegramNotifier()
            except Exception as exc:
                self.logger.warning(
                    "Falha ao inicializar notificador Telegram: %s", str(exc)
                )
        return None

    def _initialize_database(self) -> TradingDatabase:
        """Inicializa o banco de dados para persistência de ordens."""
        db_name = "sandbox_trades.db" if self.config.SANDBOX_MODE else "live_trades.db"
        db_path = os.path.join("trades", db_name)
        try:
            db = TradingDatabase(db_path)
            # Criar tabela para rastrear notificações de ordens, se não existir
            db.create_notified_orders_table()
            return db
        except Exception as exc:
            self.logger.error("Falha ao inicializar banco de dados: %s", str(exc))
            raise

    def _initialize_metrics_calculator(self) -> MetricsCalculator:
        """Inicializa o calculador de métricas para análise de desempenho."""
        db_name = "sandbox_trades.db" if self.config.SANDBOX_MODE else "live_trades.db"
        db_path = os.path.join("trades", db_name)
        try:
            return MetricsCalculator(db_path)
        except Exception as exc:
            self.logger.warning(
                "Falha ao inicializar calculador de métricas: %s", str(exc)
            )
            return None

    async def play_alert(self, alert_type: str, volume: float = 0.5) -> None:
        """Toca som de alerta se disponível."""
        if self.sound_alert:
            try:
                self.sound_alert.play_notification(alert_type, volume=volume)
            except Exception as exc:
                self.logger.warning("Falha ao reproduzir alerta: %s", str(exc))

    async def send_telegram_notification(self, message: str) -> None:
        """Envia notificação Telegram se disponível."""
        if self.notifier:
            try:
                await self.notifier.send_message(message)
            except Exception as exc:
                self.logger.warning(
                    "Falha ao enviar notificação Telegram: %s", str(exc)
                )

    def save_order_to_db(self, order: Dict) -> None:
        """Salva uma ordem no banco de dados."""
        try:
            self.db.save_order(order)
            self.logger.info(
                "Ordem salva no banco de dados: %s", order.get("id", "N/A")
            )
        except Exception as exc:
            self.logger.error("Erro ao salvar ordem no banco de dados: %s", str(exc))

    async def _calculate_all_indicators(
        self, symbol: str, indicator: TechnicalIndicator
    ) -> Dict[str, Any]:
        """Calcula todos os indicadores para um símbolo."""
        return {
            "rsi": indicator.calculate_rsi(symbol),
            # "macd": indicator.calculate_macd(symbol),
            "adx": indicator.calculate_adx(symbol),
            # "wma": indicator.calculate_wma(symbol),
            "sma": indicator.calculate_sma(symbol),
            "chop": indicator.calculate_chop(symbol),
            "atr": indicator.calculate_atr(symbol),
        }


async def sync_orders(self, client: OKXClient, symbol: str):
    """
    Sincroniza ordens locais com ordens da exchange para garantir consistência.

    Args:
        client: Instância de OKXClient para interagir com a exchange
        symbol: Símbolo do par de trading para sincronizar ordens
    """
    exchange_orders = await client.exchange.fetch_open_orders(symbol)
    local_orders = self.db.get_open_orders(symbol)  # Assuming TradingDatabase

    for order in exchange_orders:
        if order["id"] not in [o["id"] for o in local_orders]:
            self.db.add_order(order)  # Update local DB

    self.logger.debug("Ordens sincronizadas para %s", symbol)


async def initialize_bot_components() -> Tuple[OKXClient, OKXOrder, TradingBot]:
    """Inicializa componentes do bot."""
    try:
        config = BotConfig()
        client = OKXClient(config)
        # Ensure TRADING_SYMBOLS from config are used in client
        client.config.TRADING_SYMBOLS = config.TRADING_SYMBOLS
        order_manager = OKXOrder(client)
        bot = TradingBot(config)
        return client, order_manager, bot
    except Exception as exc:
        logger = TradingLogger.get_logger(__name__)
        logger.error("Falha ao inicializar componentes do bot: %s", str(exc))
        raise ConfigurationError("Falha na inicialização do bot") from exc


async def send_startup_notification(client: OKXClient, bot: TradingBot) -> None:
    """Envia notificação de inicialização."""
    if not bot.config.ENABLE_TELEGRAM_NOTIFICATIONS:
        return

    try:
        balance = client.get_balance()
        message = await build_startup_message(balance, bot.config)
        await bot.send_telegram_notification(message)
    except Exception as exc:
        logger = TradingLogger.get_logger(__name__)
        logger.error("Erro ao enviar notificação de inicialização: %s", str(exc))


async def build_startup_message(balance: Optional[Dict], config: BotConfig) -> str:
    """Constrói mensagem de inicialização."""
    mode_text = "🟢 LIVE TRADING" if not config.SANDBOX_MODE else "🔴 SANDBOX MODE"
    message = f"""🚀 * {config.SCRIPT_NAME} Iniciado*
-----------------------------
• *Mode*: {mode_text}
• *Balance*:
💰 Available Balance:
"""

    if balance and balance.get("total"):
        for asset, amount in balance["total"].items():
            if amount > 0:
                message += f" 🪙 {asset}: {amount:.8f}\n"

    return message


async def main() -> None:
    """Função principal com auto-trading implementado."""
    logger = TradingLogger.get_logger(__name__)

    def handle_sigstp(signum, frame):
        logger.info("Bot interrompido manualmente pelo usuário (SIGTSTP).")
        print("\n🛑 Bot interrompido manualmente (Ctrl+Z).")
        print("A finalizar o bot. Até breve!")
        exit(0)

    try:
        client, order_manager, bot = await initialize_bot_components()
        await send_startup_notification(client, bot)
        await bot.play_alert("start", volume=0.25)

        # Configura o tratamento de SIGTSTP (Ctrl+Z)
        signal.signal(signal.SIGTSTP, handle_sigstp)

        indicator = TechnicalIndicator(client)

        # Inicializar dados históricos
        for symbol in client.config.TRADING_SYMBOLS:
            ohlcv_data = indicator.fetch_historical_data(
                symbol, client.config.TIMEFRAME
            )

        cycle_count = 0

        while True:
            cycle_count += 1
            if cycle_count == 1:
                client._print_mode_message()
            print(
                f"\n🔄 Ciclo #{cycle_count} - {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )

            client.display_prices_with_precision()
            client.display_balance_with_precision()

            # Verificar ordens fechadas e enviar notificações
            for symbol in client.config.TRADING_SYMBOLS:
                try:
                    closed_orders = client.exchange.fetch_closed_orders(symbol, limit=5)
                    # Atualizar o cache com ordens fechadas
                    open_orders = client.exchange.fetch_open_orders(symbol)
                    bot.order_cache.update_orders(symbol, open_orders, closed_orders)
                    # logger.info("Cache de ordens atualizado para %s", symbol)

                    for order in closed_orders:
                        order_id = order.get("id", "N/A")
                        # Verificar se a ordem já foi notificada para evitar duplicatas
                        if not bot.db.is_order_notified(order_id):
                            if (
                                order.get("side") == "sell"
                                and order.get("status") == "closed"
                            ):
                                buy_order = bot.db.get_matching_buy_order(
                                    order_id, symbol
                                )
                                if buy_order:
                                    try:
                                        buy_price = float(
                                            buy_order.get("price", 0.0) or 0.0
                                        )
                                        sell_price = float(
                                            order.get("price", 0.0) or 0.0
                                        )
                                        amount = min(
                                            float(buy_order.get("amount", 0.0) or 0.0),
                                            float(order.get("amount", 0.0) or 0.0),
                                        )
                                        pnl = (sell_price - buy_price) * amount
                                        # Atualizar PnL no banco de dados para a ordem de venda
                                        order_with_pnl = order.copy()
                                        order_with_pnl["pnl"] = pnl
                                        bot.save_order_to_db(order_with_pnl)
                                        # Registrar no histórico de trades
                                        trade_record = {
                                            "symbol": symbol,
                                            "buy_order_id": buy_order.get("id"),
                                            "sell_order_id": order_id,
                                            "buy_price": buy_price,
                                            "sell_price": sell_price,
                                            "amount": amount,
                                            "pnl": pnl,
                                            "open_timestamp": buy_order.get(
                                                "timestamp"
                                            ),
                                        }
                                        with sqlite3.connect(bot.db.db_path) as conn:
                                            cursor = conn.cursor()
                                            cursor.execute(
                                                """
                                                INSERT INTO trade_history 
                                                (symbol, buy_order_id, sell_order_id, buy_price, sell_price, amount, pnl, open_timestamp)
                                                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                                                """,
                                                (
                                                    trade_record["symbol"],
                                                    trade_record["buy_order_id"],
                                                    trade_record["sell_order_id"],
                                                    trade_record["buy_price"],
                                                    trade_record["sell_price"],
                                                    trade_record["amount"],
                                                    trade_record["pnl"],
                                                    trade_record["open_timestamp"],
                                                ),
                                            )
                                            conn.commit()
                                            self.logger.debug(
                                                "Registro de trade salvo no histórico: %s",
                                                order_id,
                                            )

                                        if bot.metrics_calculator:
                                            metrics = bot.metrics_calculator.get_trade_metrics(
                                                symbol, buy_price, sell_price, amount
                                            )
                                            win_rate = metrics.get("win_rate", 0.0)
                                            message = f"""📉 *OCO \\- POSIÇÃO FECHADA \\- {symbol}*
• *Bot*: {bot.config.SCRIPT_NAME}
• *Tipo*: Venda
• *Quantidade*: {amount}
• *Preço de Compra*: ${buy_price:.2f}
• *Preço de Venda*: ${sell_price:.2f}
• *PnL*: ${pnl:.2f}
• *Taxa de Acerto*: {win_rate*100:.1f}%"""
                                        else:
                                            message = f"""📉 *OCO \\- POSIÇÃO FECHADA \\- {symbol}*
• *Bot*: {bot.config.SCRIPT_NAME}
• *Tipo*: Venda
• *Quantidade*: {amount}
• *Preço de Compra*: ${buy_price:.2f}
• *Preço de Venda*: ${sell_price:.2f}
• *PnL*: ${pnl:.2f}"""
                                        await bot.send_telegram_notification(message)
                                        await bot.play_alert(
                                            (
                                                "win"
                                                if sell_price > buy_price
                                                else "loose"
                                            ),
                                            volume=0.7,
                                        )
                                        bot.db.mark_order_as_notified(order_id)
                                    except (ValueError, TypeError) as e:
                                        logger.warning(
                                            "Erro ao processar ordem %s de %s: %s",
                                            order_id,
                                            symbol,
                                            str(e),
                                        )
                                        message = f"""📉 *OCO \\- POSIÇÃO FECHADA \\- {symbol}*
• *Bot*: {bot.config.SCRIPT_NAME}
• *Tipo*: Venda
• *Quantidade*: {order.get("amount", 0.0)}
• *Preço de Venda*: ${order.get("price", 0.0) if order.get("price") else 0.0:.2f}
• *Nota*: Erro ao processar dados"""
                                        await bot.send_telegram_notification(message)
                                        bot.db.mark_order_as_notified(order_id)
                                else:
                                    logger.warning(
                                        "Ordem de venda sem correspondência de compra para %s: ID=%s",
                                        symbol,
                                        order_id,
                                    )
                                    sell_price = float(order.get("price", 0.0) or 0.0)
                                    message = f"""📉 *OCO \\- POSIÇÃO FECHADA \\- {symbol}*
• *Bot*: {bot.config.SCRIPT_NAME}
• *Tipo*: Venda
• *Quantidade*: {order.get("amount", 0.0)}
• *Preço de Venda*: ${sell_price:.2f}
• *PnL*: N/A (Sem ordem de compra correspondente)
• *ID da Ordem*: {order_id}"""
                                    await bot.send_telegram_notification(message)
                                    bot.db.mark_order_as_notified(order_id)
                            elif (
                                order.get("status") == "closed"
                                and order.get("side") == "buy"
                            ):
                                message = f"""📈 *OCO \\- POSIÇÃO ABERTA \\- {symbol}*
• *Bot*: {bot.config.SCRIPT_NAME}
• *Tipo*: Compra
• *Quantidade*: {order.get("amount", 0.0)}
• *Preço*: ${order.get("price", 0.0):.2f}"""
                                await bot.send_telegram_notification(message)
                                bot.db.mark_order_as_notified(order_id)
                except Exception as exc:
                    logger.error(
                        "Erro ao verificar ordens fechadas para %s: %s",
                        symbol,
                        str(exc),
                    )

            # ====== NOVA SEÇÃO: AUTO-TRADING ======
            print("\n🤖 Verificando Sinais de Trading:")
            print("=" * 50)

            checker = SinaisWmaRsi(indicator, client)
            # Obter tickers de uma vez para todos os símbolos para otimizar
            tickers = client.exchange.fetch_tickers(client.config.TRADING_SYMBOLS)

            for symbol in client.config.TRADING_SYMBOLS:
                try:
                    # Verificar sinal de entrada
                    has_buy_signal = checker.check_entry_signal(
                        symbol, timeframe=None, tickers=tickers
                    )

                    # Exibir sinal atual
                    checker.print_signal(symbol, timeframe=None, tickers=tickers)

                    # NOVA LÓGICA: Verificar trailing stops antes de processar sinal
                    if order_manager.has_active_oco_orders(symbol):
                        # print(
                        #     f"⏳ {symbol}: Trailing stop ativo detectado. Aguardando fechamento..."
                        # )
                        continue  # Pula para o próximo símbolo

                    # Se há sinal de compra, processar ordem
                    if has_buy_signal:
                        print(f"\n🚨 SINAL DE COMPRA DETECTADO PARA {symbol}!")

                        # Validar condições antes de colocar ordem (já inclui verificação de trailing stop)
                        if order_manager.validate_order_conditions(symbol):
                            print(f"✅ Condições validadas para {symbol}")

                            # Colocar ordem
                            order_result = order_manager.place_buy_order_with_oco(
                                symbol=symbol, indicator=indicator
                            )

                            if order_result:
                                print(f"🎯 ORDEM Stop Loss EXECUTADA COM SUCESSO!")
                                print(f"• Símbolo: {symbol}")
                                print(f"• Quantidade: {order_result['amount']}")
                                print(f"• Preço: ${order_result['entry_price']}")
                                print(
                                    f"• Valor usado: ${order_result['ten_percent_used']:.2f}"
                                )
                                print(
                                    f"• ID da ordem: {order_result['limit_order'].get('id', 'N/A')}"
                                )

                                # Salvar no banco de dados
                                bot.save_order_to_db(order_result["limit_order"])
                                bot.created_orders.append(order_result["limit_order"])

                                # Notificações
                                await bot.play_alert("success", volume=0.7)

                                message = f"""🎯 *ORDEM Stop Loss EXECUTADA*
            • *Bot*: {bot.config.SCRIPT_NAME}
            • *Símbolo*: {symbol}
            • *Tipo*: OCO (Limit)
            • *Quantidade*: {order_result['amount']}
            • *Preço*: ${order_result['entry_price']}
            • *Valor*: ${order_result['ten_percent_used']:.2f}
            """
                                await bot.send_telegram_notification(message)

                            else:
                                print(f"❌ Falha ao executar ordem para {symbol}")
                                await bot.play_alert("error", volume=0.5)
                        else:
                            print(f"⚠️ Condições não atendidas para {symbol}")
                            # print(
                            #     f"    (Pode haver ordens ativas ou saldo insuficiente)"
                            # )
                    else:
                        logger.info(
                            f"OCO | {symbol} -> Wait"
                        )  # Assuming logger supports color formatting
                        print()

                except Exception as exc:
                    logger.error("Erro ao processar %s: %s", symbol, str(exc))
                    print(f"❌ Erro ao processar {symbol}: {exc}")

            print(f"\n⏰ Aguardando próximo ciclo (60 segundos)...")
            await asyncio.sleep(60)

    except KeyboardInterrupt:
        logger.info("Bot interrompido manualmente pelo usuário.")
        print("\n🛑 Bot interrompido manualmente.")
        if "bot" in locals():
            await bot.play_alert("exit", volume=0.9)
        print("A finalizar o bot. Até breve!")
        return

    except (ConfigurationError, ExchangeConnectionError) as exc:
        logger.error("Erro de configuração/conexão: %s", exc)
        print(f"❌ Erro: {exc}")
        print("Por favor, verifique as suas credenciais e configuração.")

    except Exception as exc:
        logger.error("Erro inesperado: %s", exc)
        logger.exception(exc)
        print(f"❌ Erro inesperado: {exc}")
        print("Verifique os logs para mais detalhes.")


if __name__ == "__main__":
    asyncio.run(main())
