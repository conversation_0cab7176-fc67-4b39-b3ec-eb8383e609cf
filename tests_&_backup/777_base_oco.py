"""
Bot de trading automatizado para OKX Exchange.
"""

import warnings

warnings.filterwarnings("ignore", message="pkg_resources is deprecated as an API")

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
import os
import shutil
import numpy as np
import json
import ccxt
import talib
import pandas as pd
import signal
import uuid
from dataclasses import dataclass
from decimal import Decimal, ROUND_HALF_UP
from dotenv import load_dotenv
from indicators.indicators import TechnicalIndicator
from service.service_alerts import SoundNotifications
from service.service_telegram import TelegramNotifier
from typing import Any, Dict, List, Optional, Tuple
from utils.logger import TradingLogger
from utils.database import TradingDatabase
from utils.exceptions import ConfigurationError, ExchangeConnectionError
from utils.check_orders import (
    check_regular_orders,
    display_regular_orders,
    check_oco_orders,
    display_oco_orders,
)
from utils.error_handler import <PERSON>rror<PERSON><PERSON><PERSON>
from utils.order_validator import OrderValidator
from tabulate import tabulate

load_dotenv()
FIAT_CURRENCIES = ["EUR", "USD"]


@dataclass
class BotConfig:
    """Configurações centralizadas para o bot de trading."""

    SANDBOX_MODE: bool = True

    TRADING_SYMBOLS = ["BTC/USDC", "ETH/USDC", "SOL/USDC"]
    TIMEFRAME = "15m"

    RSI_PERIOD: int = 30
    WMA_PERIOD: int = 84
    # OHLCV_LIMIT: int = 100
    ATR_SL_FACTOR: float = 1.5
    ATR_TP_FACTOR: float = 2.0
    # Configurações para gestão dinâmica de posição baseada em volatilidade
    ENABLE_DYNAMIC_POSITION_SIZING: bool = True
    ENABLE_DYNAMIC_ATR_MULTIPLIERS: bool = True
    POSITION_SIZE_BASE: float = 0.1  # 10% do saldo por padrão
    POSITION_SIZE_MIN: float = 0.05  # 5% mínimo em alta volatilidade
    POSITION_SIZE_MAX: float = 0.15  # 15% máximo em baixa volatilidade
    ATR_SL_FACTOR_MIN: float = 1.2  # Multiplicador SL mínimo
    ATR_SL_FACTOR_MAX: float = 2.0  # Multiplicador SL máximo
    ATR_TP_FACTOR_MIN: float = 1.8  # Multiplicador TP mínimo
    ATR_TP_FACTOR_MAX: float = 3.0  # Multiplicador TP máximo
    ATR_LOOKBACK_PERIODS: int = 20  # Períodos para média histórica de ATR
    VOLATILITY_THRESHOLD_HIGH: float = (
        1.2  # Fator para alta volatilidade (ATR atual / média)
    )
    VOLATILITY_THRESHOLD_LOW: float = (
        0.8  # Fator para baixa volatilidade (ATR atual / média)
    )
    STABLECOINS: List[str] = None
    FIAT_DECIMAL_PLACES: int = 2
    CRYPTO_DECIMAL_PLACES: int = 8
    ENABLE_SOUND_NOTIFICATIONS: bool = True
    ENABLE_TELEGRAM_NOTIFICATIONS: bool = True

    SCRIPT_NAME: str = os.path.splitext(os.path.basename(__file__))[0].replace("_", " ")

    def __post_init__(self):
        """Inicializa valores padrão após criação."""
        if self.TRADING_SYMBOLS is None:
            self.TRADING_SYMBOLS = BotConfig.SYMBOLS
        if self.STABLECOINS is None:
            self.STABLECOINS = ["USDC", "USDT", "BUSD", "FDUSD"]
        self._initialize_defaults()

    def _initialize_defaults(self):
        """Inicializa valores padrão de OKXClient para atributos não definidos."""
        from core.okx_client import OKXClient

        default_config = OKXClient().config
        # Obtém todos os atributos disponíveis no DefaultConfig
        default_attrs = [attr for attr in dir(default_config) if attr.isupper()]
        for attr in default_attrs:
            if (
                not hasattr(self, attr)
                or getattr(self, attr) is None
                or (
                    isinstance(getattr(self, attr), (int, float))
                    and getattr(self, attr) == 0
                )
            ):
                default_val = getattr(default_config, attr, None)
                if default_val is not None:
                    setattr(self, attr, default_val)
                    # print(f"Atributo {attr} inicializado com valor padrão: {default_val}")
        # Comentado o código de cache de ordens para evitar erro de 'cache_duration' não definido
        """
        Inicializa o cache de ordens.
        
        Args:
            cache_duration: Duração em segundos antes de atualizar o cache.
        """
        # self.cache_duration = cache_duration
        # self.open_orders: Dict[str, List[Dict]] = {}
        # self.closed_orders: Dict[str, List[Dict]] = {}
        # self.last_updated: Dict[str, float] = {}

    @classmethod
    def load_config(cls, file_path: str = "config.json") -> "BotConfig":
        """Carrega a configuração de um arquivo JSON."""
        try:
            with open(file_path, "r") as f:
                config_data = json.load(f)
            return cls(**config_data)
        except Exception as e:
            print(f"Falha ao carregar configuração: {e}")
            return cls()


from core.okx_client import OKXClient


class OKXOrder:
    """Classe para gerenciar operações de ordens na exchange OKX."""

    def __init__(self, client: OKXClient):
        self.client = client
        self.logger = TradingLogger.get_logger(__name__)
        self.error_handler = ErrorHandler(logger_name=__name__)
        self.validator = OrderValidator(client)

    def validate_order_conditions(self, symbol: str) -> bool:
        """
        Valida se as condições estão adequadas para colocar uma ordem.
        Inclui verificação de ordens tp/sl (oco).

        Args:
            symbol: Par de trading

        Returns:
            True se condições estão OK, False caso contrário
        """
        return self.validator.validate_order_conditions(symbol, strategy_type="oco")

    def _generate_client_order_id(self) -> str:
        """Gera um ID único para a ordem."""
        return uuid.uuid4().hex[:32]  # Máximo 32 caracteres para OKX

    def has_active_oco_orders(self, symbol: str) -> bool:
        """
        Verifica especificamente se há ordens tp/sl (oco) ativas.

        Args:
            symbol: Par de trading

        Returns:
            True se há trailing stop ativo, False caso contrário
        """
        try:
            trailing_orders = check_oco_orders(self.client, symbol)
            active_count = len(trailing_orders)

            if active_count > 0:
                self.logger.info(
                    "Encontradas %d ordens tp/sl (oco) para %s",
                    active_count,
                    symbol,
                )
                # Log das ordens ativas para debugging
                for order in trailing_orders:
                    order_id = order.get("id", "N/A")
                    status = order.get("status", "N/A")
                    self.logger.debug(
                        "Trailing stop ativo: ID=%s, Status=%s", order_id, status
                    )
                return True

            return False

        except Exception as exc:
            self.logger.error(
                "Erro ao verificar trailing stops para %s: %s", symbol, str(exc)
            )
            return (
                True  # Em caso de erro, assume que há ordens ativas para ser cauteloso
            )

    def place_buy_order_with_oco(
        self, symbol: str, indicator: TechnicalIndicator, timeframe: str = "15m"
    ) -> Optional[Dict]:
        """
        Coloca ordem de compra com OCO (One Cancels the Other) usando 10% do saldo.
        O Stop Loss (SL) é definido como 1.75x o ATR abaixo do preço de entrada.
        O Take Profit (TP) é definido como 1.75x o ATR acima do preço de entrada.
        """
        try:
            existing_orders = check_oco_orders(self.client, symbol)
            if existing_orders:
                self.logger.info(
                    "Já existem ordens abertas para %s, pulando criação", symbol
                )
                return None
            best_bid = self.client.get_best_bid(symbol)
            if not best_bid:
                self.logger.error("Não foi possível obter melhor bid para %s", symbol)
                return None
            entry_price = best_bid
            self.logger.info("Melhor bid obtido para %s: %s", symbol, entry_price)
            balance = self.client.get_balance()
            quote_currency = symbol.split("/")[1]
            available_balance = balance["total"].get(quote_currency, 0)
            if available_balance <= 0:
                self.logger.error(
                    "Saldo insuficiente em %s para %s", quote_currency, symbol
                )
                return None
            # Ajuste dinâmico do tamanho da posição baseado em volatilidade
            position_size = 0.1  # Valor padrão
            if (
                hasattr(self.client.config, "ENABLE_DYNAMIC_POSITION_SIZING")
                and self.client.config.ENABLE_DYNAMIC_POSITION_SIZING
            ):
                atr = indicator.calculate_atr(symbol, timeframe, period=14)
                if atr is not None:
                    # Calcular média histórica de ATR para comparação
                    lookback_periods = getattr(
                        self.client.config, "ATR_LOOKBACK_PERIODS", 20
                    )
                    ohlcv_data = indicator.fetch_historical_data(
                        symbol, timeframe, limit=lookback_periods + 14
                    )
                    if ohlcv_data and len(ohlcv_data) > 14:
                        atr_values = []
                        for i in range(len(ohlcv_data) - 14, len(ohlcv_data)):
                            high = np.array(
                                [candle[2] for candle in ohlcv_data[i - 14 : i]]
                            )
                            low = np.array(
                                [candle[3] for candle in ohlcv_data[i - 14 : i]]
                            )
                            close = np.array(
                                [candle[4] for candle in ohlcv_data[i - 14 : i]]
                            )
                            atr_val = talib.ATR(high, low, close, timeperiod=14)[-1]
                            atr_values.append(atr_val)
                        atr_mean = np.mean(atr_values) if atr_values else atr
                        volatility_ratio = atr / atr_mean if atr_mean > 0 else 1.0
                        self.logger.info(
                            "Volatilidade para %s: ATR atual=%.2f, ATR médio=%.2f, Razão=%.2f",
                            symbol,
                            atr,
                            atr_mean,
                            volatility_ratio,
                        )

                        high_threshold = getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_HIGH", 1.2
                        )
                        low_threshold = getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_LOW", 0.8
                        )
                        if volatility_ratio > high_threshold:
                            position_size = getattr(
                                self.client.config, "POSITION_SIZE_MIN", 0.05
                            )
                            self.logger.info(
                                "Alta volatilidade detectada para %s, reduzindo tamanho da posição para %.1f%%",
                                symbol,
                                position_size * 100,
                            )
                        elif volatility_ratio < low_threshold:
                            position_size = getattr(
                                self.client.config, "POSITION_SIZE_MAX", 0.15
                            )
                            self.logger.info(
                                "Baixa volatilidade detectada para %s, aumentando tamanho da posição para %.1f%%",
                                symbol,
                                position_size * 100,
                            )
                        else:
                            position_size = getattr(
                                self.client.config, "POSITION_SIZE_BASE", 0.1
                            )
                            self.logger.info(
                                "Volatilidade normal para %s, tamanho da posição padrão %.1f%%",
                                symbol,
                                position_size * 100,
                            )

                        # Notificação de ajuste de volatilidade
                        if (
                            hasattr(self.client.config, "ENABLE_TELEGRAM_NOTIFICATIONS")
                            and self.client.config.ENABLE_TELEGRAM_NOTIFICATIONS
                            and volatility_ratio > high_threshold
                        ):
                            message = f"⚠️ *Alta Volatilidade Detectada - {symbol}*\n• Tamanho da posição reduzido para {position_size * 100:.1f}%\n• Razão ATR: {volatility_ratio:.2f}"
                            asyncio.run_coroutine_threadsafe(
                                bot.send_telegram_notification(message),
                                asyncio.get_event_loop(),
                            )
                    else:
                        self.logger.warning(
                            "Dados históricos insuficientes para calcular média ATR de %s, usando tamanho padrão",
                            symbol,
                        )
                else:
                    self.logger.warning(
                        "Não foi possível calcular ATR para %s, usando tamanho padrão",
                        symbol,
                    )
            else:
                position_size = 0.1  # Padrão fixo se desativado

            balance_to_use = available_balance * position_size
            ten_percent_balance = balance_to_use  # Definindo o valor usado como 10% do saldo ajustado pelo position_size
            amount = balance_to_use / entry_price
            formatted_amount = self.client.format_amount_with_precision(symbol, amount)
            formatted_price = self.client.format_price_with_precision(
                symbol, entry_price
            )
            if float(formatted_amount) <= 0:
                self.logger.error(
                    "Quantidade calculada insuficiente para %s: %s",
                    symbol,
                    formatted_amount,
                )
                return None
            self.logger.info(
                "Calculado para %s - Quantidade: %s, Preço: %s",
                symbol,
                formatted_amount,
                formatted_price,
            )
            # Calcular ATR
            atr = indicator.calculate_atr(symbol, timeframe, period=14)
            if atr is None:
                self.logger.error("Não foi possível calcular ATR para %s", symbol)
                return None
            # Ajuste dinâmico dos multiplicadores de ATR para SL e TP baseado em volatilidade
            atr_sl_factor = getattr(self.client.config, "ATR_SL_FACTOR", 1.5)
            atr_tp_factor = getattr(self.client.config, "ATR_TP_FACTOR", 2.0)
            if (
                hasattr(self.client.config, "ENABLE_DYNAMIC_ATR_MULTIPLIERS")
                and self.client.config.ENABLE_DYNAMIC_ATR_MULTIPLIERS
            ):
                if atr is not None:
                    # Calcular média histórica de ATR para comparação
                    lookback_periods = getattr(
                        self.client.config, "ATR_LOOKBACK_PERIODS", 20
                    )
                    ohlcv_data = indicator.fetch_historical_data(
                        symbol, timeframe, limit=lookback_periods + 14
                    )
                    if ohlcv_data and len(ohlcv_data) > 14:
                        atr_values = []
                        for i in range(len(ohlcv_data) - 14, len(ohlcv_data)):
                            high = np.array(
                                [candle[2] for candle in ohlcv_data[i - 14 : i]]
                            )
                            low = np.array(
                                [candle[3] for candle in ohlcv_data[i - 14 : i]]
                            )
                            close = np.array(
                                [candle[4] for candle in ohlcv_data[i - 14 : i]]
                            )
                            atr_val = talib.ATR(high, low, close, timeperiod=14)[-1]
                            atr_values.append(atr_val)
                        atr_mean = np.mean(atr_values) if atr_values else atr
                        volatility_ratio = atr / atr_mean if atr_mean > 0 else 1.0

                        high_threshold = getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_HIGH", 1.2
                        )
                        low_threshold = getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_LOW", 0.8
                        )
                        if volatility_ratio > high_threshold:
                            atr_sl_factor = getattr(
                                self.client.config, "ATR_SL_FACTOR_MAX", 2.0
                            )
                            atr_tp_factor = getattr(
                                self.client.config, "ATR_TP_FACTOR_MAX", 3.0
                            )
                            self.logger.info(
                                "Alta volatilidade detectada para %s, aumentando multiplicadores SL/TP para %.1f/%.1f",
                                symbol,
                                atr_sl_factor,
                                atr_tp_factor,
                            )
                        elif volatility_ratio < low_threshold:
                            atr_sl_factor = getattr(
                                self.client.config, "ATR_SL_FACTOR_MIN", 1.2
                            )
                            atr_tp_factor = getattr(
                                self.client.config, "ATR_TP_FACTOR_MIN", 1.8
                            )
                            self.logger.info(
                                "Baixa volatilidade detectada para %s, reduzindo multiplicadores SL/TP para %.1f/%.1f",
                                symbol,
                                atr_sl_factor,
                                atr_tp_factor,
                            )
                        else:
                            atr_sl_factor = getattr(
                                self.client.config, "ATR_SL_FACTOR", 1.5
                            )
                            atr_tp_factor = getattr(
                                self.client.config, "ATR_TP_FACTOR", 2.0
                            )
                            self.logger.info(
                                "Volatilidade normal para %s, multiplicadores SL/TP padrão %.1f/%.1f",
                                symbol,
                                atr_sl_factor,
                                atr_tp_factor,
                            )
                    else:
                        self.logger.warning(
                            "Dados históricos insuficientes para calcular média ATR de %s, usando multiplicadores padrão",
                            symbol,
                        )
                else:
                    self.logger.warning(
                        "Não foi possível calcular ATR para %s, usando multiplicadores padrão",
                        symbol,
                    )

            # Calcular Stop Loss (SL) e Take Profit (TP) com base no ATR ajustado
            sl_trigger = entry_price - (atr * atr_sl_factor)
            sl_price = sl_trigger
            tp_trigger = entry_price + (atr * atr_tp_factor)
            tp_price = tp_trigger
            formatted_sl_trigger = self.client.format_price_with_precision(
                symbol, sl_trigger
            )
            formatted_sl_price = self.client.format_price_with_precision(
                symbol, sl_price
            )
            formatted_tp_trigger = self.client.format_price_with_precision(
                symbol, tp_trigger
            )
            formatted_tp_price = self.client.format_price_with_precision(
                symbol, tp_price
            )
            self.logger.info(
                "Parâmetros OCO calculados para %s: SL Trigger=%s, SL Price=%s, TP Trigger=%s, TP Price=%s (ATR=%.2f)",
                symbol,
                formatted_sl_trigger,
                formatted_sl_price,
                formatted_tp_trigger,
                formatted_tp_price,
                atr,
            )
            # Definir parâmetros da ordem OCO
            order_params = {
                "orderType": "oco",
                "postOnly": False,
                "stopLoss": {
                    "triggerPrice": formatted_sl_trigger,
                    "price": formatted_sl_price,
                },
                "takeProfit": {
                    "triggerPrice": formatted_tp_trigger,
                    "price": formatted_tp_price,
                },
            }
            limit_order = self.client.exchange.create_order(
                symbol=symbol,
                type="limit",
                side="buy",
                amount=float(formatted_amount) if formatted_amount is not None else 0.0,
                price=entry_price,
                params=order_params,
            )
            if "amount" not in limit_order or limit_order["amount"] is None:
                limit_order["amount"] = float(formatted_amount)
            self.logger.info(
                "Ordem limite com OCO criada com sucesso: %s", limit_order.get("id")
            )
            # Adicionar informações OCO ao campo additional_info para armazenamento no banco de dados
            oco_info = {
                "oco_details": {
                    "sl_trigger": float(formatted_sl_trigger),
                    "sl_price": float(formatted_sl_price),
                    "tp_trigger": float(formatted_tp_trigger),
                    "tp_price": float(formatted_tp_price),
                }
            }
            if "info" not in limit_order:
                limit_order["info"] = {}
            limit_order["info"].update(oco_info)
            result = {
                "limit_order": limit_order,
                "entry_price": float(formatted_price),
                "amount": float(formatted_amount),
                "symbol": symbol,
                "ten_percent_used": ten_percent_balance,
                "sl_trigger": float(formatted_sl_trigger),
                "sl_price": float(formatted_sl_price),
                "tp_trigger": float(formatted_tp_trigger),
                "tp_price": float(formatted_tp_price),
            }
            self.logger.info("Ordem OCO processada com sucesso para %s", symbol)
            return result
        except Exception as exc:
            self.logger.error("Erro ao colocar ordem OCO para %s: %s", symbol, str(exc))
            return None

    def log_order_prevention_details(self, symbol: str) -> None:
        """
        Registra detalhes sobre prevenção de criação de ordens.

        Args:
            symbol: Par de trading
        """
        try:
            trailing_orders = check_oco_orders(self.client, symbol)

            if trailing_orders:
                self.logger.info("=== PREVENÇÃO DE ORDEM ATIVADA ===")
                self.logger.info("Símbolo: %s", symbol)
                self.logger.info("Trailing stops ativos: %d", len(trailing_orders))

                for i, order in enumerate(trailing_orders, 1):
                    self.logger.info("Trailing Stop #%d:", i)
                    self.logger.info("  - ID: %s", order.get("id", "N/A"))
                    self.logger.info("  - Status: %s", order.get("status", "N/A"))
                    self.logger.info("  - Lado: %s", order.get("side", "N/A"))
                    self.logger.info("  - Quantidade: %s", order.get("amount", "N/A"))
                    self.logger.info(
                        "  - Trigger: %s", order.get("triggerPrice", "N/A")
                    )

                self.logger.info("Nova ordem BLOQUEADA para %s", symbol)
                self.logger.info("=====================================")

        except Exception as exc:
            self.logger.error("Erro ao registrar detalhes de prevenção: %s", str(exc))


class Sinais:
    """
    Classe para verificar sinais de entrada baseados em WMA 50 e RSI 14.
    """

    def __init__(self, indicator: TechnicalIndicator, client):
        self.indicator = indicator
        self.client = client
        self.logger = TradingLogger.get_logger(__name__)
        self.error_handler = ErrorHandler(logger_name=__name__)

    def check_entry_signal(
        self,
        symbol: str,
        timeframe: str = None,
        tickers: Optional[Dict] = None,
    ) -> bool:
        """
        Sinal de compra: RSI(14) > 50 e close > WMA(50)
        """
        if timeframe is None:
            timeframe = self.client.config.TIMEFRAME
        try:
            if tickers and symbol in tickers:
                ticker = tickers[symbol]
            else:
                ticker = self.client.get_ticker(symbol)
            if not ticker or "last" not in ticker:
                self.logger.error(f"Sem dados de ticker para {symbol}")
                return False

            price = ticker["last"]
            rsi = self.indicator.calculate_rsi(
                symbol, timeframe, period=self.client.config.RSI_PERIOD
            )
            wma_period = getattr(self.client.config, "WMA_PERIOD", 84)
            wma = self.indicator.calculate_wma(symbol, timeframe, period=wma_period)

            if rsi is None or wma is None:
                self.logger.debug(f"Dados de indicador ausentes para {symbol}")
                return False

            if rsi > 50 and price > wma:
                self.logger.info(f"Sinal de entrada detectado para {symbol}")
                return True

            return False

        except Exception as exc:
            self.logger.error(f"Erro ao verificar sinal para {symbol}: {exc}")
            return False

    def print_signal(
        self,
        symbol: str,
        timeframe: str = None,
        tickers: Optional[Dict] = None,
    ) -> None:
        """
        Imprime o sinal de trading: BUY ou HOLD, usando WMA 50 e RSI 14.
        """
        if timeframe is None:
            timeframe = self.client.config.TIMEFRAME
        try:
            if tickers and symbol in tickers:
                ticker = tickers[symbol]
            else:
                ticker = self.client.get_ticker(symbol)
            if not ticker or "last" not in ticker:
                print(f"\033[91m • {symbol}: Dados de preço indisponíveis\033[0m")
                return

            price = ticker["last"]
            rsi = self.indicator.calculate_rsi(
                symbol, timeframe, period=self.client.config.RSI_PERIOD
            )
            wma_period = getattr(self.client.config, "WMA_PERIOD", 84)
            wma = self.indicator.calculate_wma(symbol, timeframe, period=wma_period)

            if rsi is None or wma is None:
                print(f"\033[91m • {symbol}: Indicadores indisponíveis\033[0m")
                return

            if rsi > 50 and price > wma:
                signal = "BUY"
                color = "\033[92m"
            else:
                signal = "HOLD"
                color = "\033[93m"

            print()
            print(f"{color} • {symbol}: SINAL = {signal}\033[0m")
            print(f"   Preço: ${price:.2f}")
            # print(f"   RSI: {rsi:.2f}")
            # print(f"   WMA: ${wma:.2f}")
            print()

        except Exception as exc:
            self.logger.error(f"Erro ao imprimir sinal para {symbol}: {exc}")


class TradingBot:
    """Bot de trading principal."""

    def __init__(self, config: Optional[BotConfig] = None):
        self.config = config or BotConfig()
        self.logger = TradingLogger.get_logger(__name__)
        self.error_handler = ErrorHandler(logger_name=__name__)
        self.sound_alert = self._initialize_sound_notifications()
        self.notifier = self._initialize_telegram_notifier()
        self.created_orders = []
        self.db = self._initialize_database()
        self.invested_values = {symbol: 0.0 for symbol in self.config.TRADING_SYMBOLS}
        self.realized_profits = {symbol: 0.0 for symbol in self.config.TRADING_SYMBOLS}

    def _initialize_sound_notifications(self) -> Optional[SoundNotifications]:
        """Inicializa notificações sonoras se habilitadas."""
        if self.config.ENABLE_SOUND_NOTIFICATIONS:
            try:
                return SoundNotifications()
            except Exception as exc:
                self.logger.warning(
                    "Falha ao inicializar notificações sonoras: %s", str(exc)
                )
        return None

    def _initialize_telegram_notifier(self) -> Optional[TelegramNotifier]:
        """Inicializa notificador Telegram se habilitado."""
        if self.config.ENABLE_TELEGRAM_NOTIFICATIONS:
            try:
                return TelegramNotifier()
            except Exception as exc:
                self.logger.warning(
                    "Falha ao inicializar notificador Telegram: %s", str(exc)
                )
        return None

    def _initialize_database(self) -> TradingDatabase:
        """Inicializa o banco de dados para persistência de ordens."""
        db_name = "sandbox_trades.db" if self.config.SANDBOX_MODE else "live_trades.db"
        db_path = os.path.join("trades", db_name)
        try:
            db = TradingDatabase(db_path)
            # Criar tabela para rastrear notificações de ordens, se não existir
            db.create_notified_orders_table()
            return db
        except Exception as exc:
            self.logger.error("Falha ao inicializar banco de dados: %s", str(exc))
            raise

    async def play_alert(self, alert_type: str, volume: float = 0.5) -> None:
        """Toca som de alerta se disponível."""
        if self.sound_alert:
            try:
                self.sound_alert.play_notification(alert_type, volume=volume)
            except Exception as exc:
                self.logger.warning("Falha ao reproduzir alerta: %s", str(exc))

    async def send_telegram_notification(self, message: str) -> None:
        """Envia notificação Telegram se disponível."""
        if self.notifier:
            try:
                await self.notifier.send_message(message)
            except Exception as exc:
                self.logger.warning(
                    "Falha ao enviar notificação Telegram: %s", str(exc)
                )

    def save_order_to_db(self, order: Dict) -> None:
        """Salva uma ordem no banco de dados e atualiza valores investidos."""
        try:
            self.db.save_order(order)
            self.logger.info(
                "Ordem salva no banco de dados: %s", order.get("id", "N/A")
            )
        except Exception as exc:
            self.logger.error("Erro ao salvar ordem no banco de dados: %s", str(exc))

        # Atualiza valores investidos se for uma ordem de compra, independentemente de erro no banco de dados
        if order.get("side") == "buy" and "symbol" in order:
            symbol = order["symbol"]
            amount = float(order.get("amount", 0.0) or 0.0)
            price = float(order.get("price", 0.0) or 0.0)
            invested = amount * price
            self.invested_values[symbol] += invested
            self.logger.info(
                "Valor investido atualizado para %s: %s",
                symbol,
                self.invested_values[symbol],
            )

    async def _calculate_all_indicators(
        self, symbol: str, indicator: TechnicalIndicator
    ) -> Dict[str, Any]:
        """Calcula todos os indicadores para um símbolo."""
        return {
            "rsi": indicator.calculate_rsi(symbol),
            # "macd": indicator.calculate_macd(symbol),
            "adx": indicator.calculate_adx(symbol),
            # "wma": indicator.calculate_wma(symbol),
            "sma": indicator.calculate_sma(symbol),
            "chop": indicator.calculate_chop(symbol),
            "atr": indicator.calculate_atr(symbol),
        }

    def update_realized_profits(self, client: OKXClient) -> None:
        """Atualiza os lucros realizados com base nas ordens fechadas."""
        for symbol in self.config.TRADING_SYMBOLS:
            try:
                closed_orders = client.exchange.fetch_closed_orders(symbol)
                total_profit = 0.0
                for order in closed_orders:
                    if order.get("side") == "sell" and order.get("status") == "closed":
                        buy_order = self.db.get_matching_buy_order(
                            order.get("id"), symbol
                        )
                        if buy_order:
                            try:
                                buy_price = float(buy_order.get("price", 0.0) or 0.0)
                                sell_price = float(order.get("price", 0.0) or 0.0)
                                amount = min(
                                    float(buy_order.get("amount", 0.0) or 0.0),
                                    float(order.get("amount", 0.0) or 0.0),
                                )
                                profit = (sell_price - buy_price) * amount
                                total_profit += profit
                            except (ValueError, TypeError) as e:
                                self.logger.warning(
                                    "Erro ao calcular lucro para ordem %s de %s: %s",
                                    order.get("id", "N/A"),
                                    symbol,
                                    str(e),
                                )
                self.realized_profits[symbol] = total_profit
                self.logger.info(
                    "Lucro realizado atualizado para %s: %.2f$", symbol, total_profit
                )
            except Exception as exc:
                self.logger.error(
                    "Erro ao atualizar lucros realizados para %s: %s", symbol, str(exc)
                )

    def calculate_advanced_metrics(self, client: OKXClient) -> Dict[str, Any]:
        """Calcula métricas avançadas como Sharpe Ratio, Máximo Drawdown, Win Rate e Profit Factor."""
        metrics = {}
        for symbol in self.config.TRADING_SYMBOLS:
            try:
                # Obter ordens fechadas do banco de dados
                all_orders = self.db.get_orders(limit=1000)
                symbol_orders = [
                    o
                    for o in all_orders
                    if o.get("symbol") == symbol
                    and o.get("status", "")
                    and o.get("status", "").lower() == "closed"
                ]
                if not symbol_orders:
                    metrics[symbol] = {
                        "sharpe_ratio": 0.0,
                        "max_drawdown": 0.0,
                        "win_rate": 0.0,
                        "profit_factor": 0.0,
                    }
                    continue

                # Calcular retornos para Sharpe Ratio
                buy_orders = [
                    o
                    for o in symbol_orders
                    if o.get("side", "") and o.get("side", "").lower() == "buy"
                ]
                sell_orders = [
                    o
                    for o in symbol_orders
                    if o.get("side", "") and o.get("side", "").lower() == "sell"
                ]
                returns = []
                equity_curve = []
                total_equity = 0.0
                for sell_order in sorted(
                    sell_orders, key=lambda x: x.get("timestamp", 0)
                ):
                    sell_price = float(sell_order.get("price", 0.0))
                    sell_amount = float(sell_order.get("amount", 0.0))
                    buy_amount_total = 0.0
                    buy_cost_total = 0.0
                    for buy_order in sorted(
                        buy_orders, key=lambda x: x.get("timestamp", 0)
                    ):
                        if (
                            buy_order.get("timestamp", 0)
                            < sell_order.get("timestamp", 0)
                            and buy_amount_total < sell_amount
                        ):
                            buy_amount = float(buy_order.get("amount", 0.0))
                            buy_price = float(buy_order.get("price", 0.0))
                            amount_to_use = min(
                                buy_amount, sell_amount - buy_amount_total
                            )
                            buy_amount_total += amount_to_use
                            buy_cost_total += amount_to_use * buy_price
                    if buy_amount_total > 0:
                        avg_buy_price = buy_cost_total / buy_amount_total
                        profit = (sell_price - avg_buy_price) * sell_amount
                        total_equity += profit
                        equity_curve.append(total_equity)
                        if len(equity_curve) > 1:
                            returns.append(
                                (equity_curve[-1] - equity_curve[-2]) / equity_curve[-2]
                                if equity_curve[-2] > 0
                                else 0.0
                            )

                # Sharpe Ratio (assumindo taxa livre de risco = 0 para simplificação)
                if returns:
                    mean_return = np.mean(returns)
                    std_return = np.std(returns) if len(returns) > 1 else 0.0
                    sharpe_ratio = (
                        mean_return / std_return * np.sqrt(252)
                        if std_return > 0
                        else 0.0
                    )  # Anualizado
                else:
                    sharpe_ratio = 0.0

                # Máximo Drawdown
                max_drawdown = 0.0
                peak = equity_curve[0] if equity_curve else 0.0
                for value in equity_curve:
                    if value > peak:
                        peak = value
                    drawdown = (peak - value) / peak if peak > 0 else 0.0
                    if drawdown > max_drawdown:
                        max_drawdown = drawdown

                # Win Rate e Profit Factor
                wins = sum(
                    1
                    for sell_order in sell_orders
                    for buy_order in buy_orders
                    if buy_order.get("timestamp", 0) < sell_order.get("timestamp", 0)
                    and float(sell_order.get("price", 0.0))
                    > float(buy_order.get("price", 0.0))
                )
                total_trades = len(sell_orders)
                win_rate = wins / total_trades * 100 if total_trades > 0 else 0.0

                gross_profit = sum(
                    (
                        float(sell_order.get("price", 0.0))
                        - float(buy_order.get("price", 0.0))
                    )
                    * float(sell_order.get("amount", 0.0))
                    for sell_order in sell_orders
                    for buy_order in buy_orders
                    if buy_order.get("timestamp", 0) < sell_order.get("timestamp", 0)
                    and float(sell_order.get("price", 0.0))
                    > float(buy_order.get("price", 0.0))
                )
                gross_loss = abs(
                    sum(
                        (
                            float(sell_order.get("price", 0.0))
                            - float(buy_order.get("price", 0.0))
                        )
                        * float(sell_order.get("amount", 0.0))
                        for sell_order in sell_orders
                        for buy_order in buy_orders
                        if buy_order.get("timestamp", 0)
                        < sell_order.get("timestamp", 0)
                        and float(sell_order.get("price", 0.0))
                        <= float(buy_order.get("price", 0.0))
                    )
                )
                profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0.0

                metrics[symbol] = {
                    "sharpe_ratio": sharpe_ratio,
                    "max_drawdown": max_drawdown * 100,  # Em porcentagem
                    "win_rate": win_rate,
                    "profit_factor": profit_factor,
                }

                self.logger.info(f"📊 Métricas Avançadas para {symbol}:")
                self.logger.info(f"• Sharpe Ratio: {sharpe_ratio:.2f}")
                self.logger.info(f"• Máximo Drawdown: {max_drawdown * 100:.2f}%")
                self.logger.info(f"• Win Rate: {win_rate:.2f}%")
                self.logger.info(f"• Profit Factor: {profit_factor:.2f}")
            except Exception as exc:
                self.logger.error(
                    f"Erro ao calcular métricas avançadas para {symbol}: {str(exc)}"
                )
                metrics[symbol] = {
                    "sharpe_ratio": 0.0,
                    "max_drawdown": 0.0,
                    "win_rate": 0.0,
                    "profit_factor": 0.0,
                }
        return metrics

    def display_invested_and_profits(self, client: OKXClient) -> None:
        """Exibe valores investidos e lucros realizados por símbolo."""
        print("\n💸 Valores Investidos e Lucros Realizados:")
        print("=" * 50)
        total_invested = 0
        total_realized_pnl = 0
        total_unrealized_pnl = 0

        for symbol in self.config.TRADING_SYMBOLS:
            # Obter ordens do banco de dados para cálculos mais precisos
            all_orders = self.db.get_orders(limit=1000)
            symbol_orders = [o for o in all_orders if o.get("symbol") == symbol]

            # Calcular valor investido (somente ordens de compra)
            buy_orders = [
                o
                for o in symbol_orders
                if o.get("side", "") and o.get("side", "").lower() == "buy"
            ]
            invested_amount = sum(
                float(o.get("amount", 0)) * float(o.get("price", 0))
                for o in buy_orders
                if o.get("amount") is not None and o.get("price") is not None
            )

            # Calcular lucro/prejuízo realizado (ordens fechadas de venda)
            sell_orders = [
                o
                for o in symbol_orders
                if o.get("side", "")
                and o.get("side", "").lower() == "sell"
                and o.get("status", "")
                and o.get("status", "").lower() == "closed"
            ]
            realized_pnl = 0
            used_buy_amounts = {o.get("id", "N/A"): 0 for o in buy_orders}
            for sell_order in sorted(sell_orders, key=lambda x: x.get("timestamp", 0)):
                sell_amount = (
                    float(sell_order.get("amount", 0))
                    if sell_order.get("amount") is not None
                    else 0.0
                )
                sell_price = (
                    float(sell_order.get("price", 0))
                    if sell_order.get("price") is not None
                    else 0.0
                )
                sell_timestamp = sell_order.get("timestamp", 0)
                buy_amount_total = 0
                buy_cost_total = 0
                for buy_order in sorted(
                    buy_orders, key=lambda x: x.get("timestamp", 0)
                ):
                    buy_id = buy_order.get("id", "N/A")
                    buy_amount = (
                        float(buy_order.get("amount", 0))
                        if buy_order.get("amount") is not None
                        else 0.0
                    )
                    buy_price = (
                        float(buy_order.get("price", 0))
                        if buy_order.get("price") is not None
                        else 0.0
                    )
                    buy_timestamp = buy_order.get("timestamp", 0)
                    if (
                        buy_timestamp < sell_timestamp
                        and used_buy_amounts[buy_id] < buy_amount
                    ):
                        available_amount = buy_amount - used_buy_amounts[buy_id]
                        amount_to_use = min(
                            available_amount, sell_amount - buy_amount_total
                        )
                        if amount_to_use > 0:
                            buy_amount_total += amount_to_use
                            buy_cost_total += amount_to_use * buy_price
                            used_buy_amounts[buy_id] += amount_to_use
                            if buy_amount_total >= sell_amount:
                                break
                if buy_amount_total > 0:
                    avg_buy_price = buy_cost_total / buy_amount_total
                    realized_pnl += (sell_price - avg_buy_price) * sell_amount

            # Calcular lucro/prejuízo não realizado (posições abertas)
            open_buy_orders = [
                o
                for o in buy_orders
                if o.get("status", "") and o.get("status", "").lower() != "closed"
            ]
            open_amount = sum(
                float(o.get("amount", 0))
                for o in open_buy_orders
                if o.get("amount") is not None
            )
            unrealized_pnl = 0
            if open_amount > 0:
                ticker = client.get_ticker(symbol)
                current_price = (
                    float(ticker.get("last", 0))
                    if ticker and "last" in ticker and ticker.get("last") is not None
                    else 0.0
                )
                open_cost = sum(
                    float(o.get("amount", 0)) * float(o.get("price", 0))
                    for o in open_buy_orders
                    if o.get("amount") is not None and o.get("price") is not None
                )
                avg_open_price = open_cost / open_amount if open_amount > 0 else 0
                unrealized_pnl = (current_price - avg_open_price) * open_amount

            total_invested += invested_amount
            total_realized_pnl += realized_pnl
            total_unrealized_pnl += unrealized_pnl

            roi = (realized_pnl / invested_amount * 100) if invested_amount > 0 else 0.0
            print(f" • {symbol}:")
            print(f"   - Investido: ${invested_amount:.2f}")
            print(f"   - Lucro Realizado: ${realized_pnl:.2f}")
            print(f"   - Lucro Não Realizado: ${unrealized_pnl:.2f}")
            print(f"   - ROI Realizado: {roi:.2f}%")

        print("\n📈 Resumo Geral:")
        print("=" * 50)
        total_roi = (
            (total_realized_pnl / total_invested * 100) if total_invested > 0 else 0.0
        )
        print(f" • Total Investido: ${total_invested:.2f}")
        print(f" • Lucro/Prejuízo Realizado Total: ${total_realized_pnl:.2f}")
        print(f" • Lucro/Prejuízo Não Realizado Total: ${total_unrealized_pnl:.2f}")
        print(
            f" • Lucro/Prejuízo Total: ${(total_realized_pnl + total_unrealized_pnl):.2f}"
        )
        print(f" • ROI Realizado Total: {total_roi:.2f}%")

        # Calcular e exibir métricas avançadas
        metrics = self.calculate_advanced_metrics(client)
        print("\n📊 Métricas Avançadas:")
        print("=" * 50)
        for symbol in self.config.TRADING_SYMBOLS:
            metric = metrics.get(symbol, {})
            print(f" • {symbol}:")
            print(f"   - Sharpe Ratio: {metric.get('sharpe_ratio', 0.0):.2f}")
            print(f"   - Máximo Drawdown: {metric.get('max_drawdown', 0.0):.2f}%")
            print(f"   - Win Rate: {metric.get('win_rate', 0.0):.2f}%")
            print(f"   - Profit Factor: {metric.get('profit_factor', 0.0):.2f}")

        # Enviar alertas de performance via Telegram será controlado na função main()
        pass

    # async def display_orders(self) -> None:
    #     """Exibe as ordens recentes criadas pelo bot."""
    #     print("\n📋 Ordens Recentes:")
    #     print("=" * 50)
    #     if not self.created_orders:
    #         print(" Nenhuma ordem criada ainda.")
    #         return

    #     for order in self.created_orders[-5:]:  # Mostra as últimas 5 ordens
    #         order_id = order.get("id", "N/A")
    #         symbol = order.get("symbol", "N/A")
    #         order_type = order.get("type", "N/A")
    #         side = order.get("side", "N/A")
    #         amount = order.get("amount", "N/A")
    #         price = order.get("price", "N/A")
    #         status = order.get("status", "N/A")
    #         print(f" ID: {order_id}")
    #         print(f" Símbolo: {symbol}")
    #         print(f" Tipo: {order_type}")
    #         print(f" Lado: {side}")
    #         print(f" Quantidade: {amount}")
    #         print(f" Preço: {price}")
    #         print(f" Status: {status}")
    #         print(" " + "-" * 30)


async def sync_orders(self, client: OKXClient, symbol: str):
    """
    Sincroniza ordens locais com ordens da exchange para garantir consistência.

    Args:
        client: Instância de OKXClient para interagir com a exchange
        symbol: Símbolo do par de trading para sincronizar ordens
    """
    exchange_orders = await client.exchange.fetch_open_orders(symbol)
    local_orders = self.db.get_open_orders(symbol)  # Assuming TradingDatabase

    for order in exchange_orders:
        if order["id"] not in [o["id"] for o in local_orders]:
            self.db.add_order(order)  # Update local DB

    self.logger.debug("Ordens sincronizadas para %s", symbol)


async def initialize_bot_components() -> Tuple[OKXClient, OKXOrder, TradingBot]:
    """Inicializa componentes do bot."""
    try:
        config = BotConfig()
        client = OKXClient(config)
        order_manager = OKXOrder(client)
        bot = TradingBot(config)
        return client, order_manager, bot
    except Exception as exc:
        logger = TradingLogger.get_logger(__name__)
        logger.error("Falha ao inicializar componentes do bot: %s", str(exc))
        raise ConfigurationError("Falha na inicialização do bot") from exc


async def send_startup_notification(client: OKXClient, bot: TradingBot) -> None:
    """Envia notificação de inicialização."""
    if not bot.config.ENABLE_TELEGRAM_NOTIFICATIONS:
        return

    try:
        balance = client.get_balance()
        message = await build_startup_message(balance, bot.config)
        await bot.send_telegram_notification(message)
    except Exception as exc:
        logger = TradingLogger.get_logger(__name__)
        logger.error("Erro ao enviar notificação de inicialização: %s", str(exc))


async def build_startup_message(balance: Optional[Dict], config: BotConfig) -> str:
    """Constrói mensagem de inicialização."""
    mode_text = "🟢 LIVE TRADING" if not config.SANDBOX_MODE else "🔴 SANDBOX MODE"
    message = f"""🚀 * {config.SCRIPT_NAME} Iniciado*
-----------------------------
• *Mode*: {mode_text}
• *Balance*:
💰 Available Balance:
"""

    if balance and balance.get("total"):
        for asset, amount in balance["total"].items():
            if amount > 0:
                message += f" 🪙 {asset}: {amount:.8f}\n"

    return message


async def main() -> None:
    """Função principal com auto-trading implementado."""
    logger = TradingLogger.get_logger(__name__)

    def handle_sigstp(signum, frame):
        logger.info("Bot interrompido manualmente pelo usuário (SIGTSTP).")
        print("\n🛑 Bot interrompido manualmente (Ctrl+Z).")
        print("A finalizar o bot. Até breve!")
        exit(0)

    try:
        client, order_manager, bot = await initialize_bot_components()
        await send_startup_notification(client, bot)
        await bot.play_alert("start", volume=0.25)

        # Configura o tratamento de SIGTSTP (Ctrl+Z)
        signal.signal(signal.SIGTSTP, handle_sigstp)

        indicator = TechnicalIndicator(client)

        # Inicializar dados históricos
        for symbol in client.config.TRADING_SYMBOLS:
            ohlcv_data = indicator.fetch_historical_data(
                symbol, client.config.TIMEFRAME
            )

        cycle_count = 0

        while True:
            cycle_count += 1
            if cycle_count == 1:
                client._print_mode_message()
            print(
                f"\n🔄 Ciclo #{cycle_count} - {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )

            client.display_prices_with_precision()
            client.display_balance_with_precision()
            bot.update_realized_profits(client)
            bot.display_invested_and_profits(client)

            # Enviar alertas de performance via Telegram foi desativado conforme solicitação do usuário
            pass

            # Verificar ordens fechadas e enviar notificações de PnL
            for symbol in client.config.TRADING_SYMBOLS:
                try:
                    closed_orders = client.exchange.fetch_closed_orders(symbol, limit=5)
                    for order in closed_orders:
                        order_id = order.get("id", "N/A")
                        # Verificar se a ordem já foi notificada para evitar duplicatas
                        if not bot.db.is_order_notified(order_id):
                            if (
                                order.get("side") == "sell"
                                and order.get("status") == "closed"
                            ):
                                buy_order = bot.db.get_matching_buy_order(
                                    order_id, symbol
                                )
                                if buy_order:
                                    try:
                                        buy_price = float(
                                            buy_order.get("price", 0.0) or 0.0
                                        )
                                        sell_price = float(
                                            order.get("price", 0.0) or 0.0
                                        )
                                        amount = min(
                                            float(buy_order.get("amount", 0.0) or 0.0),
                                            float(order.get("amount", 0.0) or 0.0),
                                        )
                                        profit = (sell_price - buy_price) * amount
                                        roi = (
                                            (profit / (buy_price * amount) * 100)
                                            if (buy_price * amount) > 0
                                            else 0.0
                                        )
                                        # Determinar se foi Stop Loss ou Take Profit
                                        closure_type = "Desconhecido"
                                        if (
                                            "info" in buy_order
                                            and "oco_details" in buy_order["info"]
                                        ):
                                            oco_details = buy_order["info"][
                                                "oco_details"
                                            ]
                                            sl_price = float(
                                                oco_details.get("sl_price", 0.0)
                                            )
                                            tp_price = float(
                                                oco_details.get("tp_price", 0.0)
                                            )
                                            if sl_price > 0 and abs(
                                                sell_price - sl_price
                                            ) < abs(sell_price - tp_price):
                                                closure_type = "Stop Loss"
                                            elif tp_price > 0:
                                                closure_type = "Take Profit"

                                        message = f"""📉 *OCO \\- POSIÇÃO FECHADA \\- {symbol}*
• *Tipo*: Venda
• *Motivo*: {closure_type}
• *Quantidade*: {amount}
• *Preço de Compra*: ${buy_price:.2f}
• *Preço de Venda*: ${sell_price:.2f}
• *Lucro/Prejuízo*: ${profit:.2f}
• *ROI*: {roi:.2f}%
• *ID da Ordem*: {order_id}"""
                                        # Notificação desativada conforme solicitação do usuário
                                        await bot.play_alert(
                                            "win" if profit > 0 else "loose", volume=0.7
                                        )
                                        bot.db.mark_order_as_notified(order_id)
                                    except (ValueError, TypeError) as e:
                                        logger.warning(
                                            "Erro ao calcular lucro/prejuízo para ordem %s de %s: %s",
                                            order_id,
                                            symbol,
                                            str(e),
                                        )
                                        message = f"""📉 *OCO \\- POSIÇÃO FECHADA \\- {symbol}*
• *Tipo*: Venda
• *Quantidade*: {order.get("amount", 0.0)}
• *Preço de Venda*: ${order.get("price", 0.0) if order.get("price") else 0.0:.2f}
• *Nota*: Erro ao calcular lucro/prejuízo
• *ID da Ordem*: {order_id}"""
                                        # Notificação desativada conforme solicitação do usuário
                                        bot.db.mark_order_as_notified(order_id)
                                else:
                                    logger.warning(
                                        "Ordem de venda sem correspondência de compra para %s: ID=%s",
                                        symbol,
                                        order_id,
                                    )
                                    sell_price = float(order.get("price", 0.0) or 0.0)
                                    message = f"""📉 *OCO \\- POSIÇÃO FECHADA \\- {symbol}*
• *Tipo*: Venda
• *Quantidade*: {order.get("amount", 0.0)}
• *Preço de Venda*: ${sell_price:.2f}
• *Nota*: Sem ordem de compra correspondente
• *ID da Ordem*: {order_id}"""
                                    # Notificação desativada conforme solicitação do usuário
                                    bot.db.mark_order_as_notified(order_id)
                            elif (
                                order.get("status") == "closed"
                                and order.get("side") == "buy"
                            ):
                                message = f"""📈 *OCO \\- POSIÇÃO ABERTA \\- {symbol}*
• *Tipo*: Compra
• *Quantidade*: {order.get("amount", 0.0)}
• *Preço*: ${order.get("price", 0.0):.2f}
• *ID da Ordem*: {order_id}"""
                                # Notificação desativada conforme solicitação do usuário
                                bot.db.mark_order_as_notified(order_id)
                except Exception as exc:
                    logger.error(
                        "Erro ao verificar ordens fechadas para %s: %s",
                        symbol,
                        str(exc),
                    )

            # await bot.display_orders()
            # display_regular_orders(client)
            # display_oco_orders(client)
            # display_trailing_stop_orders(client)

            # ====== NOVA SEÇÃO: AUTO-TRADING ======
            print("\n🤖 Verificando Sinais de Trading:")
            print("=" * 50)

            checker = Sinais(indicator, client)
            # Obter tickers de uma vez para todos os símbolos para otimizar
            tickers = client.exchange.fetch_tickers(client.config.TRADING_SYMBOLS)

            for symbol in client.config.TRADING_SYMBOLS:
                try:
                    # Verificar sinal de entrada
                    has_buy_signal = checker.check_entry_signal(
                        symbol, timeframe=None, tickers=tickers
                    )

                    # Exibir sinal atual
                    checker.print_signal(symbol, timeframe=None, tickers=tickers)

                    # NOVA LÓGICA: Verificar trailing stops antes de processar sinal
                    if order_manager.has_active_oco_orders(symbol):
                        print(
                            f"⏳ {symbol}: Trailing stop ativo detectado. Aguardando fechamento..."
                        )
                        continue  # Pula para o próximo símbolo

                    # Se há sinal de compra, processar ordem
                    if has_buy_signal:
                        print(f"\n🚨 SINAL DE COMPRA DETECTADO PARA {symbol}!")

                        # Validar condições antes de colocar ordem (já inclui verificação de trailing stop)
                        if order_manager.validate_order_conditions(symbol):
                            print(f"✅ Condições validadas para {symbol}")

                            # Colocar ordem
                            order_result = order_manager.place_buy_order_with_oco(
                                symbol=symbol, indicator=indicator
                            )

                            if order_result:
                                print(f"🎯 ORDEM Stop Loss EXECUTADA COM SUCESSO!")
                                print(f"• Símbolo: {symbol}")
                                print(f"• Quantidade: {order_result['amount']}")
                                print(f"• Preço: ${order_result['entry_price']}")
                                print(
                                    f"• Valor usado: ${order_result['ten_percent_used']:.2f}"
                                )
                                print(
                                    f"• ID da ordem: {order_result['limit_order'].get('id', 'N/A')}"
                                )

                                # Salvar no banco de dados
                                bot.save_order_to_db(order_result["limit_order"])
                                bot.created_orders.append(order_result["limit_order"])

                                # Notificações
                                await bot.play_alert("success", volume=0.7)

                                message = f"""🎯 *ORDEM Stop Loss EXECUTADA*
                                
            • *Símbolo*: {symbol}
            • *Tipo*: OCO (Limit)
            • *Quantidade*: {order_result['amount']}
            • *Preço*: ${order_result['entry_price']}
            • *Valor*: ${order_result['ten_percent_used']:.2f}
            • *Stop Loss Price*: ${order_result['sl_price']}
            • *Take Profit Price*: ${order_result['tp_price']}
            • *ID*: {order_result['limit_order'].get('id', 'N/A')}
            """
                                # Notificação desativada conforme solicitação do usuário

                            else:
                                print(f"❌ Falha ao executar ordem para {symbol}")
                                await bot.play_alert("error", volume=0.5)
                        else:
                            print(f"⚠️ Condições não atendidas para {symbol}")
                            # print(
                            #     f"    (Pode haver ordens ativas ou saldo insuficiente)"
                            # )
                    else:
                        print(f"   → Sem sinal de compra para {symbol}")

                except Exception as exc:
                    logger.error("Erro ao processar %s: %s", symbol, str(exc))
                    print(f"❌ Erro ao processar {symbol}: {exc}")

            print(f"\n⏰ Aguardando próximo ciclo (60 segundos)...")
            await asyncio.sleep(60)

    except KeyboardInterrupt:
        logger.info("Bot interrompido manualmente pelo usuário.")
        print("\n🛑 Bot interrompido manualmente.")
        if "bot" in locals():
            await bot.play_alert("exit", volume=0.9)
        print("A finalizar o bot. Até breve!")
        return

    except (ConfigurationError, ExchangeConnectionError) as exc:
        logger.error("Erro de configuração/conexão: %s", exc)
        print(f"❌ Erro: {exc}")
        print("Por favor, verifique as suas credenciais e configuração.")

    except Exception as exc:
        logger.error("Erro inesperado: %s", exc)
        logger.exception(exc)
        print(f"❌ Erro inesperado: {exc}")
        print("Verifique os logs para mais detalhes.")


if __name__ == "__main__":
    asyncio.run(main())
