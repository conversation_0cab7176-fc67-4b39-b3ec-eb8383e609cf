"""
Script para otimizar parâmetros de Grid Bots da OKX usando análise histórica
Autor: Bluetuga
Data: 2025-06-23

Analisa dados históricos de velas 1h para sugerir melhores configurações de grid
CORRIGIDO: Simulação de lucros agora usa o capital real investido
"""

import os
import sys
import re
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import json
from dataclasses import dataclass
import optuna
from utils.error_handler import ErrorHandler
from utils.logger import TradingLogger
from utils.monitor_grid_orders import OKXGridBotMonitor

optuna.logging.set_verbosity(optuna.logging.WARNING)


@dataclass
class GridOptimizationResult:
    """Resultado da otimização de grid"""

    symbol: str
    current_price: float
    invested_amount: float  # Capital investido real
    optimal_min_price: float
    optimal_max_price: float
    optimal_grid_count: int
    expected_daily_profit: float
    expected_daily_return_pct: float  # Retorno percentual diário
    expected_annual_return: float
    volatility: float
    risk_score: float
    confidence: float


class GridBotOptimizer:
    """Classe para otimizar parâmetros de Grid Bots"""

    MAKER_FEE: float = 0.0008  # 0.08%
    TAKER_FEE: float = 0.001  # 0.1%

    def __init__(self, data_folder: str = "data"):
        """Inicializa o otimizador"""
        self.data_folder = data_folder
        self.monitor = OKXGridBotMonitor()
        self.logger = TradingLogger.get_logger(__name__)
        self.error_handler = ErrorHandler(
            logger_name=__name__, max_retries=3, retry_delay=2
        )
        self.results = []
        self.previous_results = (
            []
        )  # Para armazenar resultados anteriores e comparar mudanças

    def load_historical_data(
        self, symbol: str, timeframe: str = "1h"
    ) -> Optional[pd.DataFrame]:
        """Carrega dados históricos de um símbolo"""

        def load_data():
            # Tentar diferentes padrões de nome de arquivo
            candidates = [
                f"data_{symbol}_{timeframe}.csv",  # Ex: data_BTCUSDT_1h.csv
                f"data_{symbol.replace('-', '_')}_{timeframe}.csv",  # Ex: data_BTC_USDT_1h.csv
                f"data_{symbol.replace('-', '_').replace('USD', 'USDC')}_{timeframe}.csv",  # Ex: data_BTC_USDC_1h.csv
                f"data_{symbol.replace('-', '')}_{timeframe}.csv",  # Ex: data_BTCUSDT_1h.csv
            ]
            filepath = None
            for candidate in candidates:
                path = os.path.join(self.data_folder, candidate)
                if os.path.exists(path):
                    filepath = path
                    break

            if not filepath:
                self.logger.warning(
                    f"Arquivo não encontrado para {symbol}. Tentativas: {candidates}"
                )
                return None

            # Carregar CSV
            df = pd.read_csv(filepath)

            # Verificar colunas necessárias
            required_columns = ["timestamp", "open", "high", "low", "close", "volume"]
            if not all(col in df.columns for col in required_columns):
                self.logger.warning(
                    f"Colunas necessárias não encontradas em {os.path.basename(filepath)}. Colunas disponíveis: {list(df.columns)}"
                )
                return None

            # Converter timestamp para datetime
            if df["timestamp"].dtype == "object":
                df["timestamp"] = pd.to_datetime(df["timestamp"])
            else:
                df["timestamp"] = pd.to_datetime(df["timestamp"], unit="ms")

            df = df.sort_values("timestamp").reset_index(drop=True)

            # Limitar aos últimos 300 candles
            df = df.tail(300) if len(df) > 300 else df

            # self.logger.info(f"Dados carregados: {symbol} ({len(df)} velas)")
            # self.logger.info(f"Período: {df['timestamp'].min()} até {df['timestamp'].max()}")

            return df

        result = self.error_handler.execute_with_retry(
            load_data, f"Carregar dados históricos para {symbol}"
        )
        if result is None:
            self.logger.warning(
                f"Falha ao carregar dados de {symbol} após várias tentativas"
            )
        return result

    def calculate_volatility_metrics(self, df: pd.DataFrame) -> Dict:
        """Calcula métricas de volatilidade com foco nos últimos 7 dias"""

        def calc_metrics():
            # Usar apenas os últimos 7 dias (168 candles)
            recent_df = df.tail(168)
            if len(recent_df) < 168:
                return {}  # Não há dados suficientes
            
            # Calcular retornos
            recent_df = recent_df.copy()
            recent_df["returns"] = recent_df["close"].pct_change()
            
            # Volatilidade diária baseada nos últimos 7 dias
            volatility_7d = recent_df["returns"].std() * np.sqrt(24)

            # ATR de 14 períodos baseado nos últimos 7 dias
            recent_df["true_range"] = np.maximum(
                recent_df["high"] - recent_df["low"],
                np.maximum(
                    abs(recent_df["high"] - recent_df["close"].shift(1)),
                    abs(recent_df["low"] - recent_df["close"].shift(1)),
                ),
            )
            atr_14 = recent_df["true_range"].rolling(14).mean().iloc[-1]
            atr_percentage = (atr_14 / recent_df["close"].iloc[-1]) * 100

            # Máximos e mínimos dos últimos 7 dias
            high_7d = recent_df["high"].max()
            low_7d = recent_df["low"].min()

            return {
                "volatility_daily": volatility_7d,  # Já é a volatilidade diária dos últimos 7 dias
                "atr_14": atr_14,
                "atr_percentage": atr_percentage,
                "high_7d": high_7d,
                "low_7d": low_7d,
                "current_price": recent_df["close"].iloc[-1],
            }

        result = self.error_handler.execute_with_retry(
            calc_metrics, "Calcular métricas de volatilidade"
        )
        if result is None:
            self.logger.warning(
                "Falha ao calcular métricas de volatilidade após várias tentativas"
            )
            return {}
        return result

    def optimize_grid_parameters(
        self, df: pd.DataFrame, symbol: str, invested_amount: float = 0.0
    ) -> GridOptimizationResult:
        """Otimiza parâmetros do grid para os últimos 7 dias"""
        # Focar apenas nos últimos 7 dias
        recent_df = df.tail(168)
        if len(recent_df) < 168:
            # Não há dados suficientes, usar heurística
            return self._heuristic_optimization({}, df, symbol, invested_amount)
            
        metrics = self.calculate_volatility_metrics(recent_df)
        current_price = metrics["current_price"]
        volatility = metrics["volatility_daily"]
        
        # Forçar uso de dados recentes na simulação
        df = recent_df

        # se não há capital ou dados insuficientes, usa heurística simples:
        if invested_amount <= 0 or len(df) < 24:
            # cai na heurística original
            return self._heuristic_optimization(metrics, df, symbol, invested_amount)

        # define função‐objetivo para o Optuna (agora maximizando lucro)
        def objective(trial: optuna.Trial) -> float:
            # 1) otimizar range_multiplier entre 2.25 e 3.50
            rm = trial.suggest_float("range_multiplier", 2.25, 3.50)
            # 2) otimizar número de grids entre 5 e 50
            grid_cnt = trial.suggest_int("grid_count", 5, 50)

            # calcula range baseado em rm
            range_size = current_price * (rm / 100)
            min_p = current_price - range_size
            max_p = current_price + range_size

            # força não ultrapassar suportes/resistências
            low7 = metrics.get("low_7d", min_p)
            high7 = metrics.get("high_7d", max_p)
            if low7 * 0.95 > min_p:
                min_p = low7 * 0.95
            if high7 * 1.05 < max_p:
                max_p = high7 * 1.05

            # simula lucro diário e retorna diretamente para maximização
            return self.simulate_grid_profits(
                df, min_p, max_p, grid_cnt, invested_amount
            )

        # executa estudo com direção 'maximize'
        study = optuna.create_study(direction="maximize")
        study.optimize(objective, n_trials=50, timeout=60)

        # recupera melhores params
        best = study.best_params
        best_rm = best["range_multiplier"]
        best_grid = best["grid_count"]

        # recomputa as variáveis finais com os melhores valores
        range_size = current_price * (best_rm / 100)
        optimal_min_price = current_price - range_size
        optimal_max_price = current_price + range_size

        if metrics.get("low_7d", optimal_min_price) * 0.95 > optimal_min_price:
            optimal_min_price = metrics.get("low_7d", optimal_min_price) * 0.95
        if metrics.get("high_7d", optimal_max_price) * 1.05 < optimal_max_price:
            optimal_max_price = metrics.get("high_7d", optimal_max_price) * 1.05

        expected_daily_profit = self.simulate_grid_profits(
            df, optimal_min_price, optimal_max_price, best_grid, invested_amount
        )
        expected_daily_return_pct = (
            (expected_daily_profit / invested_amount * 100)
            if invested_amount > 0
            else 0
        )
        expected_annual_return = expected_daily_return_pct * 365
        risk_score = self.calculate_risk_score(
            volatility,
            metrics["atr_percentage"],
            current_price,
            optimal_min_price,
            optimal_max_price,
        )
        confidence = min(1.0, len(df) / 720)

        return GridOptimizationResult(
            symbol=symbol,
            current_price=current_price,
            invested_amount=invested_amount,
            optimal_min_price=optimal_min_price,
            optimal_max_price=optimal_max_price,
            optimal_grid_count=best_grid,
            expected_daily_profit=expected_daily_profit,
            expected_daily_return_pct=expected_daily_return_pct,
            expected_annual_return=expected_annual_return,
            volatility=volatility,
            risk_score=risk_score,
            confidence=confidence,
        )

    def _heuristic_optimization(
        self, metrics: dict, df, symbol: str, invested_amount: float
    ):
        """Se não puder fazer Optuna, usa a heurística atual."""
        current_price = metrics["current_price"]
        volatility = metrics["volatility_daily"]
        atr_pct = metrics["atr_percentage"]

        range_multiplier = min(3.50, max(2.25, volatility * 100))
        range_size = current_price * (range_multiplier / 100)
        optimal_min_price = current_price - range_size
        optimal_max_price = current_price + range_size

        if metrics.get("low_7d", optimal_min_price) * 0.95 > optimal_min_price:
            optimal_min_price = metrics.get("low_7d", optimal_min_price) * 0.95
        if metrics.get("high_7d", optimal_max_price) * 1.05 < optimal_max_price:
            optimal_max_price = metrics.get("high_7d", optimal_max_price) * 1.05

        price_range = optimal_max_price - optimal_min_price
        min_profitable_move = (self.MAKER_FEE + self.TAKER_FEE) * current_price * 2
        optimal_grid_spacing = max(
            min_profitable_move, atr_pct * current_price / 100 * 0.5
        )
        optimal_grid_count = max(5, min(50, int(price_range / optimal_grid_spacing)))

        expected_daily_profit = self.simulate_grid_profits(
            df,
            optimal_min_price,
            optimal_max_price,
            optimal_grid_count,
            invested_amount,
        )
        expected_daily_return_pct = (
            (expected_daily_profit / invested_amount * 100)
            if invested_amount > 0
            else 0
        )
        expected_annual_return = expected_daily_return_pct * 365

        risk_score = self.calculate_risk_score(
            volatility, atr_pct, current_price, optimal_min_price, optimal_max_price
        )
        confidence = min(1.0, len(df) / 720)

        return GridOptimizationResult(
            symbol=symbol,
            current_price=current_price,
            invested_amount=invested_amount,
            optimal_min_price=optimal_min_price,
            optimal_max_price=optimal_max_price,
            optimal_grid_count=optimal_grid_count,
            expected_daily_profit=expected_daily_profit,
            expected_daily_return_pct=expected_daily_return_pct,
            expected_annual_return=expected_annual_return,
            volatility=volatility,
            risk_score=risk_score,
            confidence=confidence,
        )

    def simulate_grid_profits(
        self,
        df: pd.DataFrame,
        min_price: float,
        max_price: float,
        grid_count: int,
        invested_amount: float,
    ) -> float:
        """Simula lucros do grid baseado nos dados históricos e capital real"""

        def simulate():
            if len(df) < 24:  # Precisa de pelo menos 24 horas de dados
                return 0.0

            if invested_amount <= 0:
                return 0.0

            # Focar apenas nos últimos 7 dias (168 candles de 1h)
            recent_df = df.tail(168)
            if len(recent_df) < 168:
                return 0.0  # Não há dados suficientes para 7 dias completos

            grid_spacing = (max_price - min_price) / grid_count
            grid_levels = [min_price + i * grid_spacing for i in range(grid_count + 1)]

            # Calcular quantidade por grid baseada no capital investido
            # Usar preço médio dos últimos 7 dias para melhor estimativa
            avg_price = recent_df["close"].mean()
            total_quantity = invested_amount / avg_price
            quantity_per_grid = total_quantity / grid_count

            total_profit = 0.0
            trades_count = 0

            for i in range(1, len(recent_df)):
                prev_close = recent_df.iloc[i - 1]["close"]
                curr_close = recent_df.iloc[i]["close"]
                high = recent_df.iloc[i]["high"]
                low = recent_df.iloc[i]["low"]

                # Verificar se houve cruzamento de níveis de grid
                # Usar high/low para detectar toques nos níveis
                for level in grid_levels:
                    # Verificar se o preço tocou no nível durante esta vela
                    if low <= level <= high:
                        # Calcular lucro por trade
                        # Lucro = (spread entre grids - fees) * quantidade
                        spread_profit = grid_spacing * quantity_per_grid
                        fees = (
                            (self.MAKER_FEE + self.TAKER_FEE)
                            * level
                            * quantity_per_grid
                        )
                        net_profit = spread_profit - fees

                        if net_profit > 0:  # Só contar se for lucrativo
                            total_profit += net_profit
                            trades_count += 1

            # Calcular lucro diário médio (baseado em 7 dias exatos)
            daily_profit = total_profit / 7.0

            # Não aplicar fator de realismo para dados recentes
            # Já estamos usando dados reais dos últimos 7 dias
            self.logger.debug(
                f"Simulação 7 dias para {grid_count} grids: "
                f"Lucro total ${total_profit:.2f}, Trades: {trades_count}, "
                f"Lucro diário ${daily_profit:.2f}"
            )
            return daily_profit

        result = self.error_handler.execute_with_retry(
            simulate, "Simular lucros do grid"
        )
        if result is None:
            self.logger.warning(
                "Falha ao simular lucros do grid após várias tentativas"
            )
            return 0.0
        return result

    def calculate_risk_score(
        self,
        volatility: float,
        atr_pct: float,
        current_price: float,
        min_price: float,
        max_price: float,
    ) -> float:
        """Calcula score de risco (0-100, onde 0 é menos arriscado)"""
        try:
            # Fatores de risco
            volatility_risk = min(
                50, volatility * 100 * 2
            )  # Volatilidade alta = risco alto

            # Risco de ficar fora do range
            range_size_pct = ((max_price - min_price) / current_price) * 100
            range_risk = max(0, 30 - range_size_pct)  # Range pequeno = risco alto

            # Risco de estar muito longe do preço atual
            distance_to_min = ((current_price - min_price) / current_price) * 100
            distance_to_max = ((max_price - current_price) / current_price) * 100

            balance_risk = (
                abs(distance_to_min - distance_to_max) * 0.5
            )  # Desequilíbrio = risco

            total_risk = volatility_risk + range_risk + balance_risk

            return min(100, max(0, total_risk))

        except Exception as e:
            print(f"❌ Erro ao calcular score de risco: {e}")
            return 50.0  # Risco médio como fallback

    def get_bot_invested_amount(self, bot_data: Dict) -> float:
        """Extrai o valor investido do bot"""
        try:
            # Tentar diferentes campos que podem conter o valor investido
            invested_fields = [
                "investAmt",  # Amount invested
                "baseSz",  # Base size
                "quoteSz",  # Quote size
                "totalInvestment",  # Total investment
                "capital",  # Capital
                "amt",  # Amount
            ]

            for field in invested_fields:
                if field in bot_data and bot_data[field]:
                    amount = float(bot_data[field])
                    if amount > 0:
                        return amount

            # Se não encontrar, tentar calcular baseado em outros campos
            min_price = float(bot_data.get("minPx", 0))
            max_price = float(bot_data.get("maxPx", 0))
            grid_num = int(bot_data.get("gridNum", 0))

            if min_price > 0 and max_price > 0 and grid_num > 0:
                # Estimativa baseada no range e número de grids
                # Isso é uma aproximação muito básica
                avg_price = (min_price + max_price) / 2
                estimated_amount = avg_price * grid_num * 0.1  # Estimativa conservadora
                return estimated_amount

            print(
                f"⚠️ Não foi possível determinar o valor investido. Campos disponíveis: {list(bot_data.keys())}"
            )
            return 0.0

        except Exception as e:
            print(f"❌ Erro ao extrair valor investido: {e}")
            return 0.0

    def analyze_active_bots(self):
        """Analisa todos os bots ativos e sugere otimizações"""
        print("\n🔍 Analisando bots ativos...")

        # Obter bots ativos
        active_bots = self.monitor.api.get_active_grid_bots()

        if not active_bots:
            print("❌ Nenhum bot ativo encontrado")
            return

        print(f"✅ Encontrados {len(active_bots)} bot(s) ativo(s)")

        for i, bot in enumerate(active_bots, 1):
            symbol = bot.get("instId", "").replace(
                "-", "_"
            )  # BTCUSDT em vez de BTC-USDT

            print(
                f"\n\033[93m📊 Analisando bot {i}/{len(active_bots)}: {symbol}\033[0m"
            )

            # Obter valor investido
            invested_amount = self.get_bot_invested_amount(bot)
            print(f"💰 Valor investido: ${invested_amount:,.2f}")

            if invested_amount <= 0:
                print("⚠️ Valor investido não disponível - simulação será limitada")

            # Carregar dados históricos
            df = self.load_historical_data(symbol)
            if df is None:
                print(f"⚠️ Pulando {symbol} - dados não encontrados")
                continue

            # Otimizar parâmetros
            result = self.optimize_grid_parameters(df, symbol, invested_amount)
            if result:
                self.results.append(result)
                self.display_optimization_result(bot, result)

        # Exibir resumo geral
        if self.results:
            self.display_summary()

    def analyze_configured_symbols(self):
        """Analisa símbolos configurados em parameters.json e sugere otimizações"""
        print("\n🔍 Analisando símbolos configurados...")

        # Ler símbolos do arquivo de configuração
        try:
            with open("parameters.json", "r", encoding="utf-8") as f:
                config = json.load(f)
                symbols = config.get("general", {}).get("TRADING_SYMBOLS", [])
        except Exception as e:
            print(f"❌ Erro ao ler parameters.json: {e}")
            return

        if not symbols:
            print("❌ Nenhum símbolo configurado encontrado em parameters.json")
            return

        print(f"✅ Encontrados {len(symbols)} símbolo(s) configurado(s)")

        for i, symbol in enumerate(symbols, 1):
            symbol = symbol.replace("/", "_")  # Converter BTC/USDC para BTC_USDC
            print(
                f"\n\033[93m📊 Analisando símbolo {i}/{len(symbols)}: {symbol}\033[0m"
            )

            # Definir um valor investido padrão ou solicitar ao usuário
            invested_amount = 1000.0  # Valor padrão para simulação
            print(f"💰 Valor investido padrão: ${invested_amount:,.2f}")

            # Carregar dados históricos
            df = self.load_historical_data(symbol)
            if df is None:
                print(f"⚠️ Pulando {symbol} - dados não encontrados")
                continue

            # Otimizar parâmetros
            result = self.optimize_grid_parameters(df, symbol, invested_amount)
            if result:
                self.results.append(result)
                # Criar um bot_data fictício para exibição
                fake_bot_data = {
                    "minPx": "0",
                    "maxPx": "0",
                    "gridNum": "0",
                    "totalPnl": "0",
                }
                self.display_optimization_result(fake_bot_data, result)

        # Exibir resumo geral
        if self.results:
            self.display_summary()

    def display_optimization_result(
        self, bot_data: Dict, result: GridOptimizationResult
    ):
        """Exibe resultado da otimização"""
        print("\n" + "=" * 80)
        print(f"🤖 OTIMIZAÇÃO PARA {result.symbol}")
        print("-" * 50)

        # Configuração atual
        print("📊 CONFIGURAÇÃO ATUAL:")
        current_min = float(bot_data.get("minPx", 0))
        current_max = float(bot_data.get("maxPx", 0))
        current_grids = int(bot_data.get("gridNum", 0))
        current_pnl = float(bot_data.get("totalPnl", 0))

        print(f"   Capital Investido: ${result.invested_amount:,.2f}")
        print(f"   Min: ${current_min:,.4f}")
        print(f"   Max: ${current_max:,.4f}")
        print(f"   Grids: {current_grids}")
        pnl_str = f"   PnL Atual: ${current_pnl:+,.2f}"
        if current_pnl > 0:
            print(f"\033[92m{pnl_str}\033[0m")
        else:
            print(f"\033[91m{pnl_str}\033[0m")

        # Configuração otimizada
        print("\n🎯 CONFIGURAÇÃO OTIMIZADA:")
        print(f"   Preço Atual: ${result.current_price:,.4f}")
        print(f"\033[93m   Min Sugerido: ${result.optimal_min_price:,.4f}\033[0m")
        print(f"\033[93m   Max Sugerido: ${result.optimal_max_price:,.4f}\033[0m")
        print(f"\033[93m   Grids Sugeridos: {result.optimal_grid_count}\033[0m")

        # Métricas de performance
        print("\n📈 MÉTRICAS ESPERADAS:")
        print(f"   Lucro Diário Estimado: ${result.expected_daily_profit:+,.2f}")
        print(f"   Retorno Diário: {result.expected_daily_return_pct:+.2f}%")
        print(f"   Retorno Anual Estimado: {result.expected_annual_return:+.2f}%")
        print(f"   Volatilidade: {result.volatility*100:.2f}%")
        print(f"   Score de Risco: {result.risk_score:.1f}/100")
        print(f"   Confiança: {result.confidence*100:.2f}%")

        # Comparação e recomendações
        print("\n💡 ANÁLISE COMPARATIVA:")

        # Comparar ranges
        current_range = (
            current_max - current_min if current_max > 0 and current_min > 0 else 0
        )
        optimal_range = result.optimal_max_price - result.optimal_min_price

        if current_range > 0:
            range_diff_pct = ((optimal_range - current_range) / current_range) * 100
            print(f"   Mudança no Range: {range_diff_pct:+.2f}%")

        # Comparar número de grids
        if current_grids > 0:
            grid_diff = result.optimal_grid_count - current_grids
            print(f"   Mudança nos Grids: {grid_diff:+d}")

        # Comparar retorno atual vs esperado
        if result.invested_amount > 0 and current_pnl != 0:
            current_return_pct = (current_pnl / result.invested_amount) * 100
            print(f"   Retorno Atual: {current_return_pct:+.2f}%")

        # Recomendações
        print("\n🔧 RECOMENDAÇÕES:")

        recommendations = []

        if result.expected_daily_return_pct < 0.01:  # Menos de 0.01% ao dia
            recommendations.append(
                "⚠️ BAIXO RETORNO ESPERADO - considere revisar a estratégia"
            )
        elif result.expected_daily_return_pct > 0.1:  # Mais de 0.1% ao dia
            recommendations.append("🚀 ALTO POTENCIAL - mas verifique o risco")

        if result.risk_score > 70:
            recommendations.append(
                "⚠️ ALTO RISCO - considere reduzir o range ou aumentar o número de grids"
            )
        elif result.risk_score < 30:
            recommendations.append("✅ BAIXO RISCO - configuração conservadora")

        if result.confidence < 0.5:
            recommendations.append(
                "📊 BAIXA CONFIANÇA - poucos dados históricos disponíveis"
            )

        if current_range > 0 and abs(range_diff_pct) > 20:
            if range_diff_pct > 0:
                recommendations.append(
                    "📈 Considere AUMENTAR o range para capturar mais movimentos"
                )
            else:
                recommendations.append(
                    "📉 Considere REDUZIR o range para maior frequência de trades"
                )

        if not recommendations:
            recommendations.append("✅ Configuração atual parece adequada")

        for rec in recommendations:
            print(f"   {rec}")

    def display_summary(self):
        """Exibe resumo geral das otimizações"""
        if not self.results:
            return

        print("\n" + "=" * 80)
        print("📊 RESUMO GERAL DAS OTIMIZAÇÕES")
        print("-" * 50)

        total_invested = sum(r.invested_amount for r in self.results)
        total_expected_profit = sum(r.expected_daily_profit for r in self.results)
        avg_daily_return = (
            (total_expected_profit / total_invested * 100) if total_invested > 0 else 0
        )
        avg_risk_score = sum(r.risk_score for r in self.results) / len(self.results)
        avg_confidence = sum(r.confidence for r in self.results) / len(self.results)

        print(f"🤖 Total de Bots Analisados: {len(self.results)}")
        print(f"💰 Capital Total Investido: ${total_invested:,.2f}")
        print(f"📈 Lucro Diário Total Estimado: ${total_expected_profit:+,.2f}")
        print(f"📊 Retorno Diário Médio: {avg_daily_return:+.2f}%")
        print(f"📅 Retorno Anual Estimado: {avg_daily_return * 365:+.2f}%")
        print(f"⚖️ Score de Risco Médio: {avg_risk_score:.1f}/100")
        print(f"📊 Confiança Média: {avg_confidence*100:.2f}%")

        # Ranking por performance
        print("\n🏆 RANKING POR RETORNO PERCENTUAL:")
        sorted_results = sorted(
            self.results, key=lambda x: x.expected_daily_return_pct, reverse=True
        )

        for i, result in enumerate(sorted_results, 1):
            risk_emoji = (
                "🟢"
                if result.risk_score < 40
                else "🟡" if result.risk_score < 70 else "🔴"
            )
            print(
                f"{i}. {result.symbol}: {result.expected_daily_return_pct:+.2f}%/dia (${result.expected_daily_profit:+,.2f}) {risk_emoji}"
            )

    def export_optimization_results(self):
        """Exporta resultados da otimização para JSON"""
        if not self.results:
            print("❌ Nenhum resultado para exportar")
            return

        try:
            total_invested = sum(r.invested_amount for r in self.results)
            total_expected_profit = sum(r.expected_daily_profit for r in self.results)

            export_data = {
                "export_timestamp": datetime.now().isoformat(),
                "summary": {
                    "total_bots": len(self.results),
                    "total_invested": total_invested,
                    "total_daily_profit": total_expected_profit,
                    "average_daily_return_pct": (
                        (total_expected_profit / total_invested * 100)
                        if total_invested > 0
                        else 0
                    ),
                    "average_risk_score": sum(r.risk_score for r in self.results)
                    / len(self.results),
                    "average_confidence": sum(r.confidence for r in self.results)
                    / len(self.results),
                },
                "optimizations": [],
            }

            for result in self.results:
                export_data["optimizations"].append(
                    {
                        "symbol": result.symbol,
                        "current_price": result.current_price,
                        "invested_amount": result.invested_amount,
                        "optimal_min_price": result.optimal_min_price,
                        "optimal_max_price": result.optimal_max_price,
                        "optimal_grid_count": result.optimal_grid_count,
                        "expected_daily_profit": result.expected_daily_profit,
                        "expected_daily_return_pct": result.expected_daily_return_pct,
                        "expected_annual_return": result.expected_annual_return,
                        "volatility": result.volatility,
                        "risk_score": result.risk_score,
                        "confidence": result.confidence,
                    }
                )

            filename = (
                f"grid_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            )

            with open(filename, "w", encoding="utf-8") as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)

            print(f"✅ Resultados exportados para: {filename}")

        except Exception as e:
            print(f"❌ Erro ao exportar resultados: {e}")

    def run_interactive_menu(self):
        """Executa menu interativo"""
        while True:
            print("\n" + "=" * 80)
            print("🎯 OTIMIZADOR DE GRID BOTS OKX")
            print("-" * 50)
            print("1. 🔍 Analisar bots ativos")
            print("2. 📊 Analisar símbolo específico")
            print("3. 📈 Analisar símbolos configurados")
            print("4. 📤 Exportar resultados")
            print("5. 📋 Ver resumo dos resultados")
            print("6. 🔧 Configurar pasta de dados")
            print("7. 🔄 Iniciar loop de monitoramento (1h)")
            print("8. ❌ Sair")
            print("-" * 50)

            choice = input("Escolha uma opção (1-8): ").strip()

            if choice == "1":
                self.analyze_active_bots()
            elif choice == "2":
                self.analyze_specific_symbol()
            elif choice == "3":
                self.analyze_configured_symbols()
            elif choice == "4":
                self.export_optimization_results()
            elif choice == "5":
                self.display_summary()
            elif choice == "6":
                self.configure_data_folder()
            elif choice == "7":
                self.run_monitoring_loop()
            elif choice == "8":
                print("👋 Saindo...")
                break
            else:
                print("❌ Opção inválida")

            input("\nPressione Enter para continuar...")

    def analyze_specific_symbol(self):
        """Analisa um símbolo específico"""
        symbol = input("Digite o símbolo (ex: BTCUSDT): ").strip().upper()

        if not symbol:
            print("❌ Símbolo inválido")
            return

        # Pedir valor investido
        try:
            invested_str = input("Digite o valor investido (USD): ").strip()
            invested_amount = float(invested_str) if invested_str else 0.0
        except ValueError:
            print("❌ Valor inválido, usando 0")
            invested_amount = 0.0

        print(f"\n🔍 Analisando {symbol}...")

        df = self.load_historical_data(symbol)
        if df is None:
            return

        result = self.optimize_grid_parameters(df, symbol, invested_amount)
        if result:
            # Criar um bot_data fake para exibir
            fake_bot_data = {
                "minPx": "0",
                "maxPx": "0",
                "gridNum": "0",
                "totalPnl": "0",
            }
            self.display_optimization_result(fake_bot_data, result)
            self.results.append(result)

    def configure_data_folder(self):
        """Configura pasta de dados"""
        current_folder = self.data_folder
        print(f"\nPasta atual: {current_folder}")

        new_folder = input("Digite a nova pasta (Enter para manter atual): ").strip()

        if new_folder and os.path.exists(new_folder):
            self.data_folder = new_folder
            print(f"✅ Pasta alterada para: {new_folder}")
        elif new_folder:
            print(f"❌ Pasta não encontrada: {new_folder}")
        else:
            print("📁 Mantendo pasta atual")

    def run_monitoring_loop(self):
        """Executa loop de monitoramento a cada hora e envia notificações para o Telegram se houver mudanças"""
        import time
        import asyncio
        from service.service_telegram import TelegramNotifier

        print("🔄 Iniciando loop de monitoramento a cada 1 hora...")
        print("Pressione Ctrl+C para interromper o loop.")

        loop_interval = 7200  # 15 segundos para teste, mude para 3600 para 1 hora

        try:
            while True:
                print(
                    f"\n⏰ Executando análise às {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                )

                # Armazenar resultados anteriores para comparação
                self.previous_results = self.results.copy()
                self.results = []

                # Analisar bots ativos
                self.analyze_active_bots()

                # Verificar mudanças nos resultados
                changes_detected = False
                change_messages = []

                if len(self.previous_results) != len(self.results):
                    changes_detected = True
                    change_messages.append(
                        f"📊 Número de bots mudou de {len(self.previous_results)} para {len(self.results)}"
                    )
                else:
                    for prev, curr in zip(self.previous_results, self.results):
                        if prev.symbol == curr.symbol:
                            if (
                                abs(prev.optimal_min_price - curr.optimal_min_price)
                                > 0.01
                                or abs(prev.optimal_max_price - curr.optimal_max_price)
                                > 0.01
                                or prev.optimal_grid_count != curr.optimal_grid_count
                                or abs(
                                    prev.expected_daily_profit
                                    - curr.expected_daily_profit
                                )
                                > 0.01
                            ):
                                changes_detected = True
                                # Format the message without special characters that need escaping
                                # The TelegramNotifier will handle the escaping automatically
                                change_messages.append(
                                    f"🤖 Mudanças detectadas para {curr.symbol}:\n"
                                    f"• Min: ${prev.optimal_min_price:.4f} → ${curr.optimal_min_price:.4f}\n"
                                    f"• Max: ${prev.optimal_max_price:.4f} → ${curr.optimal_max_price:.4f}\n"
                                    f"• Grids: {prev.optimal_grid_count} → {curr.optimal_grid_count}\n"
                                    f"• Lucro Diário Esperado: ${prev.expected_daily_profit:.2f} → ${curr.expected_daily_profit:.2f}"
                                )

                # if changes_detected:
                #     print("📢 Mudanças detectadas! Enviando notificação para o Telegram...")

                #     # Let the TelegramNotifier handle all the escaping
                #     message = (
                #         "🔄 *Atualização de Otimização de Grid Bots*\n\n"
                #         + "\n\n".join(change_messages)
                #     )

                #     async def send_notification():
                #         try:
                #             notifier = await TelegramNotifier.create()
                #             success = await notifier.send_message(message)
                #             if success:
                #                 print("✅ Notificação enviada com sucesso!")
                #             else:
                #                 print("❌ Falha ao enviar notificação")
                #             return success
                #         except Exception as e:
                #             print(f"❌ Erro ao enviar notificação: {e}")
                #             return False

                if changes_detected:
                    print(
                        "📢 Mudanças detectadas! Enviando notificação para o Telegram..."
                    )

                    # Escape periods in numerical values
                    def escape_markdown_v2(text: str) -> str:
                        # Escape all periods with a backslash
                        return text.replace(".", "\\.")

                    change_messages_escaped = []
                    for msg in change_messages:
                        # Split message into lines and escape periods in lines containing numbers
                        lines = msg.split("\n")
                        escaped_lines = []
                        for line in lines:
                            if any(
                                c.isdigit() for c in line
                            ):  # Check if line contains numbers
                                line = escape_markdown_v2(line)
                            escaped_lines.append(line)
                        change_messages_escaped.append("\n".join(escaped_lines))

                    # Construct the final message
                    message = (
                        "🔄 *Atualização de Otimização de Grid Bots*\n\n"
                        + "\n\n".join(change_messages_escaped)
                    )

                    async def send_notification():
                        try:
                            notifier = await TelegramNotifier.create()
                            success = await notifier.send_message(message)
                            if success:
                                print("✅ Notificação enviada com sucesso!")
                            else:
                                print("❌ Falha ao enviar notificação")
                            return success
                        except Exception as e:
                            print(f"❌ Erro ao enviar notificação: {e}")
                            return False

                    # Run the async notification using asyncio.run()
                    asyncio.run(send_notification())

                else:
                    print("✅ Nenhuma mudança detectada nos valores dos bots.")

                print()
                print(
                    f"⏳ Aguardando próxima análise... {(datetime.now() + timedelta(seconds=loop_interval)).strftime('%H:%M:%S')}"
                )
                time.sleep(loop_interval)

        except KeyboardInterrupt:
            print("\n👋 Loop de monitoramento interrompido pelo usuário.")
        except Exception as e:
            print(f"❌ Erro no loop de monitoramento: {e}")


def main():
    """Função principal"""
    try:
        print("🚀 Iniciando Otimizador de Grid Bots...")

        # Verificar se pasta data existe
        if not os.path.exists("data"):
            print("⚠️ Pasta 'data' não encontrada. Criando...")
            os.makedirs("data", exist_ok=True)
            print("📁 Pasta 'data' criada. Coloque seus arquivos CSV nela.")

        optimizer = GridBotOptimizer()
        optimizer.run_interactive_menu()

    except KeyboardInterrupt:
        print("\n👋 Programa interrompido pelo usuário")
    except Exception as e:
        print(f"❌ Erro fatal: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
