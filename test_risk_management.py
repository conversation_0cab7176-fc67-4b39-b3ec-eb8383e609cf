#!/usr/bin/env python3
"""
Script de teste para o módulo de gestão de risco.
"""

import numpy as np
from core.risk_management import (
    VolatilityCalculator,
    PositionSizeCalculator,
    ATRCalculator,
    MarketDataProcessor,
    TrendValidator,
    PositionSizer,
    RiskManager
)

class MockConfig:
    """Configuração mock para testes."""
    POSITION_SIZE_MAX = 0.15
    POSITION_SIZE_BASE = 0.1
    VOLATILITY_THRESHOLD_HIGH = 1.2
    VOLATILITY_THRESHOLD_LOW = 0.8
    ENABLE_FLEXIBLE_ENTRY = True
    WMA_TOLERANCE_PCT = 0.5
    RSI_MIN_THRESHOLD = 45
    CALLBACK_RATIO = 2.75
    ATR_SL_FACTOR = 3.5
    ATR_TP_FACTOR = 4.75

def test_volatility_calculator():
    """Testa a calculadora de volatilidade."""
    print("🧪 Testando VolatilityCalculator...")
    
    calc = VolatilityCalculator(lookback_periods=50, cache_ttl=300)
    
    # Dados de teste
    closes = np.array([100, 101, 99, 102, 98, 103, 97, 104, 96, 105])
    atr_values = np.array([1.5, 1.6, 1.4, 1.7, 1.3, 1.8, 1.2, 1.9, 1.1, 2.0])
    
    result = calc.calculate_weighted_volatility("BTC/USDC", closes, atr_values)
    
    print(f"  ✅ Volatilidade calculada: {result}")
    assert 'weighted' in result
    assert 'historical' in result
    assert 'atr_based' in result
    print("  ✅ VolatilityCalculator funcionando!")

def test_position_size_calculator():
    """Testa a calculadora de tamanho de posição."""
    print("🧪 Testando PositionSizeCalculator...")
    
    config = MockConfig()
    calc = PositionSizeCalculator(config)
    
    volatility_metrics = {
        'weighted': 0.05,
        'historical': 0.04,
        'atr_based': 0.06
    }
    
    position_size = calc.calculate_dynamic_position_size(
        volatility_metrics, 
        btc_correlation=1.0,
        max_risk_per_trade=0.02
    )
    
    print(f"  ✅ Tamanho de posição calculado: {position_size:.2%}")
    assert 0.01 <= position_size <= 0.15
    print("  ✅ PositionSizeCalculator funcionando!")

def test_atr_calculator():
    """Testa a calculadora de ATR."""
    print("🧪 Testando ATRCalculator...")
    
    calc = ATRCalculator(cache_ttl=300)
    
    # Dados OHLCV de teste
    ohlcv_data = [
        [1000, 100, 102, 98, 101, 1000],  # timestamp, open, high, low, close, volume
        [1001, 101, 103, 99, 102, 1100],
        [1002, 102, 104, 100, 103, 1200],
        [1003, 103, 105, 101, 104, 1300],
        [1004, 104, 106, 102, 105, 1400],
        [1005, 105, 107, 103, 106, 1500],
        [1006, 106, 108, 104, 107, 1600],
        [1007, 107, 109, 105, 108, 1700],
        [1008, 108, 110, 106, 109, 1800],
        [1009, 109, 111, 107, 110, 1900],
        [1010, 110, 112, 108, 111, 2000],
        [1011, 111, 113, 109, 112, 2100],
        [1012, 112, 114, 110, 113, 2200],
        [1013, 113, 115, 111, 114, 2300],
        [1014, 114, 116, 112, 115, 2400],
    ]
    
    stats = calc.calculate_atr_statistics(ohlcv_data, period=14, lookback=5)
    
    print(f"  ✅ Estatísticas ATR: {stats}")
    assert 'current' in stats
    assert 'mean' in stats
    assert 'ratio' in stats
    print("  ✅ ATRCalculator funcionando!")

def test_trend_validator():
    """Testa o validador de tendência."""
    print("🧪 Testando TrendValidator...")
    
    config = MockConfig()
    validator = TrendValidator(config)
    
    # Dados de mercado de teste - condição bullish
    market_data = {
        'close': 105.0,
        'wma': 100.0,
        'rsi': 60.0,
        'wma_trend_up': True
    }
    
    result = validator.validate_with_cache("BTC/USDC", market_data)
    
    print(f"  ✅ Validação de tendência (bullish): {result}")
    assert result == True
    
    # Dados de mercado de teste - condição bearish
    market_data_bearish = {
        'close': 95.0,
        'wma': 100.0,
        'rsi': 30.0,
        'wma_trend_up': False
    }
    
    result_bearish = validator.validate_with_cache("BTC/USDC", market_data_bearish)
    
    print(f"  ✅ Validação de tendência (bearish): {result_bearish}")
    print("  ✅ TrendValidator funcionando!")

def test_risk_manager():
    """Testa o gerenciador de risco."""
    print("🧪 Testando RiskManager...")
    
    config = MockConfig()
    risk_mgr = RiskManager(config)
    
    entry_price = 100.0
    atr = 2.0
    
    risk_levels = risk_mgr.calculate_sl_tp_levels(
        "BTC/USDC", entry_price, atr
    )
    
    print(f"  ✅ Níveis de risco calculados: {risk_levels}")
    assert 'sl_price' in risk_levels
    assert 'tp_price' in risk_levels
    assert 'risk_reward_ratio' in risk_levels
    assert risk_levels['sl_price'] < entry_price
    assert risk_levels['tp_price'] > entry_price
    print("  ✅ RiskManager funcionando!")

def main():
    """Executa todos os testes."""
    print("🚀 Iniciando testes do módulo de gestão de risco...\n")
    
    try:
        test_volatility_calculator()
        print()
        
        test_position_size_calculator()
        print()
        
        test_atr_calculator()
        print()
        
        test_trend_validator()
        print()
        
        test_risk_manager()
        print()
        
        print("🎉 Todos os testes passaram! O módulo de gestão de risco está funcionando corretamente.")
        
    except Exception as e:
        print(f"❌ Erro durante os testes: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
