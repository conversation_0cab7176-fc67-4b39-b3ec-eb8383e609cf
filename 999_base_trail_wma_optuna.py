"""
Bot de trading automatizado para OKX Exchange utilizando estratégia Trailing Stop com WMA.
"""

import warnings
warnings.filterwarnings("ignore", message="pkg_resources is deprecated as an API")

import asyncio
import os
import numpy as np
import json
import ccxt
import talib
import pandas as pd
import uuid
import time
import sqlite3
import optuna
from collections import deque
from dotenv import load_dotenv
from typing import Dict, List, Optional, Tuple, Any

# Importações dos módulos compartilhados
from core.config import BotConfig, OrderCache
from core.base_bot import TradingBotBase, initialize_bot_components, send_startup_notification
from core.okx_client import OKXClient
from indicators.indicators import TechnicalIndicator
from utils.logger import TradingLogger
from utils.check_orders import check_trailing_stop_orders, check_closed_trailing_stop_orders
from utils.order_validator import OrderValidator
from utils.exceptions import ConfigurationError, ExchangeConnectionError
from utils.database import TradingDatabase
from signals.signal_wma_rsi import SinaisWmaRsi

load_dotenv()


class IndicatorCache:
    """Cache inteligente com TTL para indicadores técnicos."""

    def __init__(self, ttl: int = 300):  # 5 minutos por padrão
        self.cache = {}
        self.ttl = ttl
        self.logger = TradingLogger.get_logger(__name__)

    def _generate_key(self, symbol: str, timeframe: str, indicator_type: str, **params) -> str:
        """Gera chave única para o cache baseada nos parâmetros."""
        param_str = "_".join(f"{k}={v}" for k, v in sorted(params.items()))
        return f"{symbol}_{timeframe}_{indicator_type}_{param_str}"

    def get(self, symbol: str, timeframe: str, indicator_type: str, **params) -> Optional[float]:
        """Recupera valor do cache se ainda válido."""
        key = self._generate_key(symbol, timeframe, indicator_type, **params)

        if key in self.cache:
            value, timestamp = self.cache[key]
            if time.time() - timestamp < self.ttl:
                self.logger.debug(f"Cache hit para {indicator_type} de {symbol}")
                return value
            else:
                # Remove entrada expirada
                del self.cache[key]
                self.logger.debug(f"Cache expirado removido para {indicator_type} de {symbol}")

        return None

    def set(self, symbol: str, timeframe: str, indicator_type: str, value: float, **params) -> None:
        """Armazena valor no cache com timestamp atual."""
        key = self._generate_key(symbol, timeframe, indicator_type, **params)
        self.cache[key] = (value, time.time())
        self.logger.debug(f"Cache atualizado para {indicator_type} de {symbol}: {value}")

    def clear_expired(self) -> None:
        """Remove todas as entradas expiradas do cache."""
        current_time = time.time()
        expired_keys = [
            key for key, (_, timestamp) in self.cache.items()
            if current_time - timestamp >= self.ttl
        ]

        for key in expired_keys:
            del self.cache[key]

        if expired_keys:
            self.logger.debug(f"Removidas {len(expired_keys)} entradas expiradas do cache")


class VolatilityCalculator:
    """Calculadora de volatilidade otimizada com cache."""

    def __init__(self, lookback_periods: int = 100, cache_ttl: int = 300):
        self.lookback_periods = lookback_periods
        self.cache_ttl = cache_ttl
        self._cache = {}
        self._cache_timestamps = {}
        self.logger = TradingLogger.get_logger(__name__)

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Verifica se entrada do cache ainda é válida."""
        if cache_key not in self._cache:
            return False
        return time.time() - self._cache_timestamps[cache_key] < self.cache_ttl

    def _update_cache(self, cache_key: str, value: Dict[str, float]) -> None:
        """Atualiza cache com novo valor."""
        self._cache[cache_key] = value
        self._cache_timestamps[cache_key] = time.time()

    def calculate_volatility_metrics(self, symbol: str, closes: np.ndarray, atr_values: np.ndarray = None) -> Dict[str, float]:
        """
        Calcula métricas de volatilidade de forma otimizada.

        Args:
            symbol: Símbolo do par de trading
            closes: Array de preços de fechamento
            atr_values: Array de valores ATR (opcional)

        Returns:
            Dict com diferentes medidas de volatilidade
        """
        cache_key = f"vol_{symbol}_{len(closes)}_{hash(closes.tobytes())}"

        if self._is_cache_valid(cache_key):
            self.logger.debug(f"Usando volatilidade do cache para {symbol}")
            return self._cache[cache_key]

        try:
            # 1. Volatilidade histórica (retornos logarítmicos)
            returns = np.diff(np.log(closes))
            hist_volatility = np.std(returns) * np.sqrt(365)  # Anualizada

            # 2. Volatilidade baseada em ATR
            if atr_values is not None and len(atr_values) > 0:
                atr_volatility = np.mean(atr_values[-20:]) / np.mean(closes[-20:])  # ATR normalizado
            else:
                atr_volatility = hist_volatility

            # 3. Volatilidade ponderada (70% histórica + 30% ATR)
            weighted_vol = 0.7 * hist_volatility + 0.3 * atr_volatility

            # 4. Volatilidade de curto prazo (últimos 20 períodos)
            short_term_returns = returns[-20:] if len(returns) >= 20 else returns
            short_term_vol = np.std(short_term_returns) * np.sqrt(365)

            result = {
                'historical': hist_volatility,
                'atr_based': atr_volatility,
                'weighted': weighted_vol,
                'short_term': short_term_vol
            }

            self._update_cache(cache_key, result)
            self.logger.debug(f"Volatilidade calculada e armazenada no cache para {symbol}")
            return result

        except Exception as e:
            self.logger.error(f"Erro ao calcular volatilidade para {symbol}: {e}")
            return {
                'historical': 0.05,
                'atr_based': 0.05,
                'weighted': 0.05,
                'short_term': 0.05
            }


class PositionSizer:
    """Calculadora de tamanho de posição otimizada."""

    def __init__(self, config: BotConfig, volatility_calculator: VolatilityCalculator):
        self.config = config
        self.volatility_calculator = volatility_calculator
        self.logger = TradingLogger.get_logger(__name__)

        # Parâmetros configuráveis
        self.position_size_min = getattr(config, 'POSITION_SIZE_MIN', 0.05)
        self.position_size_base = getattr(config, 'POSITION_SIZE_BASE', 0.1)
        self.position_size_max = getattr(config, 'POSITION_SIZE_MAX', 0.15)
        self.volatility_threshold_high = getattr(config, 'VOLATILITY_THRESHOLD_HIGH', 1.2)
        self.volatility_threshold_low = getattr(config, 'VOLATILITY_THRESHOLD_LOW', 0.8)

    def calculate_position_size(self, symbol: str, volatility_data: Dict[str, float],
                              current_atr: float, atr_mean: float) -> float:
        """
        Calcula tamanho da posição baseado em volatilidade.

        Args:
            symbol: Símbolo do par de trading
            volatility_data: Dados de volatilidade pré-calculados
            current_atr: ATR atual
            atr_mean: ATR médio histórico

        Returns:
            Tamanho da posição como percentual do saldo
        """
        try:
            if not getattr(self.config, 'ENABLE_DYNAMIC_POSITION_SIZING', False):
                return self.position_size_base

            volatility_ratio = current_atr / atr_mean if atr_mean > 0 else 1.0

            if volatility_ratio > self.volatility_threshold_high:
                position_size = self.position_size_min
                self.logger.info(
                    f"Alta volatilidade para {symbol} (ratio={volatility_ratio:.2f}), "
                    f"reduzindo posição para {position_size*100:.1f}%"
                )
            elif volatility_ratio < self.volatility_threshold_low:
                position_size = self.position_size_max
                self.logger.info(
                    f"Baixa volatilidade para {symbol} (ratio={volatility_ratio:.2f}), "
                    f"aumentando posição para {position_size*100:.1f}%"
                )
            else:
                position_size = self.position_size_base
                self.logger.info(
                    f"Volatilidade normal para {symbol} (ratio={volatility_ratio:.2f}), "
                    f"posição padrão {position_size*100:.1f}%"
                )

            return position_size

        except Exception as e:
            self.logger.error(f"Erro ao calcular tamanho da posição para {symbol}: {e}")
            return self.position_size_base


class OKXOrder:
    """Classe para gerenciar operações de ordens na exchange OKX com estratégia Trailing Stop."""

    _instance = None

    def __new__(cls, client: OKXClient = None):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, client: OKXClient = None):
        if getattr(self, "_initialized", False):
            return
        self._initialized = True
        self.client = client or OKXClient()
        self.logger = TradingLogger.get_logger(__name__)
        self.validator = OrderValidator(self.client)

        # Garantir que o cache de ordens esteja inicializado no cliente
        if not hasattr(self.client, "order_cache"):
            self.client.order_cache = OrderCache(cache_duration=30)

        # Inicializar sistemas de cache otimizados
        self.indicator_cache = IndicatorCache(ttl=300)  # 5 minutos para indicadores
        self.volatility_calculator = VolatilityCalculator(
            lookback_periods=getattr(client.config, 'ATR_LOOKBACK_PERIODS', 100),
            cache_ttl=300  # 5 minutos para volatilidade
        )
        self.position_sizer = PositionSizer(client.config, self.volatility_calculator)

        # Cache para dados OHLCV para evitar múltiplas chamadas
        self._ohlcv_cache = {}
        self._ohlcv_cache_timestamps = {}
        self._ohlcv_cache_ttl = 60  # 1 minuto para dados OHLCV

    def _get_cached_ohlcv(self, symbol: str, timeframe: str, limit: int, indicator: TechnicalIndicator) -> Optional[np.ndarray]:
        """Obtém dados OHLCV do cache ou busca novos dados."""
        cache_key = f"{symbol}_{timeframe}_{limit}"
        current_time = time.time()

        # Verificar se dados estão no cache e ainda válidos
        if (cache_key in self._ohlcv_cache and
            cache_key in self._ohlcv_cache_timestamps and
            current_time - self._ohlcv_cache_timestamps[cache_key] < self._ohlcv_cache_ttl):
            self.logger.debug(f"Usando dados OHLCV do cache para {symbol}")
            return self._ohlcv_cache[cache_key]

        # Buscar novos dados
        ohlcv_data = indicator.fetch_historical_data(symbol, timeframe, limit)
        if ohlcv_data is not None:
            ohlcv_array = np.array(ohlcv_data)
            self._ohlcv_cache[cache_key] = ohlcv_array
            self._ohlcv_cache_timestamps[cache_key] = current_time
            self.logger.debug(f"Dados OHLCV atualizados no cache para {symbol}")
            return ohlcv_array

        return None

    def _calculate_atr_with_cache(self, symbol: str, timeframe: str, period: int, indicator: TechnicalIndicator) -> Optional[float]:
        """Calcula ATR usando cache para evitar recálculos."""
        # Verificar cache primeiro
        cached_atr = self.indicator_cache.get(symbol, timeframe, "ATR", period=period)
        if cached_atr is not None:
            return cached_atr

        # Calcular ATR
        atr = indicator.calculate_atr(symbol, timeframe, period)
        if atr is not None:
            self.indicator_cache.set(symbol, timeframe, "ATR", atr, period=period)

        return atr

    def _get_volatility_data(self, symbol: str, timeframe: str, indicator: TechnicalIndicator) -> Tuple[Optional[float], Optional[float], Optional[Dict[str, float]]]:
        """
        Obtém dados de volatilidade otimizados com cache.

        Returns:
            Tuple de (atr_atual, atr_medio, dados_volatilidade)
        """
        try:
            # Obter dados OHLCV do cache
            lookback_periods = getattr(self.client.config, "ATR_LOOKBACK_PERIODS", 20)
            ohlcv_data = self._get_cached_ohlcv(symbol, timeframe, lookback_periods + 14, indicator)

            if ohlcv_data is None or len(ohlcv_data) <= 14:
                self.logger.warning(f"Dados insuficientes para calcular volatilidade de {symbol}")
                return None, None, None

            # Calcular ATR atual
            current_atr = self._calculate_atr_with_cache(symbol, timeframe, 14, indicator)
            if current_atr is None:
                return None, None, None

            # Calcular ATR médio histórico usando dados em cache
            closes = ohlcv_data[:, 4]  # Preços de fechamento
            highs = ohlcv_data[:, 2]   # Preços máximos
            lows = ohlcv_data[:, 3]    # Preços mínimos

            # Calcular múltiplos valores de ATR para média histórica
            atr_values = []
            for i in range(len(ohlcv_data) - 14, len(ohlcv_data)):
                if i >= 14:
                    high_slice = highs[i-14:i]
                    low_slice = lows[i-14:i]
                    close_slice = closes[i-14:i]
                    atr_val = talib.ATR(high_slice, low_slice, close_slice, timeperiod=14)[-1]
                    if not np.isnan(atr_val):
                        atr_values.append(atr_val)

            atr_mean = np.mean(atr_values) if atr_values else current_atr

            # Calcular métricas de volatilidade usando a calculadora otimizada
            volatility_data = self.volatility_calculator.calculate_volatility_metrics(
                symbol, closes, np.array(atr_values) if atr_values else None
            )

            return current_atr, atr_mean, volatility_data

        except Exception as e:
            self.logger.error(f"Erro ao obter dados de volatilidade para {symbol}: {e}")
            return None, None, None

    def validate_order_conditions(self, symbol: str) -> bool:
        """
        Valida se as condições estão adequadas para colocar uma ordem.

        Args:
            symbol: Par de trading

        Returns:
            True se condições estão OK, False caso contrário
        """
        return self.validator.validate_order_conditions(symbol, strategy_type="trailing")

    def _generate_client_order_id(self) -> str:
        return uuid.uuid4().hex[:32]

    def has_active_trailing_stop(self, symbol: str) -> bool:
        """
        Verifica especificamente se há ordens trailing stop ativas.

        Args:
            symbol: Par de trading

        Returns:
            True se há ordens trailing stop ativas, False caso contrário
        """
        try:
            # Verificar primeiro no cache
            open_orders = self.client.order_cache.get_open_orders(symbol)
            if open_orders is not None:
                trailing_orders = [
                    order
                    for order in open_orders
                    if "move_order_stop" in order.get("info", {}).get("ordType", "")
                ]
                active_count = len(trailing_orders)
                if active_count > 0:
                    return True
                return False

            # Se não estiver no cache, buscar da exchange
            trailing_orders = check_trailing_stop_orders(self.client, symbol)
            active_count = len(trailing_orders)
            if active_count > 0:
                for order in trailing_orders:
                    order_id = order.get("id", "N/A")
                    status = order.get("status", "N/A")
                    self.logger.debug(
                        "Trailing stop ativo: ID=%s, Status=%s", order_id, status
                    )
                return True
            return False
        except Exception as exc:
            self.logger.debug("Erro ao buscar dados para %s: %s", symbol, str(exc))
            self.logger.info("O símbolo %s não existe na exchange.", symbol)
            return True  # Em caso de erro, assume que há ordens ativas para ser cauteloso

    async def wait_for_order_execution(
        self, order_id: str, symbol: str, timeout: int = 60, max_retries: int = 3
    ) -> Optional[Dict]:
        start_time = time.time()
        retries = 0
        while time.time() - start_time < timeout:
            try:
                order = self.client.exchange.fetch_order(order_id, symbol)
                if order["status"] == "closed":
                    self.logger.info(f"Ordem {order_id} executada com sucesso.")
                    return order
                elif order["status"] == "canceled":
                    self.logger.warning(f"Ordem {order_id} foi cancelada.")
                    return None
                await asyncio.sleep(2)  # Aumentar o intervalo entre tentativas
            except Exception as exc:
                retries += 1
                self.logger.error(
                    f"Erro ao verificar status da ordem {order_id} (Tentativa {retries}/{max_retries}): {exc}"
                )
                if retries >= max_retries:
                    self.logger.error(
                        f"Limite de tentativas atingido para a ordem {order_id}. Abortando."
                    )
                    return None
                await asyncio.sleep(3)  # Aguardar mais tempo antes de nova tentativa
        self.logger.warning(
            f"Tempo esgotado ao esperar pela execução da ordem {order_id}."
        )
        return None

    async def place_buy_order_with_trailing_stop(
        self, symbol: str, indicator: TechnicalIndicator, timeframe: str = "15m"
    ) -> Optional[Dict]:
        try:
            if check_trailing_stop_orders(self.client, symbol):
                self.logger.info(
                    "Já existem ordens abertas para %s, pulando criação", symbol
                )
                return None

            best_bid = self.client.get_best_bid(symbol)
            if not best_bid:
                self.logger.error("Não foi possível obter melhor bid para %s", symbol)
                return None
            entry_price = best_bid
            self.logger.debug("Melhor bid obtido para %s: %s", symbol, entry_price)
            balance = self.client.get_balance()
            quote_currency = symbol.split("/")[1]
            available_balance = balance["total"].get(quote_currency, 0)
            if available_balance <= 0:
                self.logger.error(
                    "Saldo insuficiente em %s para %s", quote_currency, symbol
                )
                return None
            # Ajuste dinâmico do tamanho da posição baseado em volatilidade (OTIMIZADO)
            current_atr, atr_mean, volatility_data = self._get_volatility_data(symbol, timeframe, indicator)

            if current_atr is not None and atr_mean is not None:
                position_size = self.position_sizer.calculate_position_size(
                    symbol, volatility_data, current_atr, atr_mean
                )
                self.logger.info(
                    f"Volatilidade para {symbol}: ATR atual={current_atr:.4f}, "
                    f"ATR médio={atr_mean:.4f}, Posição={position_size*100:.1f}%"
                )
            else:
                position_size = getattr(self.client.config, "POSITION_SIZE_BASE", 0.1)
                self.logger.warning(f"Usando tamanho de posição padrão para {symbol}: {position_size*100:.1f}%")

            balance_to_use = available_balance * position_size
            amount = balance_to_use / entry_price
            formatted_amount = self.client.format_amount_with_precision(symbol, amount)
            formatted_price = self.client.format_price_with_precision(
                symbol, entry_price
            )
            if float(formatted_amount) <= 0:
                self.logger.error(
                    "Quantidade calculada insuficiente para %s: %s",
                    symbol,
                    formatted_amount,
                )
                return None
            self.logger.debug(
                "Calculado para %s - Quantidade: %s, Preço: %s",
                symbol,
                formatted_amount,
                formatted_price,
            )

            limit_order = self.client.exchange.create_order(
                symbol=symbol,
                type="limit",
                side="buy",
                amount=float(formatted_amount),
                price=float(formatted_price),
                params={"tdMode": "cash", "clOrdId": self._generate_client_order_id()},
            )
            if "amount" not in limit_order or limit_order["amount"] is None:
                limit_order["amount"] = float(formatted_amount)
            self.logger.info(
                "Ordem limite criada com sucesso: %s", limit_order.get("id")
            )

            # Aguardar execução da ordem limite
            executed_order = await self.wait_for_order_execution(
                limit_order["id"], symbol
            )
            if not executed_order:
                self.logger.error(
                    f"Ordem de compra {limit_order['id']} não foi executada."
                )
                return None

            entry_price_real = (
                executed_order["average"]
                if "average" in executed_order
                else executed_order["price"]
            )

            # Calcular callback ratio otimizado usando dados já em cache
            if current_atr is None:
                # Fallback se não conseguimos obter ATR anteriormente
                current_atr = self._calculate_atr_with_cache(symbol, timeframe, 14, indicator)
                if current_atr is None:
                    self.logger.error("Não foi possível calcular ATR para %s", symbol)
                    return None

            # Calcular callback ratio base
            atr_multiplier = getattr(self.client.config, "ATR_MULTIPLIER", 1.75)
            callback_ratio_base = (atr_multiplier * current_atr) / entry_price_real

            # Ajuste dinâmico do callback_ratio baseado em volatilidade (OTIMIZADO)
            if (getattr(self.client.config, "ENABLE_DYNAMIC_TRAILING_RATIO", False) and
                atr_mean is not None):

                volatility_ratio = current_atr / atr_mean if atr_mean > 0 else 1.0

                if volatility_ratio > getattr(self.client.config, "VOLATILITY_THRESHOLD_HIGH", 1.2):
                    callback_ratio = max(
                        getattr(self.client.config, "CALLBACK_RATIO_MAX", 0.08),
                        callback_ratio_base,
                    )
                    self.logger.info(
                        f"Alta volatilidade para {symbol} (ratio={volatility_ratio:.2f}), "
                        f"callback ratio={callback_ratio*100:.2f}%"
                    )
                elif volatility_ratio < getattr(self.client.config, "VOLATILITY_THRESHOLD_LOW", 0.8):
                    callback_ratio = min(
                        getattr(self.client.config, "CALLBACK_RATIO_MIN", 0.015),
                        callback_ratio_base,
                    )
                    self.logger.info(
                        f"Baixa volatilidade para {symbol} (ratio={volatility_ratio:.2f}), "
                        f"callback ratio={callback_ratio*100:.2f}%"
                    )
                else:
                    callback_ratio = max(
                        getattr(self.client.config, "CALLBACK_RATIO_BASE", 0.03),
                        min(callback_ratio_base, getattr(self.client.config, "CALLBACK_RATIO_MAX", 0.08)),
                    )
                    self.logger.info(
                        f"Volatilidade normal para {symbol} (ratio={volatility_ratio:.2f}), "
                        f"callback ratio={callback_ratio*100:.2f}%"
                    )
            else:
                callback_ratio = max(0.015, min(0.08, callback_ratio_base))
                self.logger.info(f"Callback ratio padrão para {symbol}: {callback_ratio*100:.2f}%")

            trailing_params = {
                "instId": self.client.exchange.markets[symbol]["id"],
                "tdMode": "cash",
                "side": "sell",
                "ordType": "move_order_stop",
                "sz": formatted_amount,
                "callbackRatio": str(callback_ratio),
                "activePx": "",
                "clOrdId": self._generate_client_order_id(),
            }

            trailing_order = self.client.exchange.private_post_trade_order_algo(
                trailing_params
            )

            if trailing_order and trailing_order.get("code") == "0":
                algo_id = trailing_order.get("data", [{}])[0].get("algoId", "N/A")
                self.logger.info("Trailing stop criado com sucesso: %s", algo_id)
                # Salvar o trailing stop no banco de dados
                trailing_stop_data = {
                    "algo_id": algo_id,
                    "order_id": limit_order.get("id"),
                    "symbol": symbol,
                    "callback_ratio": callback_ratio,
                    "status": "active",
                }

                bot = TradingBot()  # Instância do bot para acessar o banco de dados
                bot.db.save_trailing_stop(trailing_stop_data)
            else:
                self.logger.error("Falha ao criar trailing stop: %s", trailing_order)

            result = {
                "limit_order": limit_order,
                "entry_price": entry_price_real,
                "amount": float(formatted_amount),
                "symbol": symbol,
            }
            self.logger.info("Ordem com Trailing Stop Optuna processada com sucesso para %s", symbol)
            return result

        except Exception as exc:
            self.logger.error("Erro ao colocar ordem para %s: %s", symbol, str(exc))
            return None

    def log_order_prevention_details(self, symbol: str) -> None:
        try:
            trailing_orders = check_trailing_stop_orders(self.client, symbol)
            if trailing_orders:
                self.logger.info("=== PREVENÇÃO DE ORDEM ATIVADA ===")
                self.logger.info("Símbolo: %s", symbol)
                self.logger.info("Trailing stops ativos: %d", len(trailing_orders))
                for i, order in enumerate(trailing_orders, 1):
                    self.logger.info("Trailing Stop #%d:", i)
                    self.logger.info("  - ID: %s", order.get("id", "N/A"))
                    self.logger.info("  - Status: %s", order.get("status", "N/A"))
                    self.logger.info("  - Lado: %s", order.get("side", "N/A"))
                    self.logger.info("  - Quantidade: %s", order.get("amount", "N/A"))
                    self.logger.info(
                        "  - Trigger: %s", order.get("triggerPrice", "N/A")
                    )
                self.logger.info("Nova ordem BLOQUEADA para %s", symbol)
                self.logger.info("=====================================")
        except Exception as exc:
            self.logger.error("Erro ao registrar detalhes de prevenção: %s", str(exc))


class BulkDataFetcher:
    """Classe para busca em lote de dados OHLCV."""

    def __init__(self, indicator: TechnicalIndicator):
        self.indicator = indicator
        self.logger = TradingLogger.get_logger(__name__)

    async def fetch_bulk_ohlcv(self, symbols: List[str], timeframe: str, limit: int = None) -> Dict[str, Optional[np.ndarray]]:
        """
        Busca dados OHLCV para múltiplos símbolos em paralelo.

        Args:
            symbols: Lista de símbolos para buscar dados
            timeframe: Timeframe dos dados
            limit: Limite de candles

        Returns:
            Dicionário com dados OHLCV para cada símbolo
        """
        async def fetch_single_symbol(symbol: str) -> Tuple[str, Optional[np.ndarray]]:
            try:
                data = self.indicator.fetch_historical_data(symbol, timeframe, limit)
                return symbol, np.array(data) if data is not None else None
            except Exception as e:
                self.logger.error(f"Erro ao buscar dados para {symbol}: {e}")
                return symbol, None

        # Usar asyncio.gather para busca paralela
        tasks = [fetch_single_symbol(symbol) for symbol in symbols]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Processar resultados
        bulk_data = {}
        for result in results:
            if isinstance(result, Exception):
                self.logger.error(f"Erro na busca em lote: {result}")
                continue
            symbol, data = result
            bulk_data[symbol] = data

        self.logger.info(f"Busca em lote concluída para {len(bulk_data)} símbolos")
        return bulk_data


class TrailingTradingBot(TradingBotBase):
    """Bot de trading específico para estratégia Trailing Stop WMA."""

    async def _execute_strategy(self, client: OKXClient, indicator: TechnicalIndicator, 
                               signal_checker: SinaisWmaRsi, order_manager: OKXOrder, strategy_type: str) -> None:
        """
        Implementação da lógica de trading específica para estratégia Trailing Stop WMA.
        
        Args:
            client: Instance of OKXClient for exchange interactions.
            indicator: TechnicalIndicator instance for calculating indicators.
            signal_checker: SinaisWmaRsi instance for checking entry signals.
            order_manager: OKXOrder instance for managing order placement.
            strategy_type: Type of strategy for notifications.
        """
        print("\n🤖 Verificando Sinais de Trading (OTIMIZADO):")
        print("=" * 50)

        # Initialize database for order tracking
        db = TradingDatabase("trades/sandbox_trades.db")

        # Inicializar fetcher para busca em lote
        bulk_fetcher = BulkDataFetcher(indicator)

        # Pré-buscar dados para todos os símbolos em paralelo
        self.logger.info("Iniciando busca em lote de dados OHLCV...")
        bulk_ohlcv_data = await bulk_fetcher.fetch_bulk_ohlcv(
            client.config.TRADING_SYMBOLS,
            client.config.TIMEFRAME,
            limit=getattr(client.config, "ATR_LOOKBACK_PERIODS", 20) + 14
        )

        # Usar semáforo para controlar concorrência
        semaphore = asyncio.Semaphore(3)  # Máximo 3 operações simultâneas

        async def process_symbol(symbol: str) -> None:
            async with semaphore:
                try:
                    # Check for closed trailing stop orders and update associations
                    closed_orders = check_closed_trailing_stop_orders(client, symbol, db)
                    if closed_orders:
                        print(f"🔄 Processed {len(closed_orders)} closed trailing stop orders for {symbol}")

                    # Verificar se há dados OHLCV disponíveis
                    if symbol not in bulk_ohlcv_data or bulk_ohlcv_data[symbol] is None:
                        self.logger.warning(f"Dados OHLCV não disponíveis para {symbol}")
                        return

                    has_buy_signal = signal_checker.check_entry_signal(symbol, timeframe=None)
                    signal_checker.print_signal(symbol, timeframe=None)

                    if order_manager.has_active_trailing_stop(symbol):
                        return

                    if has_buy_signal:
                        self.logger.info(f"\n🚨 SINAL DE COMPRA DETECTADO PARA {symbol}!")
                        if order_manager.validate_order_conditions(symbol):
                            self.logger.info(f"✅ Condições validadas para {symbol}")
                            order_result = await order_manager.place_buy_order_with_trailing_stop(
                                symbol=symbol, indicator=indicator
                            )
                            if order_result:
                                print(f"🎯 Trail Order - ORDEM EXECUTADA COM SUCESSO!")
                                print(f"• Símbolo: {symbol}")
                                print(f"• Quantidade: {order_result['amount']}")
                                print(f"• Preço: ${order_result['entry_price']}")
                                print(f"• ID da ordem: {order_result['limit_order'].get('id', 'N/A')}")

                                self.save_order_to_db(order_result["limit_order"])
                                self.created_orders.append(order_result["limit_order"])
                                await self.play_alert("success", volume=0.7)

                                message = f"""🎯 *ORDEM TRAILING STOP OPTUNA EXECUTADA*
• *Bot*: {self.config.SCRIPT_NAME}
• *Símbolo*: {symbol}
• *Tipo*: Trailing Stop (Limit) Optimizada
• *Quantidade*: {order_result['amount']}
• *Preço*: ${order_result['entry_price']}
"""
                                await self.send_telegram_notification(message)
                            else:
                                self.logger.error(f"❌ Falha ao executar ordem para {symbol}")
                                await self.play_alert("error", volume=0.5)
                        else:
                            self.logger.warning(f"⚠️ Condições não atendidas para {symbol}")
                    else:
                        self.logger.info(f"Trail | {symbol} -> Wait")
                except Exception as exc:
                    self.logger.error("Erro ao processar %s: %s", symbol, str(exc))
                    print(f"❌ Erro ao processar {symbol}: {exc}")

        # Processar todos os símbolos em paralelo com controle de concorrência
        tasks = [process_symbol(symbol) for symbol in client.config.TRADING_SYMBOLS]
        await asyncio.gather(*tasks, return_exceptions=True)

        # Limpar caches expirados
        order_manager.indicator_cache.clear_expired()
        self.logger.info("Ciclo de trading otimizado concluído")


class TradingBot(TrailingTradingBot):
    """Classe para compatibilidade com chamadas internas que esperam TradingBot."""
    pass


async def optimize_parameters(client: OKXClient, indicator: TechnicalIndicator, timeframe: str = "15m") -> Dict[str, Any]:
    """Função para otimizar parâmetros da estratégia usando Optuna."""
    logger = TradingLogger.get_logger(__name__)
    logger.info("Iniciando otimização de parâmetros com Optuna...")

    def objective(trial: optuna.Trial) -> float:
        # Definir parâmetros a otimizar
        wma_period = trial.suggest_int("wma_period", 30, 80)
        rsi_low = trial.suggest_int("rsi_low", 20, 50)
        rsi_high = trial.suggest_int("rsi_high", 50, 80)
        atr_multiplier = trial.suggest_float("atr_multiplier", 2.0, 4.0)
        volatility_threshold_high = trial.suggest_float("volatility_threshold_high", 1.1, 1.5)
        volatility_threshold_low = trial.suggest_float("volatility_threshold_low", 0.5, 0.9)
        position_size_min = trial.suggest_float("position_size_min", 0.03, 0.07)
        position_size_base = trial.suggest_float("position_size_base", 0.08, 0.12)
        position_size_max = trial.suggest_float("position_size_max", 0.13, 0.2)

        # Configurar parâmetros no cliente (temporariamente para simulação)
        client.config.WMA_PERIOD = wma_period
        client.config.RSI_LOW = rsi_low
        client.config.RSI_HIGH = rsi_high
        client.config.ATR_MULTIPLIER = atr_multiplier
        client.config.VOLATILITY_THRESHOLD_HIGH = volatility_threshold_high
        client.config.VOLATILITY_THRESHOLD_LOW = volatility_threshold_low
        client.config.POSITION_SIZE_MIN = position_size_min
        client.config.POSITION_SIZE_BASE = position_size_base
        client.config.POSITION_SIZE_MAX = position_size_max

        # Simulação/backtest para calcular desempenho
        total_return = 0.0
        for symbol in client.config.TRADING_SYMBOLS:
            ohlcv_data = indicator.fetch_historical_data(symbol, timeframe)
            if ohlcv_data is None or len(ohlcv_data) < wma_period + 14:
                continue

            df = pd.DataFrame(ohlcv_data, columns=["timestamp", "open", "high", "low", "close", "volume"])
            df["wma"] = talib.WMA(df["close"], timeperiod=wma_period)
            df["rsi"] = talib.RSI(df["close"], timeperiod=14)
            df["atr"] = talib.ATR(df["high"], df["low"], df["close"], timeperiod=14)

            # Lógica simplificada de entrada e saída para backtest
            position = 0
            entry_price = 0.0
            returns = []
            for i in range(1, len(df)):
                if position == 0 and df["rsi"].iloc[i] < rsi_low and df["close"].iloc[i] > df["wma"].iloc[i]:
                    position = 1
                    entry_price = df["close"].iloc[i]
                elif position == 1:
                    callback_ratio = atr_multiplier * df["atr"].iloc[i] / entry_price
                    stop_price = entry_price * (1 + callback_ratio)
                    if df["high"].iloc[i] >= stop_price:
                        exit_price = stop_price
                        returns.append((exit_price - entry_price) / entry_price)
                        position = 0
                    elif df["rsi"].iloc[i] > rsi_high:
                        exit_price = df["close"].iloc[i]
                        returns.append((exit_price - entry_price) / entry_price)
                        position = 0

            if returns:
                total_return += np.mean(returns) * len(returns)

        # Retornar métrica de desempenho (retorno total ajustado)
        return total_return if total_return > 0 else -1.0

    # Criar estudo Optuna
    study = optuna.create_study(direction="maximize", storage="sqlite:///optuna_studies.db", study_name="trail_wma_optimization", load_if_exists=True)
    study.optimize(objective, n_trials=300, timeout=3600)  # 1 hora de timeout

    # Obter melhores parâmetros
    best_params = study.best_params
    logger.info("Otimização concluída. Melhores parâmetros encontrados: %s", best_params)

    return best_params

async def main() -> None:
    """Função principal com auto-trading implementado para estratégia Trailing Stop WMA com otimização Optuna."""
    logger = TradingLogger.get_logger(__name__)

    try:
        config = BotConfig(strategy="signals_wma_rsi")
        config.SCRIPT_NAME = "Trail WMA Optuna"
        client = OKXClient(config)
        # Ensure TRADING_SYMBOLS from config are used in client
        client.config.TRADING_SYMBOLS = config.TRADING_SYMBOLS
        order_manager = OKXOrder(client)  # Inicializar order_manager específico para Trailing Stop
        bot = TradingBot(config)
        await send_startup_notification(client, bot)
        await bot.play_alert("start", volume=0.25)

        indicator = TechnicalIndicator(client)

        # Inicializar dados históricos
        for symbol in client.config.TRADING_SYMBOLS:
            indicator.fetch_historical_data(symbol, client.config.TIMEFRAME)

        # Otimizar parâmetros usando Optuna
        best_params = await optimize_parameters(client, indicator, client.config.TIMEFRAME)
        # Aplicar os melhores parâmetros à configuração
        client.config.WMA_PERIOD = best_params.get("wma_period", 50)
        client.config.RSI_LOW = best_params.get("rsi_low", 30)
        client.config.RSI_HIGH = best_params.get("rsi_high", 70)
        client.config.ATR_MULTIPLIER = best_params.get("atr_multiplier", 1.75)
        client.config.VOLATILITY_THRESHOLD_HIGH = best_params.get("volatility_threshold_high", 1.2)
        client.config.VOLATILITY_THRESHOLD_LOW = best_params.get("volatility_threshold_low", 0.8)
        client.config.POSITION_SIZE_MIN = best_params.get("position_size_min", 0.05)
        client.config.POSITION_SIZE_BASE = best_params.get("position_size_base", 0.1)
        client.config.POSITION_SIZE_MAX = best_params.get("position_size_max", 0.15)
        logger.info("Parâmetros otimizados aplicados à configuração do bot.")

        signal_checker = SinaisWmaRsi(indicator, client)
        await bot.run_trading_loop(client, indicator, signal_checker, order_manager, "TRAIL")

    except KeyboardInterrupt:
        logger.info("Bot interrompido manualmente pelo usuário.")
        print("\n🛑 Bot interrompido manualmente.")
        if "bot" in locals():
            await bot.play_alert("exit", volume=0.9)
        print("A finalizar o bot. Até breve!")
        return

    except (ConfigurationError, ExchangeConnectionError) as exc:
        logger.error("Erro de configuração/conexão: %s", exc)
        print(f"❌ Erro: {exc}")
        print("Por favor, verifique as suas credenciais e configuração.")

    except Exception as exc:
        logger.error("Erro inesperado: %s", exc)
        logger.exception(exc)
        print(f"❌ Erro inesperado: {exc}")
        print("Verifique os logs para mais detalhes.")


if __name__ == "__main__":
    asyncio.run(main())
