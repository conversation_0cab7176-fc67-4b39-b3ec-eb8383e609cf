import os
import shutil
import argparse
from datetime import datetime
import sys
from dataclasses import dataclass
import time


@dataclass
class ColorConfig:
    """Configuração de cores para saída no terminal.

    Atributos:
        GREEN (str): Código ANSI para cor verde.
        RED (str): Código ANSI para cor vermelha.
        BLUE (str): Código ANSI para cor azul.
        YELLOW (str): Código ANSI para cor amarela.
        RESET (str): Código ANSI para resetar a cor.
    """

    GREEN: str = "\033[92m"
    RED: str = "\033[91m"
    BLUE: str = "\033[94m"
    YELLOW: str = "\033[93m"
    RESET: str = "\033[0m"


# Instância global usada no script
Colors = ColorConfig()


def print_colored(message, color=Colors.RESET, end="\n"):
    """Imprime mensagens no terminal com cores específicas.

    Args:
        message (str): A mensagem a ser impressa.
        color (str): O código de cor ANSI a ser usado. Padrão é RESET (sem cor).
        end (str): Caractere de fim de linha. Padrão é nova linha.
    """
    print(f"{color}{message}{Colors.RESET}", end=end)


def get_size(path):
    """Calcula o tamanho total de um diretório em bytes.

    Args:
        path (str): Caminho do diretório ou arquivo para calcular o tamanho.

    Returns:
        int: Tamanho total em bytes.
    """
    total_size = 0
    for dirpath, _, filenames in os.walk(path):
        for f in filenames:
            fp = os.path.join(dirpath, f)
            if not os.path.islink(fp):  # Ignora links simbólicos
                try:
                    total_size += os.path.getsize(fp)
                except OSError:
                    pass
    return total_size


def format_size(size_bytes):
    """Formata o tamanho em bytes para uma unidade mais legível.

    Args:
        size_bytes (float): Tamanho em bytes a ser formatado.

    Returns:
        str: Tamanho formatado com a unidade apropriada (B, KB, MB, GB, TB).
    """
    for unit in ["B", "KB", "MB", "GB"]:
        if size_bytes < 1024.0:
            return f"{size_bytes:.2f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.2f} TB"


def limpar_cache_python(
    diretorio, dry_run=False, verbose=True, incluir_pyc=True, log_file=None
):
    """
    Percorre recursivamente o diretório e remove os arquivos de cache do Python.

    Args:
        diretorio (str): Caminho do diretório para iniciar a limpeza
        dry_run (bool): Se True, apenas simula a remoção sem excluir os arquivos
        verbose (bool): Se True, exibe mensagens detalhadas
        incluir_pyc (bool): Se True, também remove arquivos .pyc soltos
        log_file (str): Caminho para o arquivo de log (opcional)
    """
    if not os.path.exists(diretorio):
        print_colored(
            f"[ERRO] O diretório especificado não existe: {diretorio}", Colors.RED
        )
        return 0, 0

    # Inicializa contadores e logs
    total_removido = 0
    espaco_liberado = 0
    log_entries = []

    # Timestamp para o log
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entries.append(f"=== Limpeza iniciada em {timestamp} ===")
    log_entries.append(f"Diretório: {os.path.abspath(diretorio)}")

    modo = "SIMULAÇÃO" if dry_run else "REMOÇÃO"
    if verbose:
        print_colored(f"\n[INFO] Modo: {modo}", Colors.BLUE)
        if dry_run:
            print_colored(
                "[INFO] Nenhum arquivo será excluído (dry-run)", Colors.YELLOW
            )

    # Procurar e remover diretórios __pycache__
    for root, dirs, files in os.walk(diretorio):
        # Procurar __pycache__
        if "__pycache__" in dirs:
            caminho_pycache = os.path.join(root, "__pycache__")
            try:
                # Calcular tamanho antes de remover
                tamanho = get_size(caminho_pycache)
                espaco_liberado += tamanho

                if not dry_run:
                    shutil.rmtree(caminho_pycache)

                total_removido += 1
                if verbose:
                    msg = f"[{modo}] {caminho_pycache} ({format_size(tamanho)})"
                    print_colored(msg, Colors.GREEN)
                    log_entries.append(msg)
            except PermissionError:
                msg = f"[ERRO] Permissão negada para {caminho_pycache}"
                print_colored(msg, Colors.RED)
                log_entries.append(msg)
            except Exception as e:
                msg = f"[ERRO] Falha ao processar {caminho_pycache}: {str(e)}"
                print_colored(msg, Colors.RED)
                log_entries.append(msg)

        # Procurar arquivos .pyc soltos (se solicitado)
        if incluir_pyc:
            for file in files:
                if file.endswith(".pyc"):
                    pyc_path = os.path.join(root, file)
                    try:
                        tamanho = os.path.getsize(pyc_path)
                        espaco_liberado += tamanho

                        if not dry_run:
                            os.remove(pyc_path)

                        if verbose:
                            msg = f"[{modo}] {pyc_path} ({format_size(tamanho)})"
                            print_colored(msg, Colors.GREEN)
                            log_entries.append(msg)
                    except Exception as e:
                        msg = f"[ERRO] Falha ao remover {pyc_path}: {str(e)}"
                        print_colored(msg, Colors.RED)
                        log_entries.append(msg)

    # Gravar log em arquivo se especificado
    if log_file:
        try:
            with open(log_file, "a", encoding="utf-8") as f:
                for entry in log_entries:
                    f.write(f"{entry}\n")
                f.write(f"Total de diretórios/arquivos processados: {total_removido}\n")
                f.write(f"Espaço liberado: {format_size(espaco_liberado)}\n")
                f.write(
                    f"=== Limpeza concluída em {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===\n\n"
                )
        except Exception as e:
            print_colored(f"[ERRO] Falha ao gravar log: {str(e)}", Colors.RED)

    return total_removido, espaco_liberado


def main():
    """Função principal para executar a limpeza de cache do Python.

    Configura os argumentos da linha de comando, executa a limpeza de cache
    e exibe um resumo dos resultados. Também implementa um loop de espera
    para execuções periódicas.
    """
    # Configurar parser de argumentos
    parser = argparse.ArgumentParser(
        description="Ferramenta para limpar arquivos de cache do Python (__pycache__ e .pyc)",
        epilog="Exemplo: python pycleaner.py -d /caminho/do/projeto --no-color",
    )

    parser.add_argument(
        "-d",
        "--dir",
        default=os.getcwd(),
        help="Diretório para iniciar a limpeza (padrão: diretório atual)",
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Executar em modo simulação sem excluir arquivos",
    )
    parser.add_argument(
        "-v", "--verbose", action="store_true", help="Mostrar informações detalhadas"
    )
    parser.add_argument(
        "-q",
        "--quiet",
        action="store_true",
        help="Modo silencioso, mostrar apenas resumo",
    )
    parser.add_argument(
        "--no-pyc", action="store_true", help="Não remover arquivos .pyc soltos"
    )
    parser.add_argument(
        "--no-color", action="store_true", help="Desativar saída colorida"
    )
    parser.add_argument("--log", metavar="ARQUIVO", help="Salvar log em arquivo")

    args = parser.parse_args()

    # Desativar cores se solicitado ou se não houver suporte a cores
    if args.no_color or not sys.stdout.isatty():
        Colors.GREEN = Colors.RED = Colors.BLUE = Colors.YELLOW = Colors.RESET = ""

    # Executar limpeza
    print_colored(
        f"🧹 Limpando cache Python em: {os.path.abspath(args.dir)}", Colors.BLUE
    )

    start_time = datetime.now()
    total, espaco = limpar_cache_python(
        args.dir,
        dry_run=args.dry_run,
        verbose=(args.verbose and not args.quiet),
        incluir_pyc=(not args.no_pyc),
        log_file=args.log,
    )

    # Exibir resumo
    if not args.quiet or total > 0:
        tempo_execucao = (datetime.now() - start_time).total_seconds()
        print_colored("\n=== RESUMO DA LIMPEZA ===", Colors.BLUE)
        print_colored(f"📂 Diretórios/arquivos processados: {total}", Colors.BLUE)
        print_colored(f"💾 Espaço liberado: {format_size(espaco)}", Colors.BLUE)
        print_colored(
            f"⏱️ Tempo de execução: {tempo_execucao:.2f} segundos", Colors.BLUE
        )

        if args.dry_run:
            print_colored(
                "\n⚠️ Esta foi apenas uma simulação. Execute sem --dry-run para remover os arquivos.",
                Colors.YELLOW,
            )


if __name__ == "__main__":
    try:
        main()
        print_colored("\n💡 LIMPEZA CONCLUÍDA COM SUCESSO! 💡", Colors.GREEN)
        print_colored("⏱️ Aguardando 30 minutos para nova execução...\n", Colors.YELLOW)
        time.sleep(1800)  # 1800 segundos = 30 minutos
    except KeyboardInterrupt:
        print_colored("\n\n⚠️ Operação cancelada pelo usuário", Colors.YELLOW)
        sys.exit(1)
    except Exception as e:
        print_colored(f"\n❌ ERRO: {str(e)}", Colors.RED)
        sys.exit(1)
