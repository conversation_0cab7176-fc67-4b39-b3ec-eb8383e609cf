"""
Bot de trading automatizado para OKX Exchange utilizando estratégia OCO com WMA.
"""

import warnings

warnings.filterwarnings("ignore", message="pkg_resources is deprecated as an API")

import asyncio
import os
import sys
import numpy as np
import json
import ccxt
import talib
import pandas as pd
import uuid
import time
from decimal import Decimal, ROUND_HALF_UP
from dotenv import load_dotenv
from typing import Dict, List, Optional, Tuple, Any

# Importações dos módulos compartilhados
from core.config import BotConfig, OrderCache
from core.base_bot import (
    TradingBotBase,
    initialize_bot_components,
    send_startup_notification,
)
from core.okx_client import OKXClient
from indicators.indicators import TechnicalIndicator
from utils.logger import TradingLogger
from utils.check_orders import check_oco_orders
from utils.order_validator import OrderValidator
from utils.exceptions import ConfigurationError, ExchangeConnectionError
from utils.common import load_parameters
from utils.check_orders import check_closed_oco_orders
from utils.database import TradingDatabase
from signals.signal_wma_rsi import SinaisWmaRsi

load_dotenv()


class OKXOrder:
    """Classe para gerenciar operações de ordens na exchange OKX com estratégia OCO."""

    def __init__(self, client: OKXClient):
        self.client = client
        self.logger = TradingLogger.get_logger(__name__)
        self.validator = OrderValidator(client)
        # Garantir que o cache de ordens esteja inicializado no cliente
        if not hasattr(self.client, "order_cache"):
            self.client.order_cache = OrderCache(cache_duration=30)

    def validate_order_conditions(self, symbol: str) -> bool:
        """
        Valida se as condições estão adequadas para colocar uma ordem.
        Inclui verificação de ordens tp/sl (oco).

        Args:
            symbol: Par de trading

        Returns:
            True se condições estão OK, False caso contrário
        """
        return self.validator.validate_order_conditions(symbol, strategy_type="oco")

    def _generate_client_order_id(self) -> str:
        """Gera um ID único para a ordem."""
        return uuid.uuid4().hex[:32]

    def has_active_oco_orders(self, symbol: str) -> bool:
        """
        Verifica especificamente se há ordens tp/sl (oco) ativas.

        Args:
            symbol: Par de trading

        Returns:
            True se há ordens OCO ativas, False caso contrário
        """
        try:
            # Verificar primeiro no cache
            open_orders = self.client.order_cache.get_open_orders(symbol)
            if open_orders is not None:
                oco_orders = [
                    order
                    for order in open_orders
                    if order.get("info", {}).get("oco_details")
                ]
                active_count = len(oco_orders)
                if active_count > 0:
                    return True
                return False

            # Se não estiver no cache, buscar da exchange
            open_orders = check_oco_orders(self.client, symbol)
            active_count = len(open_orders)

            if active_count > 0:
                for order in open_orders:
                    order_id = order.get("id", "N/A")
                    status = order.get("status", "N/A")
                    self.logger.debug(
                        "Ordem OCO ativa: ID=%s, Status=%s", order_id, status
                    )
                return True

            return False
        except Exception as exc:
            self.logger.debug("Erro ao buscar dados para %s: %s", symbol, str(exc))
            self.logger.info("O símbolo %s não existe na exchange.", symbol)
            return (
                True  # Em caso de erro, assume que há ordens ativas para ser cauteloso
            )

    def place_buy_order_with_oco(
        self, symbol: str, indicator: TechnicalIndicator, timeframe: str = "15m"
    ) -> Optional[Dict]:
        """
        Coloca ordem de compra com OCO (One Cancels the Other) usando 10% do saldo.
        O Stop Loss (SL) é definido como 1.75x o ATR abaixo do preço de entrada.
        O Take Profit (TP) é definido como 1.75x o ATR acima do preço de entrada.
        Verifica tendência antes de entrar e implementa timeouts inteligentes.
        """
        try:
            # Verificar se há ordens existentes com timeout de 5 minutos
            existing_orders = check_oco_orders(self.client, symbol)
            if existing_orders:
                # Verificar se alguma ordem está pendente há mais de 5 minutos
                current_time = time.time()
                for order in existing_orders:
                    order_time = order.get("timestamp", 0)
                    if current_time - order_time > 300:  # 5 minutos
                        self.logger.info(
                            f"Ordem pendente há mais de 5 minutos para {symbol}, cancelando..."
                        )
                        try:
                            self.client.exchange.cancel_order(order["id"], symbol)
                        except Exception as e:
                            self.logger.error(f"Erro ao cancelar ordem: {e}")
                return None

            # Verificar tendência usando WMA e RSI
            wma = indicator.calculate_wma(symbol, period=self.client.config.WMA_PERIOD)
            rsi = indicator.calculate_rsi(symbol, period=self.client.config.RSI_PERIOD)

            # Corrigir: garantir que wma e rsi são listas
            if isinstance(wma, float):
                wma = [wma]
            if isinstance(rsi, float):
                rsi = [rsi]

            if (
                wma is None
                or rsi is None
                or not isinstance(wma, list)
                or not isinstance(rsi, list)
            ):
                self.logger.error(
                    f"Não foi possível calcular indicadores para {symbol}"
                )
                return None

            # Apenas entrar em tendência de alta (WMA ascendente e RSI > 50)
            try:
                ticker = self.client.exchange.fetch_ticker(symbol)
                current_close = ticker.get("last", 0.0)
                if current_close <= 0.0:
                    self.logger.error(f"Preço atual inválido obtido para {symbol}")
                    return None
            except Exception as e:
                self.logger.error(f"Erro ao obter preço atual para {symbol}: {str(e)}")
                return None
            if current_close < wma[-1] or rsi[-1] < 50:
                self.logger.info(
                    f"Tendência não favorável para {symbol} (Preço: {current_close}, WMA: {wma[-1]}, RSI: {rsi[-1]})"
                )
                return None
            best_bid = self.client.get_best_bid(symbol)
            if not best_bid or not isinstance(best_bid, (int, float)) or best_bid <= 0:
                self.logger.error("Melhor bid inválido ou não obtido para %s", symbol)
                return None
            entry_price = float(best_bid)
            self.logger.debug("Melhor bid obtido para %s: %s", symbol, entry_price)
            balance = self.client.get_balance()
            quote_currency = symbol.split("/")[1]
            available_balance = balance["total"].get(quote_currency, 0)
            if available_balance <= 0:
                self.logger.error(
                    "Saldo insuficiente em %s para %s", quote_currency, symbol
                )
                return None
            # Cálculo avançado de risco baseado em múltiplos fatores
            position_size = 0.1  # Valor padrão
            max_risk_per_trade = 0.02  # 2% do capital por trade
            min_position_size = 0.01  # 1% mínimo absoluto

            if (
                hasattr(self.client.config, "ENABLE_DYNAMIC_POSITION_SIZING")
                and self.client.config.ENABLE_DYNAMIC_POSITION_SIZING
            ):
                # Calcular múltiplas medidas de volatilidade
                atr = indicator.calculate_atr(symbol, timeframe, period=14)
                if atr is not None:
                    # Obter dados históricos para análise
                    lookback_periods = getattr(
                        self.client.config, "ATR_LOOKBACK_PERIODS", 100
                    )
                    ohlcv_data = indicator.fetch_historical_data(
                        symbol, timeframe, limit=lookback_periods
                    )

                    if ohlcv_data and len(ohlcv_data) > 1:
                        closes = np.array([x[4] for x in ohlcv_data])

                        # 1. Volatilidade histórica (desvio padrão dos retornos)
                        returns = np.diff(closes) / closes[:-1]
                        hist_volatility = np.std(returns) * np.sqrt(365)

                        # 2. Volatilidade baseada em ATR (média móvel)
                        atr_values = talib.ATR(
                            np.array([x[2] for x in ohlcv_data]),
                            np.array([x[3] for x in ohlcv_data]),
                            np.array([x[4] for x in ohlcv_data]),
                            timeperiod=14,
                        )
                        atr_volatility = (
                            np.mean(atr_values[-30:]) / closes[-1]
                            if len(atr_values) >= 30
                            else 0.05
                        )

                        # 3. Volatilidade ponderada (70% histórica + 30% ATR)
                        weighted_vol = 0.7 * hist_volatility + 0.3 * atr_volatility

                        # 4. Fator de correlação com mercado (BTC)
                        btc_corr = 1.0  # Default
                        if symbol != "BTC/USDC":
                            btc_data = indicator.fetch_historical_data(
                                "BTC/USDC", timeframe, limit=lookback_periods
                            )
                            if btc_data and len(btc_data) > 1:
                                btc_closes = np.array([x[4] for x in btc_data])
                                btc_returns = np.diff(btc_closes) / btc_closes[:-1]
                                corr_matrix = np.corrcoef(
                                    returns, btc_returns[: len(returns)]
                                )
                                btc_corr = min(
                                    1.5, max(0.5, 1 + corr_matrix[0, 1])
                                )  # Limit correlation factor

                        # Cálculo final do tamanho da posição
                        risk_adjusted_size = min(
                            (max_risk_per_trade / (weighted_vol * btc_corr + 0.0001)),
                            self.client.config.POSITION_SIZE_MAX,
                        )
                        position_size = max(risk_adjusted_size, min_position_size)

                        self.logger.info(
                            f"Risco calculado para {symbol}:\n"
                            f"- Hist Vol: {hist_volatility:.4f}\n"
                            f"- ATR Vol: {atr_volatility:.4f}\n"
                            f"- Weighted: {weighted_vol:.4f}\n"
                            f"- BTC Corr: {btc_corr:.2f}\n"
                            f"- Pos Size: {position_size:.2%}"
                        )
                    if ohlcv_data and len(ohlcv_data) > 14:
                        atr_values = []
                        for i in range(len(ohlcv_data) - 14, len(ohlcv_data)):
                            high = np.array(
                                [candle[2] for candle in ohlcv_data[i - 14 : i]]
                            )
                            low = np.array(
                                [candle[3] for candle in ohlcv_data[i - 14 : i]]
                            )
                            close = np.array(
                                [candle[4] for candle in ohlcv_data[i - 14 : i]]
                            )
                            atr_val = talib.ATR(high, low, close, timeperiod=14)[-1]
                            atr_values.append(atr_val)
                        atr_mean = np.mean(atr_values) if atr_values else atr
                        volatility_ratio = atr / atr_mean if atr_mean > 0 else 1.0
                        self.logger.info(
                            "Volatilidade para %s: ATR atual=%.2f, ATR médio=%.2f, Razão=%.2f",
                            symbol,
                            atr,
                            atr_mean,
                            volatility_ratio,
                        )

                        high_threshold = getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_HIGH", 1.2
                        )
                        low_threshold = getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_LOW", 0.8
                        )
                        if volatility_ratio > high_threshold:
                            position_size = getattr(
                                self.client.config, "POSITION_SIZE_MIN", 0.05
                            )
                            self.logger.info(
                                "Alta volatilidade detectada para %s, reduzindo tamanho da posição para %.1f%%",
                                symbol,
                                position_size * 100,
                            )
                        elif volatility_ratio < low_threshold:
                            position_size = getattr(
                                self.client.config, "POSITION_SIZE_MAX", 0.15
                            )
                            self.logger.info(
                                "Baixa volatilidade detectada para %s, aumentando tamanho da posição para %.1f%%",
                                symbol,
                                position_size * 100,
                            )
                        else:
                            position_size = getattr(
                                self.client.config, "POSITION_SIZE_BASE", 0.1
                            )
                            self.logger.info(
                                "Volatilidade normal para %s, tamanho da posição padrão %.1f%%",
                                symbol,
                                position_size * 100,
                            )

                        # Notificação de ajuste de volatilidade
                        if (
                            hasattr(self.client.config, "ENABLE_TELEGRAM_NOTIFICATIONS")
                            and self.client.config.ENABLE_TELEGRAM_NOTIFICATIONS
                            and volatility_ratio > high_threshold
                        ):
                            message = f"⚠️ *Alta Volatilidade Detectada - {symbol}*\n• Tamanho da posição reduzido para {position_size * 100:.1f}%\n• Razão ATR: {volatility_ratio:.2f}"
                            asyncio.run_coroutine_threadsafe(
                                bot.send_telegram_notification(message),
                                asyncio.get_event_loop(),
                            )
                    else:
                        self.logger.warning(
                            "Dados históricos insuficientes para calcular média ATR de %s, usando tamanho padrão",
                            symbol,
                        )
                else:
                    self.logger.warning(
                        "Não foi possível calcular ATR para %s, usando tamanho padrão",
                        symbol,
                    )
            else:
                position_size = 0.1  # Padrão fixo se desativado

            balance_to_use = available_balance * position_size
            ten_percent_balance = balance_to_use  # Definindo o valor usado como 10% do saldo ajustado pelo position_size
            amount = balance_to_use / entry_price
            formatted_amount = self.client.format_amount_with_precision(symbol, amount)
            formatted_price = self.client.format_price_with_precision(
                symbol, entry_price
            )
            if float(formatted_amount) <= 0:
                self.logger.error(
                    "Quantidade calculada insuficiente para %s: %s",
                    symbol,
                    formatted_amount,
                )
                return None
            self.logger.info(
                "Calculado para %s - Quantidade: %s, Preço: %s",
                symbol,
                formatted_amount,
                formatted_price,
            )
            # Calcular ATR
            atr = indicator.calculate_atr(symbol, timeframe, period=14)
            if atr is None:
                self.logger.error("Não foi possível calcular ATR para %s", symbol)
                return None
            # Ajuste dinâmico dos multiplicadores de ATR para SL e TP baseado em volatilidade
            atr_sl_factor = getattr(self.client.config, "ATR_SL_FACTOR", 2.5)
            atr_tp_factor = getattr(self.client.config, "ATR_TP_FACTOR", 3.25)
            if (
                hasattr(self.client.config, "ENABLE_DYNAMIC_ATR_MULTIPLIERS")
                and self.client.config.ENABLE_DYNAMIC_ATR_MULTIPLIERS
            ):
                if atr is not None:
                    # Calcular média histórica de ATR para comparação
                    lookback_periods = getattr(
                        self.client.config, "ATR_LOOKBACK_PERIODS", 20
                    )
                    ohlcv_data = indicator.fetch_historical_data(
                        symbol, timeframe, limit=lookback_periods + 14
                    )
                    if ohlcv_data and len(ohlcv_data) > 14:
                        atr_values = []
                        for i in range(len(ohlcv_data) - 14, len(ohlcv_data)):
                            high = np.array(
                                [candle[2] for candle in ohlcv_data[i - 14 : i]]
                            )
                            low = np.array(
                                [candle[3] for candle in ohlcv_data[i - 14 : i]]
                            )
                            close = np.array(
                                [candle[4] for candle in ohlcv_data[i - 14 : i]]
                            )
                            atr_val = talib.ATR(high, low, close, timeperiod=14)[-1]
                            atr_values.append(atr_val)
                        atr_mean = np.mean(atr_values) if atr_values else atr
                        volatility_ratio = atr / atr_mean if atr_mean > 0 else 1.0

                        high_threshold = getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_HIGH", 1.2
                        )
                        low_threshold = getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_LOW", 0.8
                        )
                        if volatility_ratio > high_threshold:
                            atr_sl_factor = getattr(
                                self.client.config, "ATR_SL_FACTOR_MAX", 2.50
                            )
                            atr_tp_factor = getattr(
                                self.client.config, "ATR_TP_FACTOR_MAX", 4.25
                            )
                            self.logger.info(
                                "Alta volatilidade detectada para %s, aumentando multiplicadores SL/TP para %.1f/%.1f",
                                symbol,
                                atr_sl_factor,
                                atr_tp_factor,
                            )
                        elif volatility_ratio < low_threshold:
                            atr_sl_factor = getattr(
                                self.client.config, "ATR_SL_FACTOR_MIN", 1.2
                            )
                            atr_tp_factor = getattr(
                                self.client.config, "ATR_TP_FACTOR_MIN", 1.8
                            )
                            self.logger.info(
                                "Baixa volatilidade detectada para %s, reduzindo multiplicadores SL/TP para %.1f/%.1f",
                                symbol,
                                atr_sl_factor,
                                atr_tp_factor,
                            )
                        else:
                            atr_sl_factor = getattr(
                                self.client.config, "ATR_SL_FACTOR", 1.5
                            )
                            atr_tp_factor = getattr(
                                self.client.config, "ATR_TP_FACTOR", 2.0
                            )
                            self.logger.info(
                                "Volatilidade normal para %s, multiplicadores SL/TP padrão %.1f/%.1f",
                                symbol,
                                atr_sl_factor,
                                atr_tp_factor,
                            )
                    else:
                        self.logger.warning(
                            "Dados históricos insuficientes para calcular média ATR de %s, usando multiplicadores padrão",
                            symbol,
                        )
                else:
                    self.logger.warning(
                        "Não foi possível calcular ATR para %s, usando multiplicadores padrão",
                        symbol,
                    )

            # Calcular Stop Loss (SL) e Take Profit (TP) com base no ATR ajustado
            sl_trigger = entry_price - (atr * atr_sl_factor)
            sl_price = sl_trigger
            tp_trigger = entry_price + (atr * atr_tp_factor)
            tp_price = tp_trigger
            formatted_sl_trigger = self.client.format_price_with_precision(
                symbol, sl_trigger
            )
            formatted_sl_price = self.client.format_price_with_precision(
                symbol, sl_price
            )
            formatted_tp_trigger = self.client.format_price_with_precision(
                symbol, tp_trigger
            )
            formatted_tp_price = self.client.format_price_with_precision(
                symbol, tp_price
            )
            self.logger.debug(
                "Parâmetros OCO calculados para %s: SL Trigger=%s, SL Price=%s, TP Trigger=%s, TP Price=%s (ATR=%.2f)",
                symbol,
                formatted_sl_trigger,
                formatted_sl_price,
                formatted_tp_trigger,
                formatted_tp_price,
                atr,
            )
            # Definir parâmetros da ordem OCO
            order_params = {
                "orderType": "oco",
                "postOnly": False,
                "stopLoss": {
                    "triggerPrice": formatted_sl_trigger,
                    "price": formatted_sl_price,
                },
                "takeProfit": {
                    "triggerPrice": formatted_tp_trigger,
                    "price": formatted_tp_price,
                },
            }
            # Calculate max acceptable slippage (0.2% of entry price)
            max_slippage = entry_price * 0.002
            try:
                ticker = self.client.exchange.fetch_ticker(symbol)
                current_price = ticker.get("last", 0.0)
                if current_price <= 0.0:
                    self.logger.error(f"Preço atual inválido obtido para {symbol}")
                    return None
            except Exception as e:
                self.logger.error(f"Erro ao obter preço atual para {symbol}: {str(e)}")
                return None

            # Verify price hasn't moved beyond acceptable slippage
            if abs(current_price - entry_price) > max_slippage:
                self.logger.warning(
                    f"Price slippage too high for {symbol}: "
                    f"Requested {entry_price}, Current {current_price}, "
                    f"Slippage {abs(current_price - entry_price)/entry_price*100:.2f}%"
                )
                return None

            # Place order with price validation
            try:
                limit_order = self.client.exchange.create_order(
                    symbol=symbol,
                    type="limit",
                    side="buy",
                    amount=(
                        float(formatted_amount) if formatted_amount is not None else 0.0
                    ),
                    price=entry_price,
                    params={
                        **order_params,
                        "priceProtect": True,  # Enable exchange's price protection
                        "timeInForce": "GTC",  # Good Till Cancelled
                    },
                )
            except ccxt.NetworkError as e:
                self.logger.error(f"Network error placing order for {symbol}: {str(e)}")
                return None
            except ccxt.ExchangeError as e:
                self.logger.error(
                    f"Exchange error placing order for {symbol}: {str(e)}"
                )
                return None
            if "amount" not in limit_order or limit_order["amount"] is None:
                limit_order["amount"] = float(formatted_amount)
            self.logger.info(
                "Ordem limite com OCO criada com sucesso: %s", limit_order.get("id")
            )
            # Adicionar informações OCO ao campo additional_info para armazenamento no banco de dados
            oco_info = {
                "oco_details": {
                    "sl_trigger": float(formatted_sl_trigger),
                    "sl_price": float(formatted_sl_price),
                    "tp_trigger": float(formatted_tp_trigger),
                    "tp_price": float(formatted_tp_price),
                }
            }
            if "info" not in limit_order:
                limit_order["info"] = {}
            limit_order["info"].update(oco_info)
            result = {
                "limit_order": limit_order,
                "entry_price": float(formatted_price),
                "amount": float(formatted_amount),
                "symbol": symbol,
                "ten_percent_used": ten_percent_balance,
                "sl_trigger": float(formatted_sl_trigger),
                "sl_price": float(formatted_sl_price),
                "tp_trigger": float(formatted_tp_trigger),
                "tp_price": float(formatted_tp_price),
            }
            self.logger.info("Ordem OCO processada com sucesso para %s", symbol)
            return result
        except Exception as exc:
            self.logger.error("Erro ao colocar ordem OCO para %s: %s", symbol, str(exc))
            return None

    def log_order_prevention_details(self, symbol: str) -> None:
        """
        Registra detalhes sobre prevenção de criação de ordens.

        Args:
            symbol: Par de trading
        """
        try:
            trailing_orders = check_oco_orders(self.client, symbol)

            if trailing_orders:
                self.logger.info("=== PREVENÇÃO DE ORDEM ATIVADA ===")
                self.logger.info("Símbolo: %s", symbol)
                self.logger.info("Trailing stops ativos: %d", len(trailing_orders))

                for i, order in enumerate(trailing_orders, 1):
                    self.logger.info("Trailing Stop #%d:", i)
                    self.logger.info("  - ID: %s", order.get("id", "N/A"))
                    self.logger.info("  - Status: %s", order.get("status", "N/A"))
                    self.logger.info("  - Lado: %s", order.get("side", "N/A"))
                    self.logger.info("  - Quantidade: %s", order.get("amount", "N/A"))
                    self.logger.info(
                        "  - Trigger: %s", order.get("triggerPrice", "N/A")
                    )

                self.logger.info("Nova ordem BLOQUEADA para %s", symbol)
                self.logger.info("=====================================")

        except Exception as exc:
            self.logger.error("Erro ao registrar detalhes de prevenção: %s", str(exc))


class OCOTradingBot(TradingBotBase):
    """Bot de trading específico para estratégia OCO WMA."""

    async def _execute_strategy(
        self,
        client: OKXClient,
        indicator: TechnicalIndicator,
        signal_checker: SinaisWmaRsi,
        order_manager: OKXOrder,
        strategy_type: str,
    ) -> None:
        """
        Implementação da lógica de trading específica para estratégia OCO WMA.

        Args:
            client: Instance of OKXClient for exchange interactions.
            indicator: TechnicalIndicator instance for calculating indicators.
            signal_checker: SinaisWmaRsi instance for checking entry signals.
            order_manager: OKXOrder instance for managing order placement.
            strategy_type: Type of strategy for notifications.
        """
        print("\n🤖 Verificando Sinais de Trading:")
        print("=" * 50)

        # Initialize database for order tracking
        db = TradingDatabase("trades/sandbox_trades.db")

        # Obter tickers de uma vez para todos os símbolos para otimizar
        tickers = client.exchange.fetch_tickers(client.config.TRADING_SYMBOLS)

        for symbol in client.config.TRADING_SYMBOLS:
            try:
                # Check for closed OCO orders and update associations
                closed_orders = check_closed_oco_orders(client, symbol, db)
                if closed_orders:
                    print(
                        f"🔄 Processed {len(closed_orders)} closed OCO orders for {symbol}"
                    )

                # Verificar sinal de entrada
                has_buy_signal = signal_checker.check_entry_signal(
                    symbol, timeframe=None, tickers=tickers
                )

                # Exibir sinal atual
                signal_checker.print_signal(symbol, timeframe=None, tickers=tickers)

                # NOVA LÓGICA: Verificar trailing stops antes de processar sinal
                if order_manager.has_active_oco_orders(symbol):
                    continue  # Pula para o próximo símbolo

                # Se há sinal de compra, processar ordem
                if has_buy_signal:
                    print(f"\n🚨 SINAL DE COMPRA DETECTADO PARA {symbol}!")

                    # Validar condições antes de colocar ordem (já inclui verificação de trailing stop)
                    if order_manager.validate_order_conditions(symbol):
                        print(f"✅ Condições validadas para {symbol}")

                        # Colocar ordem
                        order_result = order_manager.place_buy_order_with_oco(
                            symbol=symbol, indicator=indicator
                        )

                        if order_result:
                            print(f"🎯 ORDEM Stop Loss EXECUTADA COM SUCESSO!")
                            print(f"• Símbolo: {symbol}")
                            print(f"• Quantidade: {order_result['amount']}")
                            print(f"• Preço: ${order_result['entry_price']}")
                            print(
                                f"• Valor usado: ${order_result['ten_percent_used']:.2f}"
                            )
                            print(
                                f"• ID da ordem: {order_result['limit_order'].get('id', 'N/A')}"
                            )

                            # Salvar no banco de dados
                            self.save_order_to_db(order_result["limit_order"])
                            self.created_orders.append(order_result["limit_order"])

                            # Notificações
                            await self.play_alert("success", volume=0.7)

                            message = f"""🎯 *ORDEM Stop Loss EXECUTADA*
        • *Bot*: {self.config.SCRIPT_NAME}
        • *Símbolo*: {symbol}
        • *Tipo*: OCO (Limit)
        • *Quantidade*: {order_result['amount']}
        • *Preço*: ${order_result['entry_price']}
        • *Valor*: ${order_result['ten_percent_used']:.2f}
        """
                            await self.send_telegram_notification(message)

                        else:
                            print(f"❌ Falha ao executar ordem para {symbol}")
                            await self.play_alert("error", volume=0.5)
                    else:
                        pass
                else:
                    self.logger.info(f"OCO | {symbol} -> Wait")
                    print()

            except Exception as exc:
                self.logger.error("Erro ao processar %s: %s", symbol, str(exc))
                print(f"❌ Erro ao processar {symbol}: {exc}")


async def main() -> None:
    """Função principal com auto-trading implementado para estratégia OCO WMA."""
    logger = TradingLogger.get_logger(__name__)

    try:
        config = BotConfig(strategy="OCO_wma_rsi")
        config.SCRIPT_NAME = "999 Base OCO WMA"
        client = OKXClient(config)
        # Ensure TRADING_SYMBOLS from config are used in client
        client.config.TRADING_SYMBOLS = config.TRADING_SYMBOLS
        order_manager = OKXOrder(
            client
        )  # Inicializar order_manager específico para OCO
        bot = OCOTradingBot(config)
        await send_startup_notification(client, bot)
        await bot.play_alert("start", volume=0.25)

        indicator = TechnicalIndicator(client)

        # Inicializar dados históricos
        for symbol in client.config.TRADING_SYMBOLS:
            indicator.fetch_historical_data(symbol, client.config.TIMEFRAME)

        signal_checker = SinaisWmaRsi(indicator, client)
        await bot.run_trading_loop(
            client, indicator, signal_checker, order_manager, "OCO"
        )

    except KeyboardInterrupt:
        logger.info("Bot interrompido manualmente pelo usuário.")
        print("\n🛑 Bot interrompido manualmente.")
        if "bot" in locals():
            await bot.play_alert("exit", volume=0.9)
        print("A finalizar o bot. Até breve!")
        return

    except (ConfigurationError, ExchangeConnectionError) as exc:
        logger.error("Erro de configuração/conexão: %s", exc)
        print(f"❌ Erro: {exc}")
        print("Por favor, verifique as suas credenciais e configuração.")

    except Exception as exc:
        logger.error("Erro inesperado: %s", exc)
        logger.exception(exc)
        print(f"❌ Erro inesperado: {exc}")
        print("Verifique os logs para mais detalhes.")


if __name__ == "__main__":
    asyncio.run(main())
